'use client';
import React from 'react';
 
import Link from 'next/link';
import styles from "./driverConnectionConsentPrompt.module.scss"
interface DriverConnectionConsentPromptProps {
  companyName: string;
  companyLogoUrl?: string;
  onAccept: () => void;
  onCancel: () => void;
  employerName?: string; // optional
}

const DriverConnectionConsentPrompt: React.FC<DriverConnectionConsentPromptProps> = ({
  companyName,
  companyLogoUrl,
  onAccept,
  onCancel,
  employerName,
}) => {
  return (
    <div className={styles.promptWrapper}>
      <div className={styles.promptBox}>
        <h2>Accept Connection Request?</h2>

        {companyLogoUrl && (
          <img
            src={companyLogoUrl}
            alt={`${companyName} Logo`}
            className={styles.logo}
          />
        )}

        <p className={styles.highlight}>
          <strong>{companyName}</strong> would like to connect with you on DriverJobz
          {employerName && <> (Requested by {employerName})</>}.
        </p>

        <h3>What happens if you accept?</h3>
        <ul className={styles.bulletPoints}>
          <li>
            <strong>View Your Detailed Profile:</strong> {companyName} will be able to view your{' '}
            <strong>Full DriverJobz Profile</strong>, including:
            <ul className={styles.subPoints}>
              <li>Your Full Name</li>
              <li>Detailed Work History & Experience</li>
              <li>
                All Licenses, Certifications (with expiry dates, masked numbers, and links to view
                uploaded documents)
              </li>
              <li>Equipment Details (if provided)</li>
              <li>Skills & Languages</li>
              <li>Availability & Work Preferences</li>
              <li>Self-Reported Driving Record Summary</li>
            </ul>
          </li>
          <li>
            <strong>Enable Messaging:</strong> You and {companyName} will be able to message each
            other through DriverJobz.
          </li>
          <li>
            <strong>Contact Info NOT Automatically Shared:</strong> Accepting this request{' '}
            <strong>does NOT</strong> share your phone number or email. That only happens if you
            apply to their job or perform certain actions later.
          </li>
          <li>
            <strong>No Obligation:</strong> You are not obligated to apply or accept any job. You
            can disconnect anytime.
          </li>
        </ul>

        <p className={styles.agreementText}>
          By clicking <strong>&quot;Accept Connection&quot;</strong>, you agree to allow {companyName} access
          to your full profile as described, subject to DriverJobz{' '}
          <Link href="/terms" target="_blank">Terms & Conditions</Link> and{' '}
          <Link href="/privacy" target="_blank">Privacy Policy</Link>.
        </p>

        <div className={styles.buttonGroup}>
          <button className={styles.acceptBtn} onClick={onAccept}>
            Accept Connection
          </button>
          <button className={styles.cancelBtn} onClick={onCancel}>
            Cancel / Decline
          </button>
        </div>
      </div>
    </div>
  );
};

export default DriverConnectionConsentPrompt;
