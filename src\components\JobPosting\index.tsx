"use client";
import React, { useEffect, useState } from "react";
import CompanyWithStepDetails from "./CompanyWithStepDetails";
import styles from "./jobPosting.module.scss";
import BasicForm from "./CDL-Driver/BasicForm";
import PayStructure from "./CDL-Driver/PayStructure";
import TruckDetails from "./CDL-Driver/TruckDetails";
import { JobPostingProps } from "../../types/jobpostingform";
import JobDescription from "./CDL-Driver/JobDescription";
import Qualifications from "./CDL-Driver/Qualifications";

const FormStructure = ({
  lang,
  formFields,
  currentStepNo,
  languages,
  otherLanguages,
  companyDetails,
  states
}: JobPostingProps) => {
  const [currentStep, setCurrentStep] = useState<number>(currentStepNo);

  useEffect(() => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  }, [currentStep]);

  const steps = [
    {
      title: "Position Details",
      subCategory:
        "Enter the basic information about the CDL driver position. Specify required license class in Step 4. Each posting represents one type of position and lasts 30 days.",
      component: (
        <BasicForm
          states={states}
          lang={lang}
          formFields={formFields?.[1]}
          setCurrentStep={setCurrentStep}
          companyDetails={companyDetails}
        />
      ),
    },
    {
      title: "Compensation & Benefits",
      subCategory:
        "Detail the pay structure, potential earnings, and benefits offered for this CDL position. Guaranteed minimum pay is required.",
      component: (
        <PayStructure
          lang={lang}
          formFields={formFields?.[2]}
          setCurrentStep={setCurrentStep}
        />
      ),
    },
    {
      title: "Equipment & Freight",
      subCategory:
        "Describe the truck, trailer (if applicable), technology, and freight involved",
      component: (
        <TruckDetails
          lang={lang}
          formFields={formFields?.[3]}
          setCurrentStep={setCurrentStep}
        />
      ),
    },
    {
      title: "Requirements & Qualifications",
      subCategory:
        "Specify the necessary license, experience, driving record, and other qualifications.",
      component: (
        <Qualifications
          lang={lang}
          formFields={formFields?.[4]}
          setCurrentStep={setCurrentStep}
          languages={languages}
          otherLanguages={otherLanguages}
        />
      ),
    },
    {
      title: "Job Description & Application Process",
      subCategory:
        "Provide a compelling job description and outline the application steps. All applications must be submitted via DriverJobz.",
      component: (
        <JobDescription
          lang={lang}
          formFields={formFields?.[5]}
          companyDetails={companyDetails}
          setCurrentStep={setCurrentStep}
        />
      ),
    },
  ];

  // const steps = [
  //   "Position & School Details",
  //   "Compensation & Benefits",
  //   "Bus Type Information",
  //   "Requirements & Qualifications",
  //   "Job Description & Application Process",
  // ];

  return (
    <div className={styles.formStepContainer}>
      <CompanyWithStepDetails currentStep={currentStep} steps={steps} />
      {steps[currentStep - 1]?.component}
    </div>
  );
};

export default FormStructure;
