import * as Yup from "yup";

export const getPayFormSchema = (isDraft: boolean) => {
  if (isDraft) return null;

  return Yup.object({
    type: Yup.string().required("Please select pay structure"),
    startingCpm: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .when("type", {
        is: "PER_MILE",
        then: (schema) => schema.required("Please enter starting cpm"),
        otherwise: (schema) => schema.notRequired(),
      }),
    cpmRangeMin: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .nullable()
      .when("type", {
        is: "PER_MILE",
        then: (schema) =>
          schema
            .test(
              "min-required-if-max",
              "Please enter minimum cpm",
              function (value) {
                const { cpmRangeMax } = this.parent;
                if (value === undefined && cpmRangeMax !== undefined)
                  return false;
                return true;
              }
            )
            .test("min-less-than-max", "Min <= max", function (value) {
              const { cpmRangeMax } = this.parent;
              if (
                value !== undefined &&
                value !== null &&
                cpmRangeMax !== undefined
              ) {
                return value <= cpmRangeMax;
              }
              return true;
            }),
        otherwise: (schema) => schema.notRequired(),
      }),

    cpmRangeMax: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .nullable()
      .when("type", {
        is: "PER_MILE",
        then: (schema) =>
          schema
            .test(
              "max-required-if-min",
              "Please enter maximum cpm",
              function (value) {
                const { cpmRangeMin } = this.parent;
                if (value === undefined && cpmRangeMin !== undefined)
                  return false;
                return true;
              }
            )
            .test("max-greater-than-min", "Max >= min", function (value) {
              const { cpmRangeMin } = this.parent;
              if (
                value !== undefined &&
                value !== null &&
                cpmRangeMin !== undefined
              ) {
                return value >= cpmRangeMin;
              }
              return true;
            }),
        otherwise: (schema) => schema.notRequired(),
      }),
    mileageCalculationMethod: Yup.string().when("type", {
      is: "PER_MILE",
      then: (schema) =>
        schema.required("Please select mileage calculation method"),
      otherwise: (schema) => schema.notRequired(),
    }),
    startingHourlyRate: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .when("type", {
        is: "HOURLY",
        then: (schema) =>
          schema
            .required("Please enter starting hourly rate")
            .min(1, "Starting hourly rate must be at least 1"),
        otherwise: (schema) => schema.notRequired(),
      }),
    hourlyRateRangeMin: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .nullable()
      .when("type", {
        is: "HOURLY",
        then: (schema) =>
          schema
            .min(1, "Minimum hourly rate must be at least 1")
            .test(
              "min-required-if-max",
              "Please enter minimum hourly rate",
              function (value) {
                const { hourlyRateRangeMax } = this.parent;
                if (value === undefined && hourlyRateRangeMax !== undefined)
                  return false;
                return true;
              }
            )
            .test("min-less-than-max", "Min <= max", function (value) {
              const { hourlyRateRangeMax } = this.parent;
              if (
                value !== undefined &&
                value !== null &&
                hourlyRateRangeMax !== undefined
              ) {
                return value <= hourlyRateRangeMax;
              }
              return true;
            }),
        otherwise: (schema) => schema.notRequired(),
      }),
    hourlyRateRangeMax: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .nullable()
      .when("type", {
        is: "HOURLY",
        then: (schema) =>
          schema
            .min(1, "Maximum hourly rate must be at least 1")
            .test(
              "max-required-if-min",
              "Please enter maximum hourly rate",
              function (value) {
                const { hourlyRateRangeMin } = this.parent;
                if (value === undefined && hourlyRateRangeMin !== undefined)
                  return false;
                return true;
              }
            )
            .test("max-greater-than-min", "Max >= min", function (value) {
              const { hourlyRateRangeMin } = this.parent;
              if (
                value !== undefined &&
                value !== null &&
                hourlyRateRangeMin !== undefined
              ) {
                return value >= hourlyRateRangeMin;
              }
              return true;
            }),
        otherwise: (schema) => schema.notRequired(),
      }),
    overtimeOption: Yup.string()
      .when("type", {
        is: "HOURLY",
        then: (schema) =>
          schema
            .required("Please select overtime rate")
            .test(
              "must-be-other-if-overtimeAfterHours-filled",
              "Overtime rate is filled, so Please select overtime available correclty",
              function (value) {
                const { overtimeAfterHours } = this.parent;
                console.log(overtimeAfterHours, "overtimeAfterHours")
                if (
                  overtimeAfterHours !== undefined &&
                  overtimeAfterHours !== null &&
                  overtimeAfterHours !== "" &&
                  value !== "other"
                ) {
                  return false;
                }
                return true;
              }
            ),
        otherwise: (schema) => schema.notRequired(),
      }),
    overtimeAfterHours: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .nullable()
      .when(["type", "overtimeOption"], {
        is: (type: string, overtimeOption: string) =>
          type === "HOURLY" && overtimeOption === "other",
        then: (schema) =>
          schema
            .required("Please enter overtime after hours")
            .min(1, "Overtime after hours must be at least 1"),
        otherwise: (schema) => schema.notRequired(),
      }),
    payPerDay: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .when("type", {
        is: "PER_DAY",
        then: (schema) =>
          schema
            .required("Please enter pay per day")
            .min(1, "Pay per day must be at least 1"),
        otherwise: (schema) => schema.notRequired(),
      }),
    payPerWeek: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .when("type", {
        is: "PER_WEEK",
        then: (schema) =>
          schema
            .required("Please enter pay per week")
            .min(1, "Pay per week must be at least 1"),
        otherwise: (schema) => schema.notRequired(),
      }),
    percentageRate: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .when("type", {
        is: "PERCENTAGE_OF_LOAD",
        then: (schema) =>
          schema
            .required("Please enter percentage rate")
            .min(1, "Percentage rate must be at least 1"),
        otherwise: (schema) => schema.notRequired(),
      }),
    percentageBasedOn: Yup.string().when("type", {
      is: "PERCENTAGE_OF_LOAD",
      then: (schema) => schema.required("Please select percentage based on"),
      otherwise: (schema) => schema.notRequired(),
    }),
    payPerLoad: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .when("type", {
        is: "PER_LOAD",
        then: (schema) =>
          schema
            .required("Please enter pay per load")
            .min(1, "Pay per load must be at least 1"),
        otherwise: (schema) => schema.notRequired(),
      }),
    annualSalary: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .when("type", {
        is: "SALARY",
        then: (schema) =>
          schema
            .required("Please enter annual salary")
            .min(1, "Annual salary must be at least 1"),
        otherwise: (schema) => schema.notRequired(),
      }),
    salaryRangeMin: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .nullable()
      .when("type", {
        is: "SALARY",
        then: (schema) =>
          schema
            .min(1, "Minimum salary must be at least 1")
            .test(
              "min-required-if-max",
              "Please enter minimum salary",
              function (value) {
                const { salaryRangeMax } = this.parent;
                if (value === undefined && salaryRangeMax !== undefined)
                  return false;
                return true;
              }
            )
            .test("min-less-than-max", "Min <= Max", function (value) {
              const { salaryRangeMax } = this.parent;
              if (
                value !== undefined &&
                value !== null &&
                salaryRangeMax !== undefined
              ) {
                return value <= salaryRangeMax;
              }
              return true;
            }),
        otherwise: (schema) => schema.notRequired(),
      }),
    salaryRangeMax: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .nullable()
      .when("type", {
        is: "SALARY",
        then: (schema) =>
          schema
            .min(1, "Maximum salary must be at least 1")
            .test(
              "max-required-if-min",
              "Please enter maximum salary",
              function (value) {
                const { salaryRangeMin } = this.parent;
                if (value === undefined && salaryRangeMin !== undefined)
                  return false;
                return true;
              }
            )
            .test("max-greater-than-min", "Max >= min", function (value) {
              const { salaryRangeMin } = this.parent;
              if (
                value !== undefined &&
                value !== null &&
                salaryRangeMin !== undefined
              ) {
                return value >= salaryRangeMin;
              }
              return true;
            }),
        otherwise: (schema) => schema.notRequired(),
      }),
    combinationDescription: Yup.string().when("type", {
      is: "COMBINATION_OTHER",
      then: (schema) => schema.required("Please enter combination description"),
      otherwise: (schema) => schema.notRequired(),
    }),
    minimumGuaranteedPay: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .required("Please enter minimum guaranteed pay")
      .min(1, "Minimum guaranteed pay must be atleast 1"),
    minimumGuaranteedPayPeriod: Yup.string().required(
      "Please select pay period"
    ),
    estimatePeriod: Yup.mixed()
      .nullable()
      .test(
        "required-if-estimates-exist",
        "Please select estimate period",
        function (value) {
          const { averageEarningsEstimate, averageEarningsEstimate1 } =
            this.parent;
          const hasMin =
            averageEarningsEstimate != null && averageEarningsEstimate !== "";
          const hasMax =
            averageEarningsEstimate1 != null && averageEarningsEstimate1 !== "";
          return hasMin || hasMax ? value != null && value !== "" : true;
        }
      ),
    averageEarningsEstimate: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .nullable()
      .test(
        "required-if-max",
        "Please enter minimum earnings estimate",
        function (value) {
          const { averageEarningsEstimate1, estimatePeriod } = this.parent;
          const periodSelected =
            estimatePeriod === 256 || estimatePeriod === 257;
          return periodSelected || averageEarningsEstimate1 != null
            ? value != null
            : true;
        }
      )
      .test("min-less-than-max", "Min <= Max", function (minVal) {
        const { averageEarningsEstimate1 } = this.parent;
        if (minVal != null && averageEarningsEstimate1 != null) {
          return minVal <= averageEarningsEstimate1;
        }
        return true;
      }),

    averageEarningsEstimate1: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .nullable()
      .test(
        "required-if-min",
        "Please enter maximum earnings estimate",
        function (value) {
          const { averageEarningsEstimate, estimatePeriod } = this.parent;
          const periodSelected =
            estimatePeriod === 256 || estimatePeriod === 257;
          return periodSelected || averageEarningsEstimate != null
            ? value != null
            : true;
        }
      )
      .test("max-greater-than-min", "Max >= Min", function (maxVal) {
        const { averageEarningsEstimate } = this.parent;
        if (maxVal != null && averageEarningsEstimate != null) {
          return maxVal >= averageEarningsEstimate;
        }
        return true;
      }),
    paySchedule: Yup.string().required("Please select pay schedule"),
    signOnBonusAmount: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .nullable()
      .when("signOnBonusOffered", {
        is: true,
        then: (schema) => schema.min(1, "Bonus amount must be at least 1"),
        otherwise: (schema) => schema.notRequired(),
      }),
    detentionPayRate: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .nullable()
      .when("bonusOffered", {
        is: (type: (string | number)[]) => type.includes("detentionPay"),
        then: (schema) =>
          schema
            .required("Please enter detection pay rate")
            .min(1, "Detection pay rate must be at least 1"),
        otherwise: (schema) => schema.notRequired(),
      }),
    detentionAfterHours: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .nullable()
      .when("bonusOffered", {
        is: (type: (string | number)[]) => type.includes("detentionPay"),
        then: (schema) =>
          schema
            .required("Please enter detection after hours")
            .min(1, "Detection after hours must be at least 1"),
        otherwise: (schema) => schema.notRequired(),
      }),
    layoverPay: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .nullable()
      .when("bonusOffered", {
        is: (type: (string | number)[]) => type.includes("layoverPay"),
        then: (schema) =>
          schema
            .required("Please enter layover pay")
            .min(1, "Layover pay must be at least 1"),
        otherwise: (schema) => schema.notRequired(),
      }),
    stopPayPerStop: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .nullable()
      .when("bonusOffered", {
        is: (type: (string | number)[]) => type.includes("stopPay"),
        then: (schema) =>
          schema
            .required("Please enter stop pay")
            .min(1, "Stop pay must be at least 1"),
        otherwise: (schema) => schema.notRequired(),
      }),
    otherBonusDescription: Yup.string().when("bonusOffered", {
      is: (type: (string | number)[]) => type.includes("other"),
      then: (schema) => schema.required("Please enter other value"),
      otherwise: (schema) => schema.notRequired(),
    }),
    otherBenefit: Yup.string().when("benefits", {
      is: (type: (string | number)[]) => type.includes("other"),
      then: (schema) => schema.required("Please enter other value"),
      otherwise: (schema) => schema.notRequired(),
    }),
    orientationDuration: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .nullable()
      .when("orientationRequired", {
        is: true,
        then: (schema) =>
          schema
            .test(
              "duration-required-if-unit-present",
              "Please enter duration",
              function (value) {
                const { orientationDurationUnit } = this.parent;
                const hasDuration = value !== undefined && value !== null;
                const hasUnit =
                  orientationDurationUnit !== undefined &&
                  orientationDurationUnit !== "";

                if (hasUnit && !hasDuration) return false;

                return true;
              }
            )
            .min(1, "Duration must be at least 1"),
        otherwise: (schema) => schema.notRequired(),
      }),
    orientationDurationUnit: Yup.string()
      .nullable()
      .when("orientationRequired", {
        is: true,
        then: (schema) =>
          schema.test(
            "unit-required-if-duration-present",
            "Please select duration unit",
            function (value) {
              const { orientationDuration } = this.parent;
              const hasDuration =
                orientationDuration !== undefined &&
                orientationDuration !== null;
              const hasUnit = value !== undefined && value !== "";

              if (hasDuration && !hasUnit) return false;

              return true;
            }
          ),
        otherwise: (schema) => schema.notRequired(),
      }),
    orientationPayRate: Yup.number()
      .transform((value, originalValue) =>
        String(originalValue).trim() === "" ? undefined : Number(originalValue)
      )
      .nullable()
      .when("paidOrientation", {
        is: true,
        then: (schema) =>
          schema
            .test(
              "duration-required-if-unit-present",
              "Please enter pay rate",
              function (value) {
                const { orientationPayUnit } = this.parent;
                const hasDuration = value !== undefined && value !== null;
                const hasUnit =
                  orientationPayUnit !== undefined && orientationPayUnit !== "";

                if (hasUnit && !hasDuration) return false;

                return true;
              }
            )
            .min(1, "Duration must be at least 1"),
        otherwise: (schema) => schema.notRequired(),
      }),
    orientationPayUnit: Yup.string()
      .nullable()
      .when("paidOrientation", {
        is: true,
        then: (schema) =>
          schema.test(
            "unit-required-if-duration-present",
            "Please select pay unit",
            function (value) {
              const { orientationPayRate } = this.parent;
              const hasDuration =
                orientationPayRate !== undefined && orientationPayRate !== null;
              const hasUnit = value !== undefined && value !== "";

              if (hasDuration && !hasUnit) return false;

              return true;
            }
          ),
        otherwise: (schema) => schema.notRequired(),
      }),
  });
};
