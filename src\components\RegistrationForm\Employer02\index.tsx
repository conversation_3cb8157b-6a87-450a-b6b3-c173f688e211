"use client";
import { useState, useEffect } from "react";
import css from "./employerRegistration.module.scss";
import {
  FormValues,
  getFormFieldsBySlug,
} from "@/services/employerFormService";
import { getCookie } from "cookies-next";
import { ToastContainer } from "react-toastify";
import AccordionWrapper from "@/components/Common/Accordion";
import CompanyInformation from "./CompanyInformation";
import ContactInformation from "./ContactInformation";
import ProfileEssentials from "./ProfileEssentials";
import VerificationProcess from "./VerificationProcess";
import { FileItem, CompanyValues, ContactValues, FormInitialValues } from "./types";
import { City, getCitiesByStateSlug } from "@/services/locationService";

interface Props {
  lang: "en" | "es";
}

const defaultInitialValues: FormInitialValues = {
  company: {
    name: "",
    email: "",
    phoneNumber: "",
    address: { street: "", city: "", state: "", zipCode: "" },
    website: "",
    legalName: "",
    einNumber: "",
    usDotNumber: "",
    mcNumber: "",
    noDotOrMcNumber: false,
  },
  contact: {
    contactPersonName: "",
    jobTitle: "",
  },
  fileUploads: {
    docFiles: {
      businessRegistration: [],
      einLetter: [],
    },
  },
  stepThree: {
    companyLogo:"",
    companyTypes: [],
    yearsInBusiness: "",
    estimatedDrivers: "",
    primaryAreas: [],
    otherCompanyType: ""
  },
};


const EmployerRegistration = ({ lang }: Props) => {

  const [isFirstAccordionComplete, setIsFirstAccordionComplete] = useState(false);
  const [isFirstAccordionOpen, setIsFirstAccordionOpen] = useState(false);

  const [isSecondAccordionComplete, setIsSecondAccordionComplete] = useState(false);
  const [isSecondAccordionOpen, setIsSecondAccordionOpen] = useState(false);

  const [isThirdAccordionComplete, setIsThirdAccordionComplete] = useState(false);
  const [isThirdAccordionOpen, setIsThirdAccordionOpen] = useState(false);

  const [isFourthAccordionOpen, setIsFourthAccordionOpen] = useState(false);
  const [isFourthAccordionComplete, setIsFourthAccordionComplete] = useState(false);

  const [currentStep, setCurrentStep] = useState(0)
  const [fmcsaVerified, setFmcsaVerified] = useState(null);
  const [dotFiles, setDotFiles] = useState<FileItem[]>([]);
  const [mcFiles, setMcFiles] = useState<FileItem[]>([]);

  const [areas, setAreas] = useState<FormValues[]>([]);
  const [yearInBussiness, setYearInBussiness] = useState<FormValues[]>([]);
  const [driverOptions, setDriverOptions] = useState<FormValues[]>([]);
  const [companyTypes, setCompanyTypes] = useState<FormValues[]>([]);
  const [selectedCitySlug, setSelectedCitySlug] = useState("");
  const [selectedStateSlug, setSelectedStateSlug] = useState("");
  const [cities, setCities] = useState<City[]>([]);


  const [addDocFiles, setAddDocfiles] = useState<FileItem[]>([]);
  const [enDocFiles, setEnDocFiles] = useState<FileItem[]>([]);

  const [formInitialValues, setFormInitialValues] = useState<FormInitialValues>(defaultInitialValues);
  const [formValues, setFormValues] = useState<{
    company: CompanyValues;
    contact: ContactValues;
  }>({
    company: defaultInitialValues.company,
    contact: defaultInitialValues.contact,
  });

  console.log("**lang", lang)
  
  useEffect(() => {
    const shouldFetch =
      isFirstAccordionOpen ||
      isSecondAccordionOpen ||
      isThirdAccordionOpen ||
      isFourthAccordionOpen;

    if (!shouldFetch) return;

    const fetchData = async () => {
      try {
        const token = getCookie("authToken");
        const headers: HeadersInit = {
          "Content-Type": "application/json",
          ...(token ? { Authorization: `${token}` } : {}),
        };

        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_V1}/employer/details`,
          {
            method: "GET",
            headers,
          }
        );

        const data = await res.json();
        const company = data?.data?.company || {};
        const user = data?.data?.user || {};
        const areaValues: string[] = company?.primaryOperatingAreas;

        const isLocalIncluded = areaValues.some(val => isNaN(Number(val)))
        const numericAreas = areaValues.filter(val => !isNaN(Number(val)))
        const alphaValues = areaValues.filter(val => isNaN(Number(val)))

        const [stateFromResponse, cityFromResponse] = alphaValues;

        if (isLocalIncluded && stateFromResponse) {
          setSelectedStateSlug(stateFromResponse);
        }

        if (isLocalIncluded && cityFromResponse) {
          setSelectedCitySlug(cityFromResponse);
        }

        const companyTypesFromAPI: string[] = company?.companyType || [];
        const ids = companyTypesFromAPI.filter(val => !isNaN(Number(val)));
        const others = companyTypesFromAPI.filter(val => isNaN(Number(val)));

        const updatedCompanyTypes = [...ids];
        if (others.length > 0) {
          updatedCompanyTypes.push("other");
        }
        setFmcsaVerified(company?.fmcsaVerified);
        setFormInitialValues({
          company: {
            name: company.name || "",
            email: company.email || "",
            phoneNumber: company.phoneNumber || "",
            address: {
              street: company.address?.street || "",
              city: company.address?.city || "",
              state: company.address?.state || "",
              zipCode: company.address?.zipCode || "",
            },
            website: company.website || null,
            legalName: company.legalName || "",
            einNumber: company.einNumber || "",
            usDotNumber: company.usDotNumber || "",
            mcNumber: company.mcNumber || "",
            noDotOrMcNumber: company?.noDotOrMcNumber,
          },
          contact: {
            contactPersonName: user.contactPersonName || "",
            jobTitle: user.jobTitle || "",
          },
          fileUploads: {
            docFiles: {
              businessRegistration:
                company?.docFiles?.businessRegistration || [],
              einLetter: company?.docFiles?.einLetter || [],
            },
          },

          stepThree: {
            companyLogo: company?.companyLogo,
            companyTypes: updatedCompanyTypes,
            yearsInBusiness: company.yearsInBusiness || "",
            estimatedDrivers: company.numberOfDrivers || "",
            primaryAreas: isLocalIncluded ? [...numericAreas, "local"] : numericAreas,
            otherCompanyType: others[0] || ""
          },
        });

        setAddDocfiles(company?.docFiles?.businessRegistration);
        setEnDocFiles(company?.docFiles?.einLetter);
      } catch (error) {
        console.error("Failed to fetch employer details", error);
      }
    };

    fetchData();
  }, [
    isFirstAccordionOpen,
    isSecondAccordionOpen,
    isThirdAccordionOpen,
    isFourthAccordionOpen,
  ]);

  useEffect(() => {
    const fetchcityData = async () => {
      const citiesList = await getCitiesByStateSlug(selectedStateSlug);
      setCities(citiesList)
    }
    fetchcityData()
  }, [selectedStateSlug])

  useEffect(() => {
    const fetchData = async () => {
      try {
        const token = getCookie("authToken");
        const headers: HeadersInit = {
          "Content-Type": "application/json",
          ...(token ? { Authorization: `${token}` } : {}),
        };

        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_V1}/employer/details`,
          {
            method: "GET",
            headers,
          }
        );

        const data = await res.json();
        const currentStep = data?.data?.currentStep;
        setCurrentStep(currentStep)

        const completeSetters = [
          setIsFirstAccordionComplete,
          setIsSecondAccordionComplete,
          setIsThirdAccordionComplete,
          setIsFourthAccordionComplete,
        ];

        const openSetters = [
          setIsFirstAccordionOpen,
          setIsSecondAccordionOpen,
          setIsThirdAccordionOpen,
          setIsFourthAccordionOpen,
        ];

        if (currentStep >= 0 && currentStep <= 4) {
          for (let i = 0; i < currentStep; i++) {
            completeSetters[i](true);
          }

          if (currentStep < 4) {
            openSetters[currentStep](true);
          } else {
            openSetters.forEach((set) => set(false));
          }
        }

      } catch (error) {
        console.error("Failed to fetch employer details", error);
      }
    };

    const fetchCompanyTypes = async () => {
      const resCompanyType = await getFormFieldsBySlug("company-type-employer");
      const resYearInBussiness = await getFormFieldsBySlug(
        "years-in-business-employer"
      );
      const resDriverOption = await getFormFieldsBySlug(
        "estimated-number-of-driversoperators-employed-employer"
      );
      const resAreas = await getFormFieldsBySlug(
        "primary-operating-areas-employer"
      );
      setCompanyTypes(resCompanyType);
      setYearInBussiness(resYearInBussiness);
      setDriverOptions(resDriverOption);
      setAreas(resAreas);
    };
    fetchCompanyTypes();
    fetchData();
  }, []);

  return (
    <section className={css.employerRegistration}>
      <div className={css.container}>
        <h6 className={css.required}>
          Required fields are marked with <span>*</span>
        </h6>

        {/* Accordion 1: Company Information */}
        <AccordionWrapper
          title="Company Information"
          isOpen={isFirstAccordionOpen}
          setIsOpen={(open) => {
            setIsFirstAccordionOpen(open);
            if (open) {
              setIsSecondAccordionOpen(false);
              setIsThirdAccordionOpen(false);
              setIsFourthAccordionOpen(false);
            }
          }}
          isComplete={isFirstAccordionComplete}
        >
          {isFirstAccordionOpen && <CompanyInformation
            onFormSubmit={(data) => {
              setFormValues((prev) => ({ ...prev, company: data }));
            }}
            formInitialValues={formInitialValues.company}
            currentStep={currentStep}
            setIsFirstAccordionComplete={setIsFirstAccordionComplete}
            setIsFirstAccordionOpen={setIsFirstAccordionOpen}
            setIsSecondAccordionOpen={setIsSecondAccordionOpen} />}
        </AccordionWrapper>

        {/* Accordion 2: Primary Contact Info */}
        <AccordionWrapper
          title="Primary Contact Information"
          isOpen={isSecondAccordionOpen}
          setIsOpen={(open) => {
            if (open) {
              setIsFirstAccordionOpen(false);
              setIsThirdAccordionOpen(false);
              setIsFourthAccordionOpen(false);
            }
            setIsSecondAccordionOpen(open);
          }}
          isComplete={isSecondAccordionComplete}
          isClickable={isFirstAccordionComplete}
        >
          {isSecondAccordionOpen && isFirstAccordionComplete && (
            <ContactInformation
              formInitialValues={formInitialValues.contact}
              companyValues={formInitialValues.company || formValues.company}
              currentStep={currentStep}
              setIsSecondAccordionComplete={setIsSecondAccordionComplete}
              setIsSecondAccordionOpen={setIsSecondAccordionOpen}
              setIsThirdAccordionOpen={setIsThirdAccordionOpen}
            />
          )}
        </AccordionWrapper>

        {/* Accordion 3: Company Profile Essentials */}
        <AccordionWrapper
          title="Company Profile Essentials"
          isOpen={isThirdAccordionOpen}
          setIsOpen={(open) => {
            if (open) {
              setIsFirstAccordionOpen(false);
              setIsSecondAccordionOpen(false);
              setIsFourthAccordionOpen(false);
            }
            setIsThirdAccordionOpen(open);
          }}
          isComplete={isThirdAccordionComplete}
          isClickable={isSecondAccordionComplete}

        >
          {isThirdAccordionOpen && isSecondAccordionComplete && (
            <ProfileEssentials
              formInitialValues={formInitialValues.stepThree}
              currentStep={currentStep}
              areas={areas}
              yearInBussiness={yearInBussiness}
              driverOptions={driverOptions}
              companyTypes={companyTypes}
              setIsThirdAccordionComplete={setIsThirdAccordionComplete}
              setIsThirdAccordionOpen={setIsThirdAccordionOpen}
              setIsFourthAccordionOpen={setIsFourthAccordionOpen}
              selectedStateSlug={selectedStateSlug}
              setSelectedStateSlug={setSelectedStateSlug}
              selectedCitySlug={selectedCitySlug}
              setSelectedCitySlug={setSelectedCitySlug}
              cities={cities}
              setCities={setCities}
            />
          )}
        </AccordionWrapper>

        {/* Accordion 4: Verification Process */}
        <AccordionWrapper
          title="Verification Process"
          isOpen={isFourthAccordionOpen}
          setIsOpen={(open) => {
            if (open) {
              setIsFirstAccordionOpen(false);
              setIsSecondAccordionOpen(false);
              setIsThirdAccordionOpen(false);
            }
            setIsFourthAccordionOpen(open);
          }}
          isComplete={isFourthAccordionComplete}
          isClickable={isThirdAccordionComplete}
        >
          aa
          {isFourthAccordionOpen && isThirdAccordionComplete && (
            <div className={`${css.accordionBody} ${css.pt0}`}>
              <div className={css.businessVerification}>
                Business Verification
              </div>
              <VerificationProcess
                formInitialValues={formInitialValues}
                dotFiles={dotFiles}
                mcFiles={mcFiles}
                currentStep={currentStep}
                fmcsaVerified={fmcsaVerified}
                setDotFiles={setDotFiles}
                addDocFiles={addDocFiles}
                setMcFiles={setMcFiles}
                enDocFiles={enDocFiles}
              />
            </div>
          )}
        </AccordionWrapper>
      </div>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
      />
    </section>
  );
};

export default EmployerRegistration;
