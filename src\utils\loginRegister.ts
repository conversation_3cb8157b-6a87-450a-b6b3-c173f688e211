import { deleteCookie, setC<PERSON>ie } from "cookies-next";
export const saveToken = (token: string, refreshToken: string, email: string, isCompany: boolean) => {
  const expires = new Date(new Date().setMonth(new Date().getMonth() + 1)); // 1 month expiry added to cookie
  setCookie("authToken", token, { path: "/", expires });
  setCookie("refreshToken", refreshToken, { path: "/", expires });
  setCookie("userEmail", email, { path: "/", expires });
  setCookie("isCompany", isCompany, { path: "/", expires });
};

export const removeToken = () => {
  deleteCookie("authToken");
  deleteCookie("refreshToken");
  deleteCookie("userEmail");
  deleteCookie("isCompany");
}
