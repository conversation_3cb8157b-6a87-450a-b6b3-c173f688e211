@import '../../../../styles/global.scss';

.basicForm {
    margin-top: 45px;
    
    .formBody {
        padding-top: 24px;
        display: none;
    }

    &.active {
        .formHeading {
            img {
                &.icon {
                    transform: rotateX(180deg);
                }
            }
        }

        .formBody {
            display: block;
        }
    }
}

.formHeading {
    border-bottom: 1px solid #D6DDEB;
    padding: 24px 0px;
    padding-right: 40px;
    position: relative;
    cursor: pointer;

    &:last-child {
        margin-bottom: 0px;
    }

    h3 {
        color: $dark-900;
        font-weight: 500;
        font-size: 23px;
        line-height: 140%;
    }

    img {
        &.icon {
            position: absolute;
            right: 8px;
            top: 38px;
        }
    }
}
