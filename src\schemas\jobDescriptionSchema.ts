import * as Yup from "yup";

export const getJobFormSchema = (isDraft: boolean) => {
  if (isDraft) return null;

  return Yup.object({
    jobDescription: Yup.string().required("Please enter job description"),
    contactPersonEmail: Yup.string()
      .transform((value) => (value === "" ? undefined : value))
      .notRequired()
      .email("Please enter a valid email")
      .matches(/^[^\s@]+@[^\s@]+\.[^\s@]{2,}$/, "Please enter a valid email"),
    eeoConfirmed: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please accept to continue")
      .required("Please accept to continue"),
  });
};
