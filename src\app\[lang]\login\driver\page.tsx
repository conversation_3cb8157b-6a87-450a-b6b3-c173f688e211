// import LoginContainer from "@/components/Login/LoginContainer";
// import css from "../loginPageConatiner.module.scss";

// async function page() {
//   return (
//     <div className={css.pageContainer}>
//       <LoginContainer type={"driver"} />
//     </div>
//   );
// }

// export default page;

import LoginContainer from '@/components/Login/LoginContainer';
import css from '../loginPageConatiner.module.scss';
async function page() {
    return (
      <div className={css.pageContainer}>
        <LoginContainer type={"driver"} />
      </div>
    );
  }
  
  export default page;
  