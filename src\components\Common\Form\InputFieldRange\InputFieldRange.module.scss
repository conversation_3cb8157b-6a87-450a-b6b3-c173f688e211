@import "../../../../styles/global.scss";

.columnField {
    display: flex;
    flex-direction: column;

    &.columnWidth_2 {
        width: calc((100% - 24px) / 2);
    }

    &.columnWidth_3 {
        width: calc((100% - 48px) / 3);
    }

    label {
        font-weight: 700;
        font-size: 14px;
        line-height: 22px;
        margin-bottom: 4px;
        gap: 4px;
        
        .desc {
            font-weight: 400;
        }

        .important {
            color: $red;
        }
    }

    .rangeField {
        display: flex;
        gap: 10px;

        .textClass {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 700;
            height: 44px;
        }
    }

    input {
        height: 44px;
        border-radius: 4px;
        border: 1px solid #707070;
        padding: 10px 8px;
        font-size: 14px;
        line-height: 24px;
        outline: none;
    }
}

.columnWidthTextAdjust {
    width: calc((100% - 48px) / 3);
    .rangeField {
        display: flex;
        gap: 29px;
    }
}
