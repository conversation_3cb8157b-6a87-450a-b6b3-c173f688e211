import React from "react";
import styles from "./RadioField.module.scss";
import { DropdownItem } from "@/types/jobpostingform";
import { FormikProps, getIn } from "formik";
import TextField from "../TextField";
import Tooltip from "../Tooltip";

interface RadioProps<T extends Record<string, unknown>> {
  className?: string;
  styleClass?: string;
  label?: string;
  desc?: string;
  fieldName: string;
  hide?: boolean;
  radioArray: DropdownItem[];
  formik: FormikProps<T>;
  textClassName?: string;
  textFieldName?: string;
  textPlaceholder?: string;
  textInput?: boolean;
  tooltipMsg?: string;
  handleChange?: (event: React.ChangeEvent<HTMLInputElement>, fieldName: string, formik: FormikProps<T>) => void;
}

const RadioField = <T extends Record<string, unknown>>({
  className = "",
  styleClass = "",
  label = "",
  desc = "",
  fieldName = "",
  hide = false,
  radioArray = [],
  formik,
  textClassName = "",
  textFieldName = "",
  textPlaceholder,
  handleChange,
  textInput = false,
  tooltipMsg
}: RadioProps<T>) => {
  const selectedValue = getIn(formik.values, fieldName);

  return (
    <div className={`${styles.radioWrapper} ${styles[className]} ${styleClass}`}>
      {label &&
        <label htmlFor={fieldName} className={styles.labelRadio}>
          {label} {desc && <span className={styles.desc}>{desc}</span>}{" "}
          {!hide && <span className={styles.important}>*</span>}
          {tooltipMsg && <Tooltip tooltipMsg={tooltipMsg} />}
        </label>
      }
      <ul className={styles.radioList}>
        {radioArray.map((list) => (
          <li key={list?.label} className={(textInput && list.value === "other") ? styles.fullWidthLi : ""}>
            <label className={styles.radioContainer}>
              <input
                type="radio"
                name={fieldName}
                onChange={() => formik?.setFieldValue(fieldName, list?.value)}
                checked={selectedValue === list?.value}
              />
              <span className={styles.customRadio}></span>

              {(!textInput || list.value !== "other") &&
                <span className={styles.radioLabel}>{list?.label}</span>
              }

              {textInput && list.value === "other" && (
                <span className={styles.otherLabel}>
                  {" "}Yes, after{" "}
                  <TextField
                    className={textClassName}
                    fieldName={textFieldName}
                    placeholder={textPlaceholder}
                    formik={formik}
                    handleChange={handleChange}
                    errorClass="errorClassMargin"
                  />{" "}
                  hrs per day
                </span>
              )}
            </label>
          </li>
        ))}
      </ul>
      {formik?.touched[fieldName] && typeof formik?.errors[fieldName] === "string" && (
        <div className="error_msg fielderror">
          {formik?.errors[fieldName]}
        </div>
      )}
    </div>
  );
};

export default RadioField;
