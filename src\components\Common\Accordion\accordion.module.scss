@import '../../../styles/global.scss';

.accordion {
    border: 1px solid #E5E5E5;
    border-radius: 16px;
    margin-top: 16px;
    padding: 16px 24px;

    @include for-size(tablet-phone) {
        padding: 8px;
    }

    &.active {
        border-color: $black;

        .accordionTop {
            img {
                transform: rotateX(180deg);
            }
        }
    }

    .accordionTop {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;
        min-height: 48px;

        @include for-size(tablet-phone) {
            min-height: 32px;
        }

        h3 {
            color: $black;
            font-weight: 500;
            font-size: 20px;
            line-height: 28px;
            width: calc(100% - 48px);

            @include for-size(tablet-phone) {
                font-size: 16px;
                line-height: 22px;
            }
        }
    }

    .accordionBody {
        padding-top: 24px;
        padding-bottom: 12px;
        border-top: 1px solid #E5E5E5;

        &.pt0 {
            padding-top: 0px;
        }

        @include for-size(tablet-phone) {
            padding-top: 16px;
        }
    }

    .businessVerification {
        background-color: #FFFBEE;
        color: $black;
        font-weight: 700;
        font-size: 20px;
        line-height: 28px;
        padding: 18px 20px;
        margin-bottom: 24px;
    }
}


.accordionBody {
    padding-top: 24px;
    padding-bottom: 12px;
    border-top: 1px solid #E5E5E5;

    &.pt0 {
        padding-top: 0px;
    }
}