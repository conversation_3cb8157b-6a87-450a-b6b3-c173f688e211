
"use client";
import React from "react";
import { CommonDriverCategoryProvider } from "@/contexts/CommonDriverCategoryContext";
import { useNemtDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import Experience from "./Experience";
import WorkHistory from "./WorkHistory";
import Medical from "./Medical";
import Documents from "./Documents";
import Consents from "./Consents";
import css from "../Driver/driver.module.scss";
import StepIndicator from "@/components/Common/StepIndicator";

const NemtDriverCategoryForm: React.FC = () => {
  let { currentStep } = useNemtDriverCategory();
  currentStep = 3
  const categorySteps = [
    { id: 1, title: "Experience" },
    { id: 2, title: "Work History" },
    { id: 3, title: "Medical & Certifications" },
    { id: 4, title: "Documents" },
    { id: 5, title: "Consents & Review" },
  ];

  const completedSteps: number[] = [];
  for (let i = 1; i < currentStep; i++) {
    completedSteps.push(i);
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <Experience />;
      case 2:
        return <WorkHistory />;
      case 3:
        return <Medical />;
      case 4:
        return <Documents />;
      case 5:
        return <Consents />;
      default:
        return <Experience />;
    }
  };

  const currentStepData = categorySteps.find(step => step.id === currentStep);

  return (
    <div className={css.driverCategory}>
      <div className={css.header}>
        <h2>NEMT Driver Category Registration</h2>
        <p>Complete your Non-Emergency Medical Transportation driver profile</p>
      </div>

      <StepIndicator
        categorySteps={categorySteps}
        currentStep={currentStep}
        completedSteps={completedSteps}
      />

      <div className={css.stepContent}>
        <div className={css.stepHeader}>
          <h3>{currentStepData?.title}</h3>
        </div>
        {renderStepContent()}
      </div>
    </div>
  );
};

const NemtDriver = () => {
  return (
    <CommonDriverCategoryProvider categoryType="nemt-driver">
      <NemtDriverCategoryForm />
    </CommonDriverCategoryProvider>
  );
};

export default NemtDriver;