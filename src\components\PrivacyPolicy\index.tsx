import css from "./PrivacyPolicy.module.scss";

import { getDictionary } from "@/app/[lang]/dictionaries";

interface Props {
  lang: "en" | "es";
}

const PrivacyPolicy = async ({ lang }: Props) => {
  const dict = await getDictionary(lang);
  return (
    <div className={css.privacyContainer}>
      <h1 className={css.heading}>{dict.privacy?.title}</h1>

      <section className={css.section}>
        <h2 className={css.subHeading}>{dict.privacy?.section1?.title}</h2>
        <p>{dict.privacy?.section1?.desc}</p>
      </section>

      <section className={css.section}>
        <h2 className={css.subHeading}>{dict.privacy?.section2?.title}</h2>
        <h3 className={css.miniHeading}>{dict.privacy?.section2?.sub1}</h3>
        <ul>
          <li>{dict.privacy?.section2?.list1}</li>
          <li>{dict.privacy?.section2?.list2}</li>
          <li>{dict.privacy?.section2?.list3}</li>
          <li>{dict.privacy?.section2?.list4}</li>
          <li>{dict.privacy?.section2?.list5}</li>
        </ul>

        <h3 className={css.miniHeading}>{dict.privacy?.section2?.sub2}</h3>
        <ul>
          <li>{dict.privacy?.section2?.list6}</li>
          <li>{dict.privacy?.section2?.list7}</li>
          <li>{dict.privacy?.section2?.list8}</li>
          <li>{dict.privacy?.section2?.list9}</li>
        </ul>

        <h3 className={css.miniHeading}>{dict.privacy?.section2?.sub3}</h3>
        <p>{dict.privacy?.section2?.desc}</p>
      </section>

      <section className={css.section}>
        <h2 className={css.subHeading}>{dict.privacy?.section3?.title}</h2>
        <ul>
          <li>{dict.privacy?.section3?.list1}</li>
          <li>{dict.privacy?.section3?.list2}</li>
          <li>{dict.privacy?.section3?.list3}</li>
          <li>{dict.privacy?.section3?.list4}</li>
          <li>{dict.privacy?.section3?.list5}</li>
          <li>{dict.privacy?.section3?.list6}</li>
          <li>{dict.privacy?.section3?.list7}</li>
          <li>{dict.privacy?.section3?.list8}</li>
        </ul>
      </section>

      <section className={css.section}>
        <h2 className={css.subHeading}>{dict.privacy?.section4?.title}</h2>
        <ul>
          <li>{dict.privacy?.section4?.list1}</li>
          <li>{dict.privacy?.section4?.list2}</li>
          <li>{dict.privacy?.section4?.list3}</li>
          <li>{dict.privacy?.section4?.list4}</li>
          <li>{dict.privacy?.section4?.list5}</li>
        </ul>
      </section>

      <section className={css.section}>
        <h2 className={css.subHeading}>{dict.privacy?.section5?.title}</h2>
        <ul>
          <li>{dict.privacy?.section5?.list1}</li>
          <li>{dict.privacy?.section5?.list2}</li>
          <li>{dict.privacy?.section5?.list3}</li>
          <li>{dict.privacy?.section5?.list4}</li>
          <li>{dict.privacy?.section5?.list5}</li>
          <li>{dict.privacy?.section5?.list6}</li>
          <li>{dict.privacy?.section5?.list7}</li>
        </ul>
      </section>

      <section className={css.section}>
        <h2 className={css.subHeading}>{dict.privacy?.section6?.title}</h2>
        <p>{dict.privacy?.section6?.desc}</p>
      </section>

      <section className={css.section}>
        <h2 className={css.subHeading}>{dict.privacy?.section7?.title}</h2>
        <p>{dict.privacy?.section7?.desc}</p>
      </section>

      <section className={css.section}>
        <h2 className={css.subHeading}>{dict.privacy?.section8?.title}</h2>

        <div>
          <p>{dict.privacy?.section8?.desc1}</p>
          <p>{dict.privacy?.section8?.desc2}</p>
          <ul>
            <li>{dict.privacy?.section8?.list1}</li>
            <li>{dict.privacy?.section8?.list2}</li>
            <li>{dict.privacy?.section8?.list3}</li>
          </ul>
          <p>{dict.privacy?.section8?.desc3}</p>
        </div>
      </section>

      <section className={css.section}>
        <h2 className={css.subHeading}>{dict.privacy?.section9?.title}</h2>
        <p>{dict.privacy?.section9?.desc}</p>
      </section>

      <section className={css.section}>
        <h2 className={css.subHeading}>{dict.privacy?.section10?.title}</h2>
        <p>{dict.privacy?.section10?.desc}</p>
      </section>

      <section className={css.section}>
        <h2 className={css.subHeading}>{dict.privacy?.section11?.title}</h2>
        <p>{dict.privacy?.section11?.desc}</p>
      </section>

      <section className={css.section}>
        <h2 className={css.subHeading}>{dict.privacy?.section12?.title}</h2>
        <p>{dict.privacy?.section12?.desc}</p>
      </section>

      <section className={css.section}>
        <h2 className={css.subHeading}>{dict.privacy?.section13?.title}</h2>
        <p>
          {dict.privacy?.section13?.desc}{" "}
          <a href="mailto:<EMAIL>" className={css.link}>
            <EMAIL>
          </a>
        </p>
      </section>
    </div>
  );
};

export default PrivacyPolicy;
