import React from 'react';
import css from './stepIndicator.module.scss';

export interface Step {
  id: number;
  title: string;
}

interface StepIndicatorProps {
  categorySteps: Step[];
  currentStep: number;
  completedSteps: number[];
}

const StepIndicator: React.FC<StepIndicatorProps> = ({
  categorySteps,
  currentStep,
  completedSteps,
}) => {
  return (
    <ul className={css.allSteps}>
      {categorySteps.map((step, index) => (
        <li key={index}>
          <figure className={`${
            completedSteps.includes(step.id) ? css.completed :
            currentStep === step.id ? css.active : css.inactive
          }`}>
            {completedSteps.includes(step.id) ? (
              <img src="/images/icons/icon-step-complete.svg" alt="completed" />
            ) : (
              <span>{step.id}</span>
            )}
          </figure>
          <p className={css.stepTitle}>{step.title}</p>
        </li>
      ))}
    </ul>
  );
};

export default StepIndicator;