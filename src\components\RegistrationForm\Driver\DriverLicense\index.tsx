"use client";
import React, { useEffect, useRef, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import "react-datepicker/dist/react-datepicker.css";
import {
  fetchDriverLicenseClasses,
  fetchCdlEndorsements,
  fetchCdlRestrictions,
  FormValue,
  submitDriverDetails,
} from "@/services/driverFormService";
import { getStates, State } from "@/services/locationService";
import css from "../driverRegistration.module.scss";
import DateInput from "@/components/Common/DateInput/DateInput";
import { DriverLicenseFormValues } from "../types";
import { toast } from "react-toastify";

interface Props {
  onFormSubmit: () => void;
  initialData: DriverLicenseFormValues | null;
  currentStep: number;
  currentStage: number;
}
const DriverLicense: React.FC<Props> = ({
  onFormSubmit,
  initialData,
  currentStep,
  currentStage,
}) => {
  const [licenseOptions, setLicenseOptions] = useState<FormValue[]>([]);
  const [states, setStates] = useState<State[]>([]);
  const [endorsements, setEndorsements] = useState<FormValue[]>([]);
  const [restrictions, setRestrictions] = useState<FormValue[]>([]);
  const [loading, setLoading] = useState(true);
  const [noLicenseId, setNoLicenseId] = useState<number | null>(null);

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const loadData = async () => {
      const [licenseData, stateData, endorsementData, restrictionData] =
        await Promise.all([
          fetchDriverLicenseClasses(),
          getStates(),
          fetchCdlEndorsements(),
          fetchCdlRestrictions(),
        ]);
      if (licenseData) {
        setLicenseOptions(licenseData);
      }
      if (stateData) setStates(stateData);
      if (endorsementData) setEndorsements(endorsementData);
      if (restrictionData) setRestrictions(restrictionData);
      const noLicenseOption = licenseData?.find((opt) =>
        opt.label.en.toLowerCase().includes("i do not hold")
      );
      if (noLicenseOption) {
        setNoLicenseId(noLicenseOption.formValueId);
      }
      setLoading(false);
    };

    loadData();
  }, []);

  const formik = useFormik({
    initialValues: {
      licenseClass: null as number | null,
      licenseState: null as number | null,
      licenseNumber: null,
      licenseExpiry: null as Date | null,
      endorsements: [] as number[] | null,
      restrictions: [] as number[] | null,
    },
    validationSchema: Yup.lazy((values) => {
      const holdsLicense =
        values.licenseClass !== null &&
        noLicenseId !== null &&
        values.licenseClass !== noLicenseId;

      return Yup.object().shape({
        licenseClass: Yup.number().required("License class is required"),
        licenseState: holdsLicense
          ? Yup.number().required("State is required")
          : Yup.number().nullable(),
        licenseNumber: holdsLicense
          ? Yup.string().required("License number is required")
          : Yup.string().nullable(),
        licenseExpiry: holdsLicense
          ? Yup.date().nullable().required("Expiration date is required")
          : Yup.date().nullable(),
        endorsements: Yup.array().of(Yup.number()),
        restrictions: Yup.array().of(Yup.number()),
      });
    }),
    onSubmit: async (values) => {
      const step = 3;
      const payload = {
        ...(currentStage <= 2 && { currentStage: 2 }),
        ...(currentStage === 2 && step >= currentStep && { currentStep: step }),
        driver: {
          driverLicenseClass: values.licenseClass,
          driverLicenseNumber: values.licenseNumber,
          driverLicenseState: values.licenseState,
          driverLicenseExpiration: values.licenseExpiry?.toISOString(),
          driverLicenseEndorsements: values.endorsements,
          driverLicenseRestrictions: values.restrictions,
        },
      };
      try {
        const response = await submitDriverDetails(payload);
        console.log("Submitted:", response);
        onFormSubmit();
        toast.success("Driver's license information submitted successfully");
      } catch (err: unknown) {
        if (err instanceof Error) {
          toast.error("Failed to submit driver's license information");
        } else {
          toast.error("Something went wrong. Please try again  ");
        }
      }
    },
  });

  const isLicenseHeld = formik.values.licenseClass !== noLicenseId;

  const handleCheckboxChange = (
    id: number,
    listName: "endorsements" | "restrictions"
  ) => {
    const list = formik.values[listName] ?? [];

    if (list.includes(id)) {
      formik.setFieldValue(
        listName,
        list.filter((item) => item !== id)
      );
    } else {
      formik.setFieldValue(listName, [...list, id]);
    }
  };

  // prefill
  useEffect(() => {
    if (initialData) {
      formik.setValues({
        licenseClass: initialData.driverLicenseClass || null,
        licenseState: initialData.driverLicenseState || null,
        licenseNumber: initialData.driverLicenseNumber as null,
        licenseExpiry: initialData.driverLicenseExpiration
          ? new Date(initialData.driverLicenseExpiration)
          : null,
        endorsements: initialData.driverLicenseEndorsements || [],
        restrictions: initialData.driverLicenseRestrictions || [],
      });
    }
  }, [initialData]);

  return (
    <>
      <form onSubmit={formik.handleSubmit} className={css.commonForm}>
        {loading ? (
          <p>Loading...</p>
        ) : (
          <>
            <div className={`${css.formRow} ${css.dBlaco}`}>
              <div className={css.labelDiv}>
                <label>
                  Highest Driver&apos;s License Class Held<sup>*</sup>
                  <span className={css.tooltipIcon}>
                    <img src="/images/icons/icon-info.svg" alt="" />
                    <span className={css.tooltip}>
                      Select the general geographic areas your company serves.
                    </span>
                  </span>
                </label>
              </div>
              <ul className={css.checkboxList}>
                {licenseOptions.map((option) => (
                  <li key={option.formValueId} className={css.radioGroup}>
                    <label className={css.radioLabel}>
                      <input
                        type="radio"
                        name="licenseClass"
                        value={option.formValueId}
                        checked={
                          formik.values.licenseClass === option.formValueId
                        }
                        onChange={() => {
                          formik.setFieldValue(
                            "licenseClass",
                            option.formValueId
                          );
                          if (option.formValueId === noLicenseId) {
                            formik.setFieldValue("licenseState", null);
                            formik.setFieldValue("licenseNumber", "");
                            formik.setFieldValue("licenseExpiry", null);
                          }
                        }}
                      />
                      <span className={css.checkmark}></span>
                      <p>{option.label.en}</p>
                    </label>
                  </li>
                ))}
              </ul>
              {formik.touched.licenseClass && formik.errors.licenseClass && (
                <div className={css.error}>{formik.errors.licenseClass}</div>
              )}
            </div>

            <div className={css.formRow}>
              {/* <div className={css.col03}>
                <div className={css.labelDiv}>
                  <label>License Issuing State<sup>*</sup></label>
                </div>
                <div className={css.dropdown}>
                  <button type='button' className={css.dropdownToggle}>Select State</button>
                  <div className={css.dropdownMenu}>
                    {states.map((state) => (
                      <button
                        type="button"
                        key={state.stateId}
                        className={css.dropdownItem}
                      >
                        {state.name.en}
                      </button>
                    ))}
                  </div>
                </div>
                {formik.touched.licenseState && formik.errors.licenseState && (
                  <div className={css.error}>{formik.errors.licenseState}</div>
                )}
              </div> */}
              <div className={css.col03}>
                <div className={css.labelDiv}>
                  <label>
                    License Issuing State<sup>*</sup>
                  </label>
                </div>

                <div className={css.dropdown} ref={dropdownRef}>
                  <button
                    type="button"
                    className={css.dropdownToggle}
                    disabled={!isLicenseHeld}
                    onClick={() => setIsDropdownOpen((prev) => !prev)}
                  >
                    {formik.values.licenseState
                      ? states.find(
                          (s) => s.stateId === formik.values.licenseState
                        )?.name.en
                      : "Select State"}
                  </button>

                  {isDropdownOpen && isLicenseHeld && (
                    <div className={css.dropdownMenu}>
                      {states.map((state) => (
                        <button
                          type="button"
                          key={state.stateId}
                          className={css.dropdownItem}
                          onClick={() => {
                            formik.setFieldValue("licenseState", state.stateId);
                            setIsDropdownOpen(false);
                          }}
                        >
                          {state.name.en}
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                {formik.touched.licenseState && formik.errors.licenseState && (
                  <div className={css.error}>{formik.errors.licenseState}</div>
                )}
              </div>
              <div className={css.col03}>
                <div className={css.labelDiv}>
                  <label>
                    Driver&apos;s License Number<sup>*</sup>
                  </label>
                </div>
                <input
                  type="text"
                  name="licenseNumber"
                  placeholder="Enter License Number"
                  value={formik.values.licenseNumber ?? ""}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  className={css.input}
                  disabled={!isLicenseHeld}
                />
                {formik.touched.licenseNumber &&
                  formik.errors.licenseNumber && (
                    <div className={css.error}>
                      {formik.errors.licenseNumber}
                    </div>
                  )}
              </div>
              <div className={css.col03}>
                <div className={css.labelDiv}>
                  <label>
                    License Expiration Date<sup>*</sup>
                  </label>
                </div>
                <DateInput
                  selected={formik.values.licenseExpiry}
                  onChange={(date) =>
                    formik.setFieldValue("licenseExpiry", date)
                  }
                  minDate={new Date()}
                  disabled={!isLicenseHeld}
                />
                {formik.touched.licenseExpiry &&
                  formik.errors.licenseExpiry && (
                    <div className={css.error}>
                      {formik.errors.licenseExpiry}
                    </div>
                  )}
              </div>
            </div>
            {/* <div>
              <select
                name="licenseState"
                value={formik.values.licenseState || ''}
                onChange={(e) => formik.setFieldValue('licenseState', Number(e.target.value))}
                disabled={ !isLicenseHeld}
                style={{
                  padding: '8px',
                  fontSize: '16px',
                  width: '100%',
                  maxWidth: '300px',
                  backgroundColor: !isLicenseHeld ? '#f2f2f2' : undefined
                }}
              >
                <option value="">Select a state</option>
                {states.map((state) => (
                  <option key={state.stateId} value={state.stateId.toString()}>
                    {state.name.en}
                  </option>
                ))}
              </select>
              {formik.touched.licenseState && formik.errors.licenseState && (
                <div style={{ color: 'red' }}>{formik.errors.licenseState}</div>
              )}
            </div>

            <div className={css.col03} style={{ marginTop: '24px' }}>
              <div className={css.labelDiv}>
                <label>Driver&apos;s License Number *</label>
              </div>
              <input
                type="text"
                name="licenseNumber"
                placeholder="Enter License Number"
                value={formik.values.licenseNumber ?? ''}
                onChange={formik.handleChange}
                className={css.input}
                disabled={!isLicenseHeld}
              />
              {formik.touched.licenseNumber && formik.errors.licenseNumber && (
                <div style={{ color: 'red' }}>{formik.errors.licenseNumber}</div>
              )}
            </div>

            <div className={css.col03} style={{ marginTop: '24px' }}>
              <div className={css.labelDiv}>
                <label>License Expiration Date *</label>
              </div>
              <DateInput
                selected={formik.values.licenseExpiry}
                onChange={(date) => formik.setFieldValue('licenseExpiry', date)}
                minDate={new Date()}
                disabled={!isLicenseHeld}
              />
              {formik.touched.licenseExpiry && formik.errors.licenseExpiry && (
                <div style={{ color: 'red' }}>{formik.errors.licenseExpiry}</div>
              )}
            </div>
            </div> */}

            {endorsements.length > 0 && (
              <div className={css.formRow}>
                <div className={css.labelDiv}>
                  <label>CDL Endorsements</label>
                </div>
                <ul className={css.checkboxList}>
                  {endorsements.map((endorsement) => (
                    <li key={endorsement.formValueId}>
                      <div className={css.checkBox}>
                        <input
                          type="checkbox"
                          value={endorsement.formValueId}
                          checked={formik.values.endorsements?.includes(
                            endorsement.formValueId
                          )}
                          onChange={() =>
                            handleCheckboxChange(
                              endorsement.formValueId,
                              "endorsements"
                            )
                          }
                        />
                        <span className={css.checkmark}></span>
                        <p>{endorsement.label.en}</p>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {restrictions.length > 0 && (
              <div className={css.formRow}>
                <div className={css.labelDiv}>
                  <label>CDL Restrictions</label>
                </div>
                <ul className={css.checkboxList}>
                  {restrictions.map((restriction) => (
                    <li key={restriction.formValueId}>
                      <div className={css.checkBox}>
                        <input
                          type="checkbox"
                          value={restriction.formValueId}
                          checked={formik.values.restrictions?.includes(
                            restriction.formValueId
                          )}
                          onChange={() =>
                            handleCheckboxChange(
                              restriction.formValueId,
                              "restrictions"
                            )
                          }
                        />
                        <span className={css.checkmark}></span>
                        <p>{restriction.label.en}</p>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Submit Button */}
            <div className={`${css.formRow} ${css.submitRow}`}>
              <button type="submit" className={css.submitBtn}>
                Save and Continue
              </button>
            </div>
          </>
        )}
      </form>
    </>
  );
};

export default DriverLicense;
