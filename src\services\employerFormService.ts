import { getCookie } from "cookies-next";

type EmployerResponse = {
  status: boolean;
  data: {
    currentStep: number;
    currentStage: number;
    company: {
      name: string;
      email: string;
      phoneNumber: string;
      address: {
        street: string;
        city: string;
        state: string;
        zipCode: string;
      };
      website: string;
      legalName: string;
      einNumber: string;
      usDotNumber: string;
      mcNumber: string;
      primaryOperatingAreas: string[];
      fmcsaVerified: boolean;
    };
    user: {
      contactPersonName: string;
      jobTitle: string;
    };
  };
  message: string;
};

export const getEmployerFormFields = async () => {
    const apiUrl = `${process.env.NEXT_PUBLIC_API_V1}form/employer`;
    try{
        const response = await fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
        });
        const data = await response.json();
        return data
    }
    catch (error){
        console.error('Failed to fetch form/employer:', error);
    }
}

export async function submitCompanyDetails(payload: object): Promise<EmployerResponse | null> {
  const token = getCookie("authToken");

  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_V1}/employer/company-details`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `${token}`,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data as EmployerResponse;
  } catch (error) {
    console.error("API Error:", error);
    return null;
  }
};

// fetch form data from backend 
export interface FormValues {
  formValueId: number;
  label: {
    en: string;
    es: string;
  };
  description?: {
    en: string;
    es: string;
  };
}

interface FormFieldResponse {
  status: boolean;
  data: {
    formValues: FormValues[];
  };
}

export const getFormFieldsBySlug = async (slug: string): Promise<FormValues[]> => {
  const apiUrl = `${process.env.NEXT_PUBLIC_API_V1}form/form-fields/${slug}`;
  try {
    const res = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
 
    });

    if (!res.ok) {
      throw new Error(`Failed to fetch: ${res.statusText}`);
    }

    const json: FormFieldResponse = await res.json();
    return json.data.formValues;
  } catch (error) {
    console.error("Error fetching form fields:", error);
    return [];
  }
};


