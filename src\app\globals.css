@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');

html,
body {
  width: 100%;
  height: 100%;
}

body {
  background-color: white;
  color: black;
  font-family: "Montserrat", sans-serif;
}

* {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

ul, ol {
  list-style: none;
}


/* Basic Form -------------------------*/
.checboxGroup li {
  margin-bottom: 20px;
}

.checboxGroup li:last-child {
  margin-bottom: 0px;
}

.checkBox {
  display: flex;
  gap: 16px;
  position: relative;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  font-weight: 400;
  font-size: 16px;
  line-height: 24px;
}

.checkBox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 24px;
  width: 24px;
}

.checkBox .checkmark {
  height: 24px;
  width: 24px;
  background-color: #FFFFFF;
  border-radius: 4px;
  border: 1px solid #D6DDEB;
}

.checkBox input:checked ~ .checkmark {
  background-color: #FBD758;
  border-color: #FBD758;
}

.checkBox .checkmark:after {
  content: "";
  position: absolute;
  display: none;
  left: 9px;
  top: 5px;
  width: 5px;
  height: 10px;
  border: solid #111111;
  border-width: 0 1px 1px 0;
  -webkit-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.checkBox input:checked ~ .checkmark:after {
  display: block;
}

.react-datepicker-wrapper {
  width: 100%;
}
/* Basic Form -------------------------*/

.error_msg {
  color: #f91313;
  font-size: 12px;
  line-height: 18px;
  font-weight: 400;
  margin-top: 4px;
}

.fielderror {
  margin-top: -9px;
}
