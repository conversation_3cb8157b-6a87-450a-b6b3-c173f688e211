"use client";
// import { useParams } from "next/navigation";
import css from "./home.module.scss";
// import { getDictionary } from "@/app/[lang]/dictionaries";

const Home = () => {
  // const params = useParams();
  // const lang = (params.lang as 'en' | 'es') ?? 'en';
  // const dict = getDictionary(lang);
  return (
    <>
      <section className={css.secHome01}>
        <div className={css.container}>
          <h1>Connecting Drivers to Opportunity and Employers to Talent</h1>
          <span>Select Your Role to Get Started</span>
          <div className={css.flexBox}>
            {/* Left Column */}
            <div className={css.column}>
              <h2>
                Looking for Driver?
                <button>Browse all drivers</button>
              </h2>
              <div className={css.search}>
                <div className={css.searchfield}>
                  <img
                    src="/images/icons/search.svg"
                    alt="search"
                    className={css.searchIcon}
                  />
                  <input type="search" placeholder="Search resumes" />
                </div>
                <input type="submit" value="SEARCH" />
              </div>
            </div>

            {/* Right Column */}
            <div className={css.column}>
              <h2>
                Looking for Works?
                <button>Browse all Jobs</button>
              </h2>

              <div className={css.search}>
                <div className={css.searchfield}>
                  <img
                    src="/images/icons/search.svg"
                    alt="search"
                    className={css.searchIcon}
                  />
                  <input type="search" placeholder="Search jobs" />
                </div>
                <input type="submit" value="SEARCH" />
              </div>
            </div>
          </div>
        </div>
        <div className={css.sideIconContainer}>
          {" "}
          <img
            src="/images/vector@1x (1) 2.svg "
            alt="icon"
            className={css.sideIcon}
          />
        </div>
      </section>

      {/* Section 2 */}
      <section className={css.secHome02}>
        <div className={css.container}>
          <ul>
            <li>
              <figure>
                <img src="/images/icons/icon-jobs.svg" alt="icon-jobs.svg" />
              </figure>
              <div className={css.jobsDetails}>
                <h6>1,75,324</h6>
                <span>Live Job</span>
              </div>
            </li>
            <li>
              <figure>
                <img
                  src="/images/icons/icon-company.svg"
                  alt="icon-company.svg"
                />
              </figure>
              <div className={css.jobsDetails}>
                <h6>97,354</h6>
                <span>Companies</span>
              </div>
            </li>
            <li>
              <figure>
                <img
                  src="./images/icons/icon-candidates.svg"
                  alt="icon-candidates.svg"
                />
              </figure>
              <div className={css.jobsDetails}>
                <h6>38,47,154</h6>
                <span>Candidates</span>
              </div>
            </li>
            <li>
              <figure>
                <img src="./images/icons/icon-jobs.svg" alt="icon-jobs.svg" />
              </figure>
              <div className={css.jobsDetails}>
                <h6>7,532</h6>
                <span>New Jobs</span>
              </div>
            </li>
          </ul>
        </div>
      </section>

      {/* Section 3 */}
      <section className={css.secHome03}>
        <div className={css.container}>
          <aside className={css.aside}>
            <h3>Driver : 1 Profile, Infinite Jobs</h3>
            <ul>
              <li>
                <img src="./images/icons/done_all.svg" alt="done_all.svg" />
                <span>Employer gets Licenses, MYR, work history, & more</span>
              </li>
              <li>
                <img src="./images/icons/done_all.svg" alt="done_all.svg" />
                <span>Apply once share everywhere</span>
              </li>
              <li>
                <img src="./images/icons/done_all.svg" alt="done_all.svg" />
                <span>Efficiency for drivers, effectiveness for employers</span>
              </li>
            </ul>
          </aside>
          <aside className={css.aside}>
            <h3>Employer : 1 Profile, Infinite Jobs</h3>
            <ul>
              <li>
                <img src="./images/icons/done_all.svg" alt="done_all.svg" />
                <span>Access complete driver histories</span>
              </li>
              <li>
                <img src="./images/icons/done_all.svg" alt="done_all.svg" />
                <span>Download pre-built DOT packets</span>
              </li>
              <li>
                <img src="./images/icons/done_all.svg" alt="done_all.svg" />
                <span>Post Jobs with Detailed Requriments</span>
              </li>
            </ul>
          </aside>
        </div>
      </section>

      {/* Section 4 */}
      <section className={css.secHome04}>
        <div className={css.container}>
          <aside className={css.aside01}>
            <h3>START IN 2 MINUTES</h3>
            <div className={css.flexBox}>
              <div className={css.column}>
                <h4>Drivers</h4>
                <ul>
                  <li>
                    <img src="./images/icons/done_all.svg" alt="done_all.svg" />
                    <span>Free profile creation</span>
                  </li>
                  <li>
                    <img src="./images/icons/done_all.svg" alt="done_all.svg" />
                    <span>Job-match alerts</span>
                  </li>
                </ul>
                <button type="button" className={css.register}>
                  Register
                </button>
              </div>
              <div className={css.column}>
                <h4>Employer</h4>
                <ul>
                  <li>
                    <img src="./images/icons/done_all.svg" alt="done_all.svg" />
                    <span>Free driver browsing</span>
                  </li>
                  <li>
                    <img src="./images/icons/done_all.svg" alt="done_all.svg" />
                    <span>3 free job posts</span>
                  </li>
                </ul>
                <button type="button" className={css.register}>
                  Register
                </button>
              </div>
            </div>
          </aside>
          <aside className={css.aside02}>
            <img
              src="/images/delivery-vehicles.png"
              alt="delivery-vehicles.png"
            />
          </aside>
        </div>
      </section>

      {/* Section 5 */}
      <section className={css.secHome05}>
        <div className={css.container}>
          <h2>HOW IT WORKS?</h2>
          <div className={css.flexBox}>
            <aside className={css.aside}>
              <h3>DRIVERS : 3 STEPS</h3>
              <ul>
                <li>
                  <figure>
                    <img
                      src="./images/icons/icon-Build Profile.svg"
                      alt="icon-Build Profile.svg"
                    />
                  </figure>
                  <div>
                    <h6>Build Profile</h6>
                    <span>Upload licenses/MVR and work history</span>
                  </div>
                </li>
                <li>
                  <figure>
                    <img
                      src="./images/icons/icon-Apply-Share.svg"
                      alt="icon-Apply-Share.svg"
                    />
                  </figure>
                  <div>
                    <h6>Apply & Share</h6>
                    <span>
                      Click applications and share via link or QR Code
                    </span>
                  </div>
                </li>
                <li>
                  <figure>
                    <img
                      src="./images/icons/icon-Get-Hired.svg"
                      alt="icon-Get-Hired.svg"
                    />
                  </figure>
                  <div>
                    <h6>Get Hired</h6>
                    <span>Track progress in dashboard</span>
                  </div>
                </li>
              </ul>
            </aside>
            <aside className={css.aside}>
              <h3>EMPLOYER : 3 STEPS</h3>
              <ul>
                <li>
                  <figure>
                    <img
                      src="./images/icons/icon-Browse.svg"
                      alt="icon-Browse.svg"
                    />
                  </figure>
                  <div>
                    <h6>Browse</h6>
                    <span>Filter by CDL experience, MVR status</span>
                  </div>
                </li>
                <li>
                  <figure>
                    <img
                      src="./images/icons/icon-Download.svg"
                      alt="icon-Download.svg"
                    />
                  </figure>
                  <div>
                    <h6>Download</h6>
                    <span>Get full DOT-compliant profile package</span>
                  </div>
                </li>
                <li>
                  <figure>
                    <img
                      src="./images/icons/icon-Hire.svg"
                      alt="icon-Hire.svg"
                    />
                  </figure>
                  <div>
                    <h6>Hire</h6>
                    <span>Send offer, skip paperwork</span>
                  </div>
                </li>
              </ul>
            </aside>
          </div>
        </div>
      </section>
      <div className={css.sideIconContainer}>
        {" "}
        <img
          src="/images/vector@1x (1) 2.svg "
          alt="icon"
          className={css.sideIcon}
        />
      </div>
    </>
  );
};

export default Home;
