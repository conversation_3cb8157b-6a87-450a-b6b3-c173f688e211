import { useEffect, useRef, useState } from "react";
import css from "../../Employer02/employerRegistration.module.scss";
import { useFormik } from "formik";
import Link from "next/link";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { submitCompanyDetails } from "@/services/employerFormService";
import { CompanyValues } from "../types";

// Regex patterns
const zipCodePattern = /^\d{5}$/;
const einPattern = /^\d{2}-\d{7}$/;
const dotNumberPattern = /^\d{7}$/;
const phoneCodePattern = /^\d{10}$/;

const companyValidationSchema = Yup.object().shape({
  name: Yup.string().required("Company name is required"),
  // email: Yup.string().email("Invalid email format"),
  phoneNumber: Yup.string()
    .matches(phoneCodePattern, "Phone number must be 10 digits")
    .required("Phone number is required"),
  address: Yup.object().shape({
    street: Yup.string().required("Street address is required"),
    city: Yup.string().required("City is required"),
    state: Yup.string().required("State is required"),
    zipCode: Yup.string()
      .matches(zipCodePattern, "Zip code must be 5 digits")
      .required("Zip code is required"),
  }),
  // website: Yup.string().url("Enter a valid URL").notRequired(),
  website: Yup.string()
    .transform((value) => (value === "" ? null : value)) // convert empty string to null
    .nullable() // allow null as valid value
    .notRequired()
    .url("Enter a valid URL"),

  legalName: Yup.string().required("Legal business name is required"),
  einNumber: Yup.string()
    .matches(einPattern, "EIN must be in the format XX-XXXXXXX")
    .required("EIN is required"),

  // usDotNumber: Yup.string()
  //     .transform((value) => (value === "" ? null : value))
  //     .matches(dotNumberPattern, "US DOT Number must be 7 digits")
  //     .nullable()
  //     .when("noDotOrMcNumber", {
  //         is: false,
  //         then: (schema) => schema.required("US DOT Number is required"),
  //         otherwise: (schema) => schema.notRequired(),
  //     }),


//   mcNumber: Yup.string()
//     .transform((value) => (value === "" ? null : value))
//     .nullable()
//     .notRequired(),

//   noDotOrMcNumber: Yup.boolean().notRequired(),
//   // }),
usDotNumber: Yup.string()
    .transform((value) => (value === "" ? null : value))
    .nullable()
    .when("noDotOrMcNumber", {
      is: false,
      then: (schema) =>
        schema
          .required("US DOT Number is required")
          .matches(dotNumberPattern, "US DOT Number must be 7 digits"),
      otherwise: (schema) => schema.notRequired(),
    }),

  mcNumber: Yup.string()
    .transform((value) => (value === "" ? null : value))
    .nullable()
    .notRequired(),

  noDotOrMcNumber: Yup.boolean()
    .oneOf([true, false])
    .test(
      "only-if-both-empty",
      "You can only check this if DOT and MC Number are both empty",
      function (value) {
        const { usDotNumber, mcNumber } = this.parent;
        if (value === true && (!!usDotNumber || !!mcNumber)) {
          return false;
        }
        return true;
      }
    ),
});

type CompanyInformationProps = {
  onFormSubmit: (data: CompanyValues) => void;
  formInitialValues: CompanyValues;
  currentStep: number;
  setIsFirstAccordionComplete: (val: boolean) => void;
  setIsFirstAccordionOpen: (val: boolean) => void;
  setIsSecondAccordionOpen: (val: boolean) => void;
};

const CompanyInformation: React.FC<CompanyInformationProps> = ({
  onFormSubmit,
  formInitialValues,
  currentStep,
  setIsFirstAccordionComplete,
  setIsFirstAccordionOpen,
  setIsSecondAccordionOpen,
}) => {
  const [isEditable, setIsEditable] = useState({
    name: false,
    phoneNumber: false,
    city: false,
    state: false,
  });
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    // cleanup
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  type EditableField = keyof typeof isEditable;

  const toggleEdit = (field: EditableField) => {
    setIsEditable((prev) => ({ ...prev, [field]: !prev[field] }));
  };

  const formikCompany = useFormik<CompanyValues>({
    initialValues: formInitialValues,
    enableReinitialize: true,
    validationSchema: companyValidationSchema,
    onSubmit: async (values) => {
      const companyData = {
        ...values,
        website: values.website === "" ? null : values.website,
        usDotNumber: values.usDotNumber === "" ? null : values.usDotNumber,
        mcNumber: values.mcNumber === "" ? null : values.mcNumber,
      };

      const step = 1;
      const payload = {
        currentStage: 2,
        ...(step >= currentStep && { currentStep: step }),
        company: companyData,
      };
      const success = await submitCompanyDetails(payload);
      if (success) {
        onFormSubmit(companyData);
        toast.success("Company information submitted");
        setTimeout(() => {
          setIsFirstAccordionComplete(true);
          setIsFirstAccordionOpen(false);
          setIsSecondAccordionOpen(true);
        }, 2000);
      } else {
        toast.error("Failed to submit company information");
      }
    },
  });

  return (
    <>
      <form onSubmit={formikCompany.handleSubmit} className={css.commonForm}>
        <div className={css.formRow}>
          <div className={css.col03}>
            <div className={css.labelDiv}>
              <label>Company Name</label>
              <button type="button" onClick={() => toggleEdit("name")}>
                <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
              </button>
            </div>
            <input
              type="text"
              name="name"
              placeholder="Example Delivery Co"
              value={formikCompany.values.name}
              onChange={formikCompany.handleChange}
              disabled={!isEditable.name}
            />
            {formikCompany.touched.name && formikCompany.errors.name && (
              <div className={css.error}>{formikCompany.errors.name}</div>
            )}
          </div>

          <div className={css.col03}>
            <div className={css.labelDiv}>
              <label>Company Email Address</label>
            </div>
            <input
              type="text"
              name="email"
              placeholder="<EMAIL>"
              value={formikCompany.values.email}
              disabled
            />
          </div>

          <div className={css.col03}>
            <div className={css.labelDiv}>
              <label>
                Company Phone Number
                <span className={css.tooltipIcon}>
                  <img src="/images/icons/icon-info.svg" alt="" />
                  <span className={css.tooltip}>
                    Main company line or HR/Recruiting dept. A verification code
                    may be sent later.
                  </span>
                </span>
              </label>
              <button type="button" onClick={() => toggleEdit("phoneNumber")}>
                <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
              </button>
            </div>
            <input
              type="text"
              name="phoneNumber"
              placeholder="(*************"
              maxLength={10}
              value={formikCompany.values.phoneNumber}
              onChange={formikCompany.handleChange}
              disabled={!isEditable.phoneNumber}
            />
            {formikCompany.touched.phoneNumber &&
              formikCompany.errors.phoneNumber && (
                <div className={css.error}>
                  {formikCompany.errors.phoneNumber}
                </div>
              )}
          </div>

          <div className={css.col03}>
            <div className={css.labelDiv}>
              <label>
                Company Full Address <sup>*</sup>
              </label>
            </div>
            <input
              type="text"
              name="address.street"
              placeholder="Enter Street Address"
              value={formikCompany.values.address.street}
              onChange={formikCompany.handleChange}
            />
            {formikCompany.touched.address?.street &&
              formikCompany.errors.address?.street && (
                <div className={css.error}>
                  {formikCompany.errors.address.street}
                </div>
              )}
          </div>

          <div className={css.col03}>
            <div className={css.labelDiv}>
              <button type="button" onClick={() => toggleEdit("city")}>
                <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
              </button>
            </div>
            <input
              type="text"
              name="address.city"
              placeholder="Anytown"
              value={formikCompany.values.address.city}
              onChange={formikCompany.handleChange}
              disabled={!isEditable.city}
            />
            {formikCompany.touched.address?.city &&
              formikCompany.errors.address?.city && (
                <div className={css.error}>
                  {formikCompany.errors.address.city}
                </div>
              )}
          </div>

          <div className={css.col03}>
            <div className={css.labelDiv}>
              <button type="button" onClick={() => toggleEdit("state")}>
                <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
              </button>
            </div>
            <div className={css.dropdown} ref={dropdownRef}>
              {/* Dropdown button is disabled if not editable */}
              <button
                type="button"
                className={css.dropdownToggle}
                disabled={!isEditable.state}
                onClick={() => setIsDropdownOpen((prev) => !prev)}
              >
                {formikCompany.values.address.state || "Select State"}
              </button>

              {/* Show dropdown menu only if editable */}
              {isEditable.state && isDropdownOpen && (
                <div className={css.dropdownMenu}>
                  {["AL", "AK", "AZ", "AR", "CA", "CO"].map((stateAbbr) => (
                    <button
                      type="button"
                      key={stateAbbr}
                      className={css.dropdownItem}
                      onClick={() => {
                        formikCompany.setFieldValue("address.state", stateAbbr);
                        setIsDropdownOpen(false);
                      }}
                    >
                      {stateAbbr}
                    </button>
                  ))}
                </div>
              )}
            </div>
            {formikCompany.touched.address?.state &&
              formikCompany.errors.address?.state && (
                <div className={css.error}>
                  {formikCompany.errors.address.state}
                </div>
              )}
          </div>

          <div className={css.col03}>
            <input
              type="text"
              name="address.zipCode"
              placeholder="Enter Zip Code"
              value={formikCompany.values.address.zipCode}
              onChange={formikCompany.handleChange}
            />
          </div>
          {formikCompany.touched.address?.zipCode &&
            formikCompany.errors.address?.zipCode && (
              <div className={css.error}>
                {formikCompany.errors.address.zipCode}
              </div>
            )}
        </div>

        <div className={css.formRow}>
          <div className={css.col03}>
            <div className={css.labelDiv}>
              <label>
                Company Website <span>(Optional, but Recommended)</span>
              </label>
            </div>
            <input
              type="text"
              name="website"
              placeholder="https://www.yourcompany.com"
              value={formikCompany.values.website || ""}
              onChange={formikCompany.handleChange}
            />
            {formikCompany.touched.website && formikCompany.errors.website && (
              <div className={css.error}>{formikCompany.errors.website}</div>
            )}
          </div>

          <div className={css.col03}>
            <div className={css.labelDiv}>
              <label>
                Legal Business Name
                <span className={css.tooltipIcon}>
                  <img src="/images/icons/icon-info.svg" alt="" />
                  <span className={css.tooltip}>
                    Enter the exact legal name registered with the IRS and your
                    state.
                  </span>
                </span>
              </label>
            </div>
            <input
              type="text"
              name="legalName"
              placeholder="Enter Official Legal Company Name"
              value={formikCompany.values.legalName}
              onChange={formikCompany.handleChange}
            />
            {formikCompany.touched.legalName &&
              formikCompany.errors.legalName && (
                <div className={css.error}>
                  {formikCompany.errors.legalName}
                </div>
              )}
          </div>

          <div className={css.col03}>
            <div className={css.labelDiv}>
              <label>
                Employer Identification Number (EIN) <sup>*</sup>
              </label>
            </div>
            <input
              type="text"
              name="einNumber"
              placeholder="XX-XXXXXXX"
              value={formikCompany.values.einNumber}
              onChange={formikCompany.handleChange}
            />
            {formikCompany.touched.einNumber &&
              formikCompany.errors.einNumber && (
                <div className={css.error}>
                  {formikCompany.errors.einNumber}
                </div>
              )}
          </div>
        </div>

        <div className={css.formRow}>
          <div className={css.col03}>
            <div className={css.labelDiv}>
              <label>
                US DOT Number <span>(If applicable)</span>
                <span className={css.tooltipIcon}>
                  <img src="/images/icons/icon-info.svg" alt="" />
                  <span className={css.tooltip}>
                    Providing this allows for faster verification if you are a
                    registered carrier.
                  </span>
                </span>
              </label>
            </div>
            <input
              type="text"
              name="usDotNumber"
              placeholder="Enter 7-digit DOT Number"
              value={formikCompany.values.usDotNumber ?? ""}
              onChange={formikCompany.handleChange}
            />
        
            <span className={css.FMCSA}>
              Check&nbsp;
              <Link href="https://safer.fmcsa.dot.gov/">FMCSA SAFER</Link>
              &nbsp;website
            </span>
            {formikCompany.touched.usDotNumber &&
              formikCompany.errors.usDotNumber && (
                <div className={css.error}>
                  {formikCompany.errors.usDotNumber}
                </div>
                
              )}
            
            
          </div>
            
          <div className={css.col03}>
            <div className={css.labelDiv}>
              <label>
                MC Number (Motor Carrier) <span>(If applicable)</span>
              </label>
            </div>
            <input
              type="text"
              name="mcNumber"
              placeholder="Enter MC Number (if applicable)"
              value={formikCompany.values.mcNumber ?? ""}
              onChange={formikCompany.handleChange}
            />
          </div>
          <div className={css.col03}>
            <div className={`${css.checkBox} ${css.mt36}`}>
              <label>
                <input
                  type="checkbox"
                  name="noDotOrMcNumber"
                  checked={formikCompany.values.noDotOrMcNumber}
                //   onChange={formikCompany.handleChange}

  onChange={(e) => {
    // Allow manual uncheck any time
    const isChecked = e.target.checked;

    // Only allow check if both fields are empty
    if (
      isChecked &&
      (formikCompany.values.usDotNumber || formikCompany.values.mcNumber)
    ) {
      return; // Don't allow check if DOT or MC is filled
    }

    formikCompany.setFieldValue("noDotOrMcNumber", isChecked);
  }}


              
                />
                <span className={css.checkmark}></span>
                <p>I don&apos;t have a US DOT or MC Number</p>
              </label>
              {formikCompany.touched.noDotOrMcNumber &&
                formikCompany.errors.noDotOrMcNumber && (
                  <div className={css.error}>
                    {formikCompany.errors.noDotOrMcNumber}
                  </div>
                )}
            </div>
          </div>
        </div>

        <div className={`${css.formRow} ${css.submitRow}`}>
          <button type="submit" className={css.submitBtn}>
            Save and Continue
          </button>
        </div>
      </form>
    </>
  );
};
export default CompanyInformation;
