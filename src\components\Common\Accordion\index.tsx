import React, { ReactNode } from "react";
import css from "./accordion.module.scss";

type AccordionWrapperProps = {
  title: string;
  isOpen: boolean;
  setIsOpen: (open: boolean) => void;
  isComplete?: boolean;
  children: ReactNode;
  onClickHeader?: () => void;
  isClickable?: boolean;
};

const AccordionWrapper: React.FC<AccordionWrapperProps> = ({
  title,
  isOpen,
  setIsOpen,
  isComplete = false,
  children,
  onClickHeader,
  isClickable = true,
}) => {
  const handleClick = () => {
    if (!isClickable) return;
    setIsOpen(!isOpen);
    onClickHeader?.();
  };

  return (
    <div className={`${css.accordion} ${isOpen ? css.active : ""}`}>
      <div
        className={css.accordionTop}
        onClick={handleClick}
        style={{ cursor: isClickable ? "pointer" : "not-allowed" }}
      >
        <h3>{title}</h3>

        {isComplete && !isOpen && (
          <img src="/images/icons/icon-step-complete.svg" alt='completed'/>
        )}

        <img
          src="/images/icons/icon-down-arrow.svg"
          alt="icon-down-arrow"
          className={`${css.icon} ${isOpen ? css.open : ""}`}
        />
      </div>

      {isOpen && <div className={css.accordionBody}>{children}</div>}
    </div>
  );
};

export default AccordionWrapper;
