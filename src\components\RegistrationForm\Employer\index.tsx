// "use client";

// import css from "./employer.module.scss";
// import Steps from "../Steps";
// import { useFormik } from "formik";
// import BasicFormStepContainer from "@/components/Common/Form/BasicFormStepContainer";
// import { useMemo } from "react";
// import { FormData, FormikFieldValues } from "@/types/form";

// interface EmployerRegistrationProps {
//   formData: FormData;
// }

// const EmployerRegistration: React.FC<EmployerRegistrationProps> = ({ formData }) => {
//   const formSteps = formData;

//   const initialValues = useMemo(() => {
//     const values: { [key: string]: string } = {};
//     formSteps?.forEach((step) => {
//       step?.formFields?.forEach((field) => {
//         values[field.columnName] = "";
//       });
//     });
//     return values;
//   }, [formSteps]);

//   const formik = useFormik<FormikFieldValues>({
//     initialValues,
//     onSubmit: (values) => {
//       console.log("Submitted Values:", values);
//     },
//     enableReinitialize: true,
//   });

//   return (
//     <div className={css.employer}>
//       <Steps />
//       <section className={css.FormSection}>
//         <div className={css.container}>
//           <div className={css.wrapper}>
//             <form onSubmit={formik.handleSubmit}>
//               {formSteps?.map((step, index) => (
//                 <BasicFormStepContainer
//                   key={index}
//                   stepData={step}
//                   formik={formik}
//                 />
//               ))}
//               <div className={css.buttons}>
//                 <button type="submit">Submit</button>
//               </div>
//             </form>
//           </div>
//         </div>
//       </section>
//     </div>
//   );
// };

// export default EmployerRegistration;
