import { QualificationForm } from "@/types/jobpostingform";
import { removeAllowedFields } from "@/utils/utils";

export const qualifyFormPayload = (
  values: QualificationForm,
  isDraft: boolean
) => {
  const cloneObject = JSON.parse(JSON.stringify(values)) as QualificationForm;

  const jobPost: Record<string, unknown> = {
    endorsements: cloneObject?.endorsements?.map(String),
    preferredEquipment: cloneObject?.preferredEquipment?.map(String),
    preferredRoutes: cloneObject?.preferredRoutes?.map(String),
    screeningChecks: cloneObject?.screeningChecks?.map(String),
  };

  delete cloneObject.physicalReq;
  delete cloneObject.drivingReq;

  if (cloneObject.cdlClass) {
    jobPost.cdlClass = cloneObject.cdlClass;
  }

  if (cloneObject.experienceMonths) {
    jobPost.experienceMonths = cloneObject.experienceMonths;
  }

  if (cloneObject.minAge) {
    jobPost.minAge = cloneObject.minAge;
  }

  if (cloneObject.dotMedicalCard) {
    jobPost.dotMedicalCard = cloneObject.dotMedicalCard;
  }

  if (cloneObject.airbrakeCertRequired) {
    jobPost.airbrakeCertRequired = cloneObject.airbrakeCertRequired;
  }

  if (cloneObject.twicCardRequired) {
    jobPost.twicCardRequired = cloneObject.twicCardRequired;
  }

  if (cloneObject.passportRequired) {
    jobPost.passportRequired = cloneObject.passportRequired;
  }

  if (cloneObject.isOtherRequirements) {
    jobPost.isOtherRequirements = cloneObject.isOtherRequirements;

    if (
      cloneObject?.otherRequirements.includes("other") &&
      cloneObject.otherRequirementsText
    ) {
      cloneObject.otherRequirements.push(cloneObject?.otherRequirementsText);
    }
    jobPost.otherRequirements = cloneObject.otherRequirements
      .filter((item) => item !== "other")
      .map(String);
  }

  if (cloneObject.willingToTrain) {
    jobPost.willingToTrain = cloneObject.willingToTrain;
    if (cloneObject?.trainingProgram)
      jobPost.trainingProgram = cloneObject?.trainingProgram;
  }

  if (
    cloneObject?.physicalRequirements.includes("other") &&
    cloneObject.otherPhysicalRequirements
  ) {
    cloneObject.physicalRequirements.push(
      cloneObject?.otherPhysicalRequirements
    );
  }

  if (
    cloneObject.physicalRequirements.includes("radioBtn") &&
    cloneObject.physicalLiftingLimit
  ) {
    jobPost.physicalLiftingLimit = cloneObject.physicalLiftingLimit;
  }

  jobPost.physicalRequirements = cloneObject.physicalRequirements
    .filter((item) => item !== "other" && item !== "radioBtn")
    .map(String);

  if (cloneObject?.drivingRecordOther) {
    jobPost.drivingRecordOther = cloneObject?.drivingRecordOther;
  }

  if (cloneObject.driverLanguages.length > 0) {
    jobPost.driverLanguages = cloneObject.driverLanguages;
  }

  jobPost.driverLanguages = cloneObject.driverLanguages;

  const drivingRecordPayload = removeAllowedFields(cloneObject);

  return {
    currentStep: 4,
    currentStepStatus: isDraft ? "DRAFT" : "COMPLETED",
    jobPost: {
        ...jobPost,
        ...drivingRecordPayload,
    },
  };
};
