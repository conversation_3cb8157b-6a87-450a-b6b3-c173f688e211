import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import {
  fetchLanguages,
  fetchOtherLanguages,
  FormValue,
  submitDriverDetails,
} from "@/services/driverFormService";
import { toast } from "react-toastify";
import { LanguageSkillsType } from "../types";
import * as Yup from "yup";
import css from "../driverRegistration.module.scss";
// import { FaLeaf } from "react-icons/fa";

export enum ProficiencyLevel {
  ELEMENTARY = "Elementary",
  LIMITED_WORKING = "Limited Working",
  PROFESSIONAL_WORKING = "Professional Working",
  FULL_PROFESSIONAL = "Full Professional",
  NATIVE_BILINGUAL = "Native/Bilingual" ,
}

interface LanguageSkillItem {
  language: string;
  proficiency: string;
  otherLanguage: string;
  driverLanguageId?: number | null;
}

interface DriverLanguagePayload {
  languageId: string | number;
  proficiency: string;
  rank: number;
  driverLanguageId?: number;
}
interface Props {
  onFormSubmit: () => void;
  initialData: LanguageSkillsType | null;
  currentStep:number
  currentStage:number
}
const LanguageSkills = ({ onFormSubmit, initialData,currentStep,currentStage }: Props) => {
  const [languages, setLanguages] = useState<FormValue[]>([]);
  const [otherLanguages, setOtherLanguages] = useState<FormValue[]>([]);
  const proficiencyOptions = Object.entries(ProficiencyLevel);
  const [openLangIndex, setOpenLangIndex] = useState<number | null | false>(null);
  const [openProfIndex, setOpenProfIndex] = useState<number | null |false>(null);
  const[loading,setLoadinng]=useState(true)

useEffect(() => {
  const handleClickOutside = (e: MouseEvent) => {
    const target = e.target as HTMLElement;
    if (!target.closest(`.${css.dropdown}`)) {
      setOpenLangIndex(null);
      setOpenProfIndex(null);
    }
  };

  document.addEventListener("mousedown", handleClickOutside);
  return () => document.removeEventListener("mousedown", handleClickOutside);
}, []);

 useEffect(() => {
    const getLanguages = async () => {
      const data = await fetchLanguages();
      setLanguages(data);
      setLoadinng(false);
    };
    getLanguages();
  }, []);

  const validationSchema = Yup.object({
    languageSkills: Yup.array()
      .of(
        Yup.object({
          language: Yup.string().required("Language is required"),
          proficiency: Yup.string().required("Proficiency is required"),
          otherLanguage: Yup.string().when(["language"], {
            is: (lang: string) => lang === "Other",
            then: (schema) => schema.required("Please enter other language"),
            otherwise: (schema) => schema.notRequired(),
          }),
        })
      )
      .min(1, "At least one language is required"),
  });

  const formik = useFormik({
    initialValues: {
      languageSkills: [
        {
          language: "",
          proficiency: "",
          otherLanguage: "",
          driverLanguageId: null,
        },
      ] as LanguageSkillItem[],
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      const mappedLanguages: DriverLanguagePayload[] =
        values.languageSkills.map((item, index) => {
          const languageId =
            item.language === "Other"
              ? item.otherLanguage?.trim() || ""
              : item.language;

          const payloadItem: DriverLanguagePayload = {
            languageId,
            proficiency: item.proficiency,
            rank: index + 1, // first = primary, others = rank 2+
          };

          if (item.driverLanguageId) {
            payloadItem.driverLanguageId = item.driverLanguageId; // for update
          }

          return payloadItem;
        });
        const step =6;
        const payload = {
        ...(currentStage <= 2 && { currentStage: 2 }),
        ...(currentStage === 2 && step >= currentStep && { currentStep: step }),
        driver: {
          driverLanguages: mappedLanguages,
        },
      };

      try {
        const res = await submitDriverDetails(payload);
        toast.success("Driver language skills submitted successfully");
        onFormSubmit();
        console.log(" API Response:", res);
      } catch (err: unknown) {
        toast.error(
          err instanceof Error
            ? "Failed to submit language skills "
            : "Something went wrong "
        );
      }
    },
  });

  const { values, errors, touched, handleChange, handleSubmit, setFieldValue } =
    formik;

  useEffect(() => {
    if (initialData?.driverLanguages?.length) {
      const hasOtherLanguages = initialData.driverLanguages.length > 1;
      const hydratedValues = initialData.driverLanguages.map((item) => ({
        language:
          typeof item.languageId === "number"
            ? item.languageId.toString()
            : item.languageId,
        proficiency: item.proficiency,
        otherLanguage:
          item.languageId === "Other" ? item.languageId.toString() : "",
        driverLanguageId: item.driverLanguageId ?? null,
      }));

      formik.setValues({ languageSkills: hydratedValues });

      // Fetch otherLanguages if more than one language exists
      if (hasOtherLanguages) {
        fetchOtherLanguages().then((res) => {
          setOtherLanguages(res);
          setLoadinng(false)
        });
      }
    }
  }, [initialData]);

  return (
    
    <form onSubmit={handleSubmit} className={css.commonForm}>
      {
        loading ? (
          <p>Loading...</p>
        ):<>
         {values.languageSkills.map((language, index) => (
        <div key={index} className={`${css.formRow} ${css.flexEnd}`}>
          {/* Language Dropdown or Other */}
          <div className={css.col03}>
            <div className={css.labelDiv}>
             <label style={{ display: "block", marginBottom: "0.25rem" }}>
                   {index === 0
                     ? "Primary Language Spoken:"
                    :  <label>
            Other Language Spoken
              <span className={css.tooltipIcon}>
                <img src="/images/icons/icon-info.svg" alt="" />
                <span className={css.tooltip}>
                    Select the general geographic areas your company serves.
                </span>
              </span>
          </label>}
               </label>
               {/* <label>Primary language spoken</label> */}
            </div>
            <div className={css.dropdown}      >
              <button
                type="button"
                className={css.dropdownToggle}
                onClick={() =>
                  // setOpenLangIndex(openLangIndex === index ? null : index)
                  {
                    if(openLangIndex !== index){
                      setOpenLangIndex(index);
                      setOpenProfIndex(false);
                      
                    }else{
                      setOpenLangIndex(false)
                    }
                  }
                }
              >
                {language.language
                  ? language.language === "Other"
                    ? "Other"
                    : [...languages, ...otherLanguages].find(
                        (l) => l.formValueId.toString() === language.language
                      )?.label.en || "Select Language"
                  : "Select Language"}
              </button>

              {openLangIndex === index && (
                <div className={css.dropdownMenu}>
                  {(index === 0 ? languages : otherLanguages).map((lang) => (
                    <button
                      key={lang.formValueId}
                      type="button"
                      className={css.dropdownItem}
                      onClick={() => {
                        setFieldValue(
                          `languageSkills[${index}].language`,
                          lang.formValueId.toString()
                        );
                        setFieldValue(
                          `languageSkills[${index}].otherLanguage`,
                          ""
                        );
                        setOpenLangIndex(null);
                      }}
                    >
                      {lang.label.en}
                    </button>
                  ))}
                  <button
                    type="button"
                    className={css.dropdownItem}
                    onClick={() => {
                      setFieldValue(
                        `languageSkills[${index}].language`,
                        "Other"
                      );
                      setOpenLangIndex(null);
                    }}
                  >
                    Other
                  </button>
                </div>
              )}
            </div>

            {touched.languageSkills?.[index]?.language &&
              typeof errors.languageSkills?.[index] === "object" &&
              errors.languageSkills?.[index]?.language && (
                <div className={css.error}>
                  {errors.languageSkills[index]?.language}
                </div>
              )}

            {language.language === "Other" && (
              <input
                type="text"
                name={`languageSkills[${index}].otherLanguage`}
                placeholder="Enter other language"
                value={language.otherLanguage || ""}
                onChange={handleChange}
                style={{ marginTop: "0.25rem", display: "flex" }}
              />
            )}
            {touched.languageSkills?.[index]?.otherLanguage &&
              typeof errors.languageSkills?.[index] === "object" &&
              errors.languageSkills?.[index]?.otherLanguage && (
                <div className={css.error}>
                  {errors.languageSkills[index]?.otherLanguage}
                </div>
              )}
          </div>

          {/* Proficiency Dropdown */}
          <div className={css.col03}>
            <div className={css.labelDiv}>
              <label>Proficiency</label>
            </div>
            <div className={css.dropdown} >
              <button
                type="button"
                className={css.dropdownToggle}
                onClick={() =>
                  // setOpenProfIndex(openProfIndex === index ? null : index)
                  {
                    if(openProfIndex !==index){
                      setOpenProfIndex(index);
                      setOpenLangIndex(false);
                    }else{
                      setOpenProfIndex(false)
                    }
                  }
                }
              >
                {language.proficiency
                  ? ProficiencyLevel[
                      language.proficiency as keyof typeof ProficiencyLevel
                    ]
                  : "Select Proficiency"}
              </button>

              {openProfIndex === index && (
                <div className={css.dropdownMenu}>
                  {proficiencyOptions.map(([key, label]) => (
                    <button
                      key={key}
                      type="button"
                      className={css.dropdownItem}
                      onClick={() => {
                        setFieldValue(
                          `languageSkills[${index}].proficiency`,
                          key
                        );
                        setOpenProfIndex(null);
                      }}
                    >
                      {label}
                    </button>
                  ))}
                </div>
              )}
            </div>

            {touched.languageSkills?.[index]?.proficiency &&
              typeof errors.languageSkills?.[index] === "object" &&
              errors.languageSkills?.[index]?.proficiency && (
                <div className={css.error}>
                  {errors.languageSkills[index]?.proficiency}
                </div>
              )}
          </div>

          {/* Remove button for other languages */}
          <div className={css.col03}>
          {index === 0 && values.languageSkills.length < 5 ? (
  <button
    type="button"
    className={css.additionalBtn}
    onClick={async () => {
      if (otherLanguages.length === 0) {
        const fetched = await fetchOtherLanguages();
        setOtherLanguages(fetched);
      }
      setFieldValue("languageSkills", 
        [
           ...values.languageSkills,
        {
          language: "",
          proficiency: "",
          otherLanguage: "",
          driverLanguageId: null,
        },
       
      ]);
    }}
  >
    + Add Another Language
  </button>
) : index > 0 ? (
  <button
    type="button"
    onClick={() => {
      const updated = [...values.languageSkills];
      updated.splice(index, 1);
      setFieldValue("languageSkills", updated);
    }}
    className={css.additionalCloseBtn}
  >
    <img src="/images/icons/icon-close.svg" />
    Remove
  </button>
) : null}

          </div>
        </div>
      ))}

    {/* Submit Button */}
            <div className={`${css.formRow} ${css.submitRow}`}>
                <button type="submit" className={css.submitBtn}>
                  Save and Continue
              </button>
            </div>
        </>
      }
     
    </form>
  );
};

export default LanguageSkills;
