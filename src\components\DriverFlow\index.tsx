'use client';

import React, { useState } from 'react';
import DriverRegistration from '../Registration/Driver';
import VerificationScreen from '@/components/VerificationScreen';

interface UserData {
  userId: string;
  phone: string;
  isPhoneNumberVerified: boolean;
}
interface Props {
  lang: 'en' | 'es';
  userData: UserData | null;
}

const DriverFlow = ({ lang, userData }: Props) => {
  const [step, setStep] = useState<'register' | 'verify'>(
    userData!==null && !userData.isPhoneNumberVerified ? 'verify' : 'register'
  );

  const [localUserData, setLocalUserData] = useState<UserData | null>(userData);

  const handleRegistrationSuccess = (data: { userId: string; phone: string }) => {
    setLocalUserData({
      ...data,
      isPhoneNumberVerified: false, 
    });
    setStep('verify');
  };

  return (
    <>
      {step === 'register' && (
        <DriverRegistration onSuccess={handleRegistrationSuccess} lang={lang}/>
      )}
      {step === 'verify' && localUserData && (
        <VerificationScreen
          userId={localUserData.userId}
          phone={localUserData.phone}
        />
      )}
    </>
  );
};

export default DriverFlow;
