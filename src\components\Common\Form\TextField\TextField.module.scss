.columnField {
    display: flex;
    flex-direction: column;

    &.columnWidth_2 {
        width: calc((100% - 24px) / 2);
    }

    &.columnWidth_3 {
        width: calc((100% - 48px) / 3);
    }

    label {
        font-weight: 700;
        font-size: 14px;
        line-height: 22px;
        margin-bottom: 4px;

        .desc {
            font-weight: 400;
        }

        .important {
            color: #F91313;
        }
    }

    input {
        height: 44px;
        border-radius: 4px;
        border: 1px solid #707070;
        padding: 10px 8px;
        font-size: 14px;
        line-height: 24px;
        outline: none;
    }
}

.columnWidthAdjust {
    width: calc((100% - 36px) / 2) !important;
}

.columnWidthChange {
    width: 50% !important;
}

.columnTinyWidth {
    width: calc((100% - 48px) / 6) !important;
}

.disabled {
    input {
        background: #F7F7F7;
        cursor: not-allowed;
    }
}

.alignColumn label {
    display: flex;
    justify-content: space-between;
}

.iconWrapper {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    font-size: 13px;
    line-height: 20px;
    color: #555555;
    cursor: pointer;
}

.radioInputWidth {
    width: 40% !important;
}

.columnWidth_4 {
    width: calc((100% - 72px) / 4) !important;
}

.errorClassMargin {
    margin: 4px 0px 0px !important;
}