"use client";
import React from "react";
import { CommonDriverCategoryProvider } from "@/contexts/CommonDriverCategoryContext";
import SchoolBusDriverCategoryForm from "./SchoolBusDriverCategoryForm";

const SchoolBusDriverCategoryWrapper: React.FC = () => {
  return (
    <CommonDriverCategoryProvider categoryType="school-bus-driver">
      <SchoolBusDriverCategoryForm />
    </CommonDriverCategoryProvider>
  );
};

export default SchoolBusDriverCategoryWrapper;
