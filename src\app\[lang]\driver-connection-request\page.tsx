"use client";

import DriverConnectionConsentPrompt from "@/components/DriverConnectionPrompt";

export default function ConnectionConsentPage() {
  const handleAccept = () => {
    console.log("Connection accepted");
    // Call API or navigate
  };

  const handleCancel = () => {
    console.log("Connection declined");
    // Close or go back
  };

  return (
    <DriverConnectionConsentPrompt
      companyName="FleetX Logistics"
      companyLogoUrl="/logos/fleetx.png"
      employerName="Alex Johnson"
      onAccept={handleAccept}
      onCancel={handleCancel}
    />
  );
}
