"use client";
import { forgetPasswordUser } from "@/services/userService";
import { useFormik } from "formik";
import React from "react";
import { toast, ToastContainer } from "react-toastify";
import * as Yup from "yup";
import "react-toastify/dist/ReactToastify.css";
import css from './forgotPassword.module.scss';
import Link from "next/link";

interface FormValues {
  email: string;
}

const ForgetScreen = () => {
  const formik = useFormik({
    initialValues: {
      email: "",
    },
    validationSchema: Yup.object({
      email: Yup.string()
        .email("Invalid email address")
        .required("Email is required"),
    }),
    onSubmit: async (values: FormValues) => {
      const payload = {
        email: values.email,
      };
      try {
        const res = await forgetPasswordUser(payload);
        if (res?.status) {
          toast.success("Reset password link sent to your email");
          formik.resetForm()
        } else {
          toast.error(res?.error?.message || "Failed to send reset link");
        }
      } catch (err) {
        toast.error(`An error occurred. Please try again. ${err}`);
      }
    },
  });

  return (
    <section className={css.registrationSection}>
      <div className={css.loginWrapper}>
        <h1>Forgot Password</h1>
        <p>Please enter the email address you&apos;d like your password reset information to be sent to</p>
        <form action="" className={css.registrationForm} onSubmit={formik.handleSubmit}>
          <div className={css.formGroup}>
            <label htmlFor="email">Enter Email Address</label>
            <input
              type="text"
              id="email"
              name="email"
              placeholder="<EMAIL>"
              value={formik.values.email}
              onChange={formik.handleChange}
            />
            {formik.touched.email && formik.errors.email && (
              <div className={css.error}>{formik.errors.email}</div>
            )}
          </div>
          <div className={css.formGroup}>
            <button type="submit" className={css.submitBtn}>Request Reset Link</button>
            <p>
                Back to&nbsp;
                <Link href="/login/company" className={css.registerLink}>
                  Login
                </Link>
              </p>
          </div>
        </form>
        <ToastContainer
          position="top-right"
          autoClose={3000}
          hideProgressBar={false}
        />
      </div>
    </section>
  );
};

export default ForgetScreen;
