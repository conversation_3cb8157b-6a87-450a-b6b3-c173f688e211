'use client';

import DriverHiringPacketConsentPrompt from "@/components/DriverHiringConcentPrompt";
export default function ConsentPage() {
  const handleApprove = () => {
    console.log('Access Approved');
    // Call API or navigate
  };

  const handleDeny = () => {
    console.log('Access Denied');
    // Redirect or close
  };

  return (
    <DriverHiringPacketConsentPrompt
      companyName="FleetX Logistics"
      companyLogoUrl="/logos/fleetx.png"
      jobTitle="Class A CDL Driver"
      onApprove={handleApprove}
      onDeny={handleDeny}
    />
  );
}
