// import { FormField } from "@/types/form";
// import { FormikProps } from "formik";
// import { useParams } from "next/navigation";

//  interface DropdownProps {
//   data: FormField;
//   formik: FormikProps<Record<string, string | number | boolean>>;
//  }
// const Dropdown:React.FC<DropdownProps> = ({ data, formik }) => {
//   const params = useParams();
//   const { lang } = params as { lang: "en" | "es" };
//   const {
//     label,
//     description,
//     values
//   } = data
//   console.log('Dropdown', data)
//   return (
//     <div className="formGroup">
//       <div className="col4">
//         <label htmlFor="">{label ? label[lang] || label.en : ""}</label>
//         <span>{description ? description[lang] || description.en : ""}</span>
//       </div>
//       <div className="col5">
//         <div className="dropdown">
//           <button type="button" className="dropdownToggle">
//             1
//           </button>
//           <ul className="dropdownMenu">
//             {values?.map((value, index) => {
//               return(
//               <li key={index}>
//                 <button type="button" className="dropdownItem">
//                   {value.label ? value.label[lang] || value.label.en : ""}
//                 </button>
//               </li>
//             )})}
//           </ul>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default Dropdown;

import { FormField } from "@/types/form";
import { FormikProps } from "formik";
import { useParams } from "next/navigation";
import { useState } from "react";

interface DropdownProps {
  data: FormField;
  formik: FormikProps<Record<string, string | number | boolean>>;
}

const Dropdown: React.FC<DropdownProps> = ({ data, formik }) => {
  const params = useParams();
  const { lang } = params as { lang: "en" | "es" };

  const {
    label,
    description,
    values,
    columnName
  } = data;

  const selectedValue = formik.values[columnName];
  const [isOpen, setIsOpen] = useState(false);

  const handleSelect = (valueId: string | number) => {
    formik.setFieldValue(columnName, valueId);
    setIsOpen(false);
  };

  const selectedLabel =
    values?.find((v) => v.valueId === selectedValue)?.label[lang] ||
    values?.find((v) => v.valueId === selectedValue)?.label.en ||
    "Select...";

  return (
    <div className="formGroup">
      <div className="col4">
        <label htmlFor={columnName}>{label ? label[lang] || label.en : ""}</label>
        <span>{description ? description[lang] || description.en : ""}</span>
      </div>
      <div className="col5">
        <div className="dropdown">
          <button
            type="button"
            className="dropdownToggle"
            onClick={() => setIsOpen((prev) => !prev)}
          >
            {selectedLabel}
          </button>
          {isOpen && (
            <ul className="dropdownMenu">
              {values?.map((value, index) => (
                <li key={index}>
                  <button
                    type="button"
                    className="dropdownItem"
                    onClick={() => handleSelect(value.valueId)}
                  >
                    {value.label[lang] || value.label.en}
                  </button>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dropdown;
