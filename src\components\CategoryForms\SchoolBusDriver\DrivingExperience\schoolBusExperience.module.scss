@import '../../../../styles/global.scss';

.schoolBusExperience {
    h3 {
        color: $black;
        font-weight: 600;
        font-size: 18px;
        line-height: 24px;

        sup {
            color: #ff0000;
        }
    }

    h5 {
        color: $black;
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
        margin-top: 12px;
    }

    .required {
        color: $black;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        margin-top: 16px;
        margin-bottom: 24px;

        sup {
            color: #ff0000;
        }
    }

    .card {
        border: 1px solid #DDDDDD;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 28px;

        h3 {
            margin-bottom: 16px;
        }

        h5 {
            margin-bottom: 16px;
        }
    }

    .formRow {
        display: flex;
        flex-wrap: wrap;
        column-gap: 24px;
        row-gap: 28px;
        margin-bottom: 28px;
        position: relative;

        &:last-child {
            margin-bottom: 0px;
        }

        &.dBlaco {
            display: block;
        }

        .col02 {
            width: calc((100% - 24px) / 2);
        }

        .error {
            color: #F91313;
            font-size: 12px;
            line-height: 18px;
            font-weight: 400;
            margin-top: 4px;
        }
    }

    label {
        display: block;
        color: $black;
        font-weight: 700;
        font-size: 14px;
        line-height: 22px;
        margin-bottom: 16px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        span {
            font-weight: 400;
        }

        sup {
            color: $red;
            line-height: 0px;
        }

        .tooltipIcon {
            width: 22px;
            height: 22px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin-left: 2px;
            cursor: pointer;

            &:hover {
            .tooltip {
                display: block;
            }
            }
        }

        .tooltip {
            background-color: #1E1E1E;
            border-radius: 4px;
            color: $white;
            font-size: 14px;
            line-height: 24px;
            text-align: center;
            max-width: 330px;
            padding: 8px;
            width: 84vw;
            position: absolute;
            left: 50%;
            bottom: calc(100% + 10px);
            transform: translateX(-50%);
            display: none;

            &:after {
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid #1E1E1E;
            content: "";
            position: absolute;
            left: 50%;
            bottom: -9px;
            transform: translateX(-50%);
            }
        }
    }

    .checkBox {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        &.column {
            flex-direction: column;
        }

        li {
            position: relative;
            user-select: none;
        }

        input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 24px;
            width: 24px;
            z-index: 3;

            &:checked {
                ~.checkmark {
                    background-color: #555555;
                    border-color: #555555;

                    &:after {
                        display: block;
                    }
                }
            }
        }

        .checkmark {
            height: 20px;
            width: 20px;
            background-color: #FFFFFF;
            border-radius: 2px;
            border: 2px solid #555555;
            position: absolute;
            left: 0px;
            top: 2px;

            &:after {
                content: "";
                position: absolute;
                display: none;
                left: 5px;
                top: 0px;
                width: 5px;
                height: 10px;
                border: solid $white;
                border-width: 0 1px 1px 0;
                -webkit-transform: rotate(45deg);
                -ms-transform: rotate(45deg);
                transform: rotate(45deg);
            }
        }

        p {
            color: #555555;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            padding-left: 28px;
            margin: 0px;
            text-align: left;

            a {
                color: #0075F2;
            }
        }
    }

    .btnGroup {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-top: 50px;

        button {
            border-radius: 8px;
            border: 1px solid;
            cursor: pointer;
            font-weight: 500;
            font-size: 18px;
            line-height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            height: 50px;

            &.back {
                background-color: $white;
                border-color: #555555;
                color: $black;
                width: 120px;
            }

            &.exit {
                background-color: #555555;
                border-color: #555555;
                color: $white;
                width: 220px;
                margin-left: auto;
            }

            &.continue {
                background-color: $secondary;
                border-color: $secondary;
                color: $black;
                width: 220px;
            }
        }
    }
}