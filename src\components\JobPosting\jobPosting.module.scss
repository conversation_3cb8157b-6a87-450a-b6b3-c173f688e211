@import "../../styles/global.scss";

.formStepContainer {
    padding: 28px 100px 74px;

    .sectionWrapper {
        .companyWrapper {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            box-shadow: 0px 4px 4px 0px #0000001A;
            margin: 0px -100px;
            padding: 0px 100px 28px;

            .companyDetails {
                display: flex;
                align-items: center;
                gap: 16px;

                h3 {
                    font-size: 20px;
                    margin-bottom: 8px;
                }

                p {
                    font-size: 12px;
                }
            }

            .locationDetails {
                display: flex;
                align-items: center;
                gap: 12px;

                p {
                    font-size: 12px;
                }
            }
        }

        .title {
            font-size: 16px;
            margin-bottom: 8px;
            padding-top: 24px;
        }

        .description {
            font-size: 12px;
            line-height: 16px;
            color: #555555;
        }

        .stepContainer {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            margin: 28px 0 16px;

            .tickIconRow {
                position: relative;
                display: flex;
                align-items: center;
                width: 100%;

                .stepWrapper {
                    position: relative;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 2;

                    .step {
                        display: flex;
                        width: 28px;
                        height: 28px;
                        align-items: center;
                        justify-content: center;
                        border-radius: 50%;
                        border: 1px solid #E5E5E5;
                        background-color: #E5E5E5;
                        color: #9D9D9D;
                        font-weight: 500;
                        font-size: 14px;
                        line-height: 16px;
                        transition: all 0.3s ease;
                    }
                }

                .connectLine {
                    flex: 1;
                    height: 2px;
                    background-color: #E5E5E5;
                    z-index: 1;

                    &.connectActive {
                        background-color: #1D831B;
                    }
                }
            }

            .labelRow {
                display: flex;
                width: 100%;
                margin-top: 6px;
            }

            .label {
                flex: 1;
                text-align: left;
                font-size: 14px;
                line-height: 16px;
                margin-right: 24px;
                font-weight: 500;
                color: #9D9D9D;
            }

            .labelColor {
                color: #555555;
            }
        }

        .notes {
            font-size: 14px;
            line-height: 24px;
            margin-bottom: 32px;

            span {
                color: $red;
            }
        }
    }
}

.horizontalLine {
    height: 6px;
    background: #F4F4F4;
    border: 1px solid #F4F4F4;
    margin: 40px -100px;
}

.basicFormInfo,
.payStructureInfo {
    .heading {
        margin-bottom: 24px;
        font-weight: 700;

        span {
            font-weight: 400;
        }
    }

    .rowField {
        display: flex;
        flex-wrap: wrap;
        column-gap: 24px;
        margin-bottom: 28px;

        &.marginAdjust {
            margin-bottom: 0px;
        }
    }

    .radioMargin {
        margin-top: 16px;
    }

    .btnContainer {
        display: flex;
        justify-content: space-between;

        .backBtn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 120px;
            height: 50px;
            padding: 12px;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 500;
            margin-top: 50px;
            border: 1px solid #555555;
            background-color: #FFFFFF;
            gap: 12px;
        }
    }

    .btnWrapper {
        display: flex;
        justify-content: right;
        column-gap: 20px;
        margin-top: 50px;

        .draftBtn,
        .saveBtn {
            width: 220px;
            height: 50px;
            padding: 12px;
            border-radius: 8px;
            font-size: 18px;
            font-weight: 500;
        }

        .draftBtn {
            border: 1px solid #555555;
            background-color: #555555;
            color: #FFFFFF;
        }

        .saveBtn {
            border: 1px solid #FBD758;
            background-color: #FBD758;
        }
    }
}

.inputCheckbox {
    display: flex;
    gap: 16px !important;
    align-items: baseline;

    div:first-child {
        margin-right: 41px;
    }

    &.adjustMargin {
        div:first-child {
            margin-right: 0px !important;
        }
    }
}

.columnInputGap {
    display: flex;
    gap: 53px;
}

.tooltipLabel {
    label {
        white-space: nowrap;
        text-overflow: ellipsis;
    }
}

.driverReq {
    display: flex;
    align-items: center;
    gap: 4px;
    font-weight: 700;
    font-size: 14px;
    line-height: 22px;
    margin-bottom: 16px;

    span {
        font-weight: 400;
    }

    .mandatory {
        color: #F91313;
    }
}

.wrapperContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    .titleClass {
        font-weight: 700;
        font-size: 14px;
        line-height: 22px;
        width: 33%;
    }

    .radioFormWidth {
        width: 33%;
        ul {
            gap: 100px;
        }
    }

    .dropdownClass {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 16px;
        width: 33%;
        min-width: 120px;

        .textSpan {
            font-weight: 700;
            font-size: 14px;
            line-height: 22px;
        }
    }
}