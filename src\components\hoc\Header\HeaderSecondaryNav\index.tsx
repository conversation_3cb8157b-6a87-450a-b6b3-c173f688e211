"use client";
import Link from "next/link";
import {useRouter} from "next/navigation";
import css from "../header.module.scss";
import { useEffect, useRef, useState } from "react";
import { clearStorage, isUserLoggedIn } from "@/utils/utils";
import { removeToken } from "@/utils/loginRegister";
import { toast, ToastContainer } from "react-toastify";
import { getUserData } from "@/services/userService";

const HeaderSecondaryNav: React.FC = () => {
  const router = useRouter()
  const [openDropdown, setOpenDropdown] = useState<"login" | "profile" | null>(
    null
  );
  const loginRef = useRef<HTMLDivElement>(null);
  const profileRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        loginRef.current &&
        !loginRef.current.contains(event.target as Node) &&
        profileRef.current &&
        !profileRef.current.contains(event.target as Node)
      ) {
        setOpenDropdown(null);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleProfileRedirect = async () => {
    try {
      const data = await getUserData()
      if (data?.status && data?.data?.user) {
        const user = data.data.user;
        const isCompany = user.isCompany;
        
        if (isCompany) {
          router.push("/profile/company");
        } else {
          if (user.isPhoneNumberVerified) {
            router.push("/profile/driver");
          } else {
            router.push("/register/driver");
          }
        }
      } else {
        toast.error("Unable to determine profile type. Please log in again.");
      }
    } catch (error) {
      console.error("Redirect failed", error);
      toast.error("Something went wrong. Please try again.");
    }
  };

  const redirectHandler = () => {
    clearStorage();
    router.push("/choose-job-category");
  }

  return (
    <>
      <div className={css.headerBtnBox}>
        <button type="button" className={css.btn01} onClick={redirectHandler}>
          Post a job
        </button>
        {!isUserLoggedIn() && (
          <div className={css.dropdown} ref={loginRef}>
            <button
              type="button"
              className={css.btn02}
              onClick={() =>
                setOpenDropdown(openDropdown === "login" ? null : "login")
              }
            >
              Login/ Register
            </button>
            {openDropdown === "login" && (
              <ul className={`${css.dropdownMenu} ${css.dropdownMenu01}`}>
                <li>
                  <Link
                  onClick={() => setOpenDropdown(null)}
                   href="/login/driver">Driver</Link>
                </li>
                <li>
                  <Link onClick={() => setOpenDropdown(null)}
                   href="/login/company">Employer</Link>
                </li>
              </ul>
            )}
          </div>
        )}
        {isUserLoggedIn() && (
          <div className={css.dropdown} ref={profileRef}>
            <button
              type="button"
              className={css.btn02}
              onClick={() =>
                setOpenDropdown(openDropdown === "profile" ? null : "profile")
              }
            >
              Profile
              <img
                src="/images/icons/icon-down-arrow.svg"
                alt="icon-down-arrow.svg"
              />
            </button>
            {openDropdown === "profile" && (
              <ul className={`${css.dropdownMenu} ${css.dropdownMenu02}`}>
                <li className={css.google}>
                  <img
                    src="/images/icons/icon-google.svg"
                    alt="icon-google.svg"
                  />
                  <button
                    type="button"
                    className={css.close}
                    onClick={() => setOpenDropdown(null)}
                  >
                    <img
                      src="/images/icons/icon-close.svg"
                      alt="icon-close.svg"
                    />
                  </button>
                </li>
                <li>
                  <Link href="#" onClick={(e) => {
                    e.preventDefault()
                    setOpenDropdown(null)
                    handleProfileRedirect()
                  }}>
                    <img
                      src="/images/icons/icon-profile.svg"
                      alt="icon-profile.svg"
                    />
                    Profile Completion: 60%
                  </Link>
                </li>
                 {/* <li>
                  <Link href="/category/driver">
                    <img
                      src="/images/icons/icon-profile.svg"
                      alt="icon-profile.svg"
                    />
                    Driver Category
                  </Link>
                </li> */}
                <li>
                  <Link
                    href="#"
                    onClick={() => {
                      removeToken();
                      setOpenDropdown(null);
                    }}
                  >
                    <img
                      src="/images/icons/icon-logout.svg"
                      alt="icon-logout.svg"
                    />
                    Logout
                  </Link>
                </li>
              </ul>
            )}
          </div>
        )}
      </div>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
      />
    </>
  );
};

export default HeaderSecondaryNav;
