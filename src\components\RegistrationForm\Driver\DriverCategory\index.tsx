

"use client";

import { getAllCategories, submitDriverDetails } from "@/services/driverFormService";
import React, { useEffect, useState } from "react";
import { DriverCategoryData } from "../types";
import { toast } from "react-toastify";
import css from '../driverRegistration.module.scss';

interface CategoryTranslation {
    transportationCategoryTranslationId: number;
    name: string;
    description: string | null;
    locale: string;
    transportationCategoryId: number;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
}
interface Category {
    transportationCategoryId: number;
    slug: string;
    parentId: number | null;
    isActive: boolean;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    translations: {
        en: CategoryTranslation;
        es?: CategoryTranslation;
    };
}

interface Props {
    onFormSubmit: () => void;
    initialData: DriverCategoryData | null;
    currentStep: number;
    currentStage: number;
}

const DriverCategory = ({ onFormSubmit, initialData, currentStep, currentStage }: Props) => {
    const [categories, setCategories] = useState<Category[]>([]);
    const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchCategories = async () => {
            try {
                setLoading(true);
                const result = await getAllCategories();
                setCategories(result?.data || []);
            } catch (err) {
                console.error("Error fetching categories:", err);
            } finally {
                setLoading(false);
            }
        };

        fetchCategories();
    }, []);

    const handleSubmit = async () => {
        if (!selectedCategory) {
            toast.error("Please select a driver category before continuing");
            return;
        }

        const step = 2;
        const categoryChanged = initialData?.driverCategory && initialData.driverCategory !== selectedCategory;

        const payload = {
            ...(currentStage <= 2 && { currentStage: 2 }),
            ...(categoryChanged && { currentStage: 2 }), 
            ...(currentStage === 2 && step >= currentStep && { currentStep: step }),
            driver: {
                driverCategory: selectedCategory,
            },
        };
        try {
            const response = await submitDriverDetails(payload);
            toast.success("Driver category submitted successfully");
            console.log("Submitted:", response);
            onFormSubmit();
        } catch (err: unknown) {
            if (err instanceof Error) {
                toast.error("Failed to submit driver category");
            } else {
                toast.error("Something went wrong. Please try again");
            }
        }
    };

    // 1) Prefill Formik when initialData arrives
    useEffect(() => {
        if (initialData?.driverCategory) {
            setSelectedCategory(initialData.driverCategory);
        }
    }, [initialData]);

    if (loading) {
        return <div className={css.commonForm}>Loading...</div>;
    }

    return (
        <div className={css.commonForm}>
            <div className={`${css.formRow} ${css.dBlaco}`}>
                <div className={css.labelDiv}>
                    <label>
                        Select Your Primary Driver Category
                        <span className={css.tooltipIcon}>
                            <img src="/images/icons/icon-info.svg" alt="" />
                            <span className={css.tooltip}>
                                Select the general geographic areas your company serves.
                            </span>
                        </span>
                    </label>
                </div>
                <ul className={css.checkboxList}>
                    {categories.map((category) => (
                        <li key={category.transportationCategoryId} className={css.radioGroup}>
                            <label className={css.radioLabel}>
                                <input
                                    type="radio"
                                    name="driverCategory"
                                    value={category.transportationCategoryId}
                                    checked={selectedCategory === category.transportationCategoryId}
                                    onChange={(e) => setSelectedCategory(Number(e.target.value))}
                                />
                                <span className={css.checkmark}></span>
                                <p>{category.translations?.en?.name}</p>
                            </label>
                        </li>
                    ))}
                </ul>
            </div>
            <div className={`${css.formRow} ${css.submitRow}`}>
                <button type="submit" onClick={handleSubmit} className={css.submitBtn}>
                    Save and Continue
                </button>
            </div>
        </div>
    );
};

export default DriverCategory;
