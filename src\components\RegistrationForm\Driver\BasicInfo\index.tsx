"use client";
import React, { useEffect, useRef, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import css from "../driverRegistration.module.scss";
import { cleanFlatFormValues } from "@/utils/utils";
import { submitDriverDetails } from "@/services/driverFormService";
import { getStates, State } from "@/services/locationService";
import { DriverBasicInfo } from "../types";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { getUserData } from "@/services/userService";

const driverValidationSchema = Yup.object({
  isLegallyAuthorizedToWorkInUs: Yup.string().required(
    "Please select if you are authorized to work"
  ),
  futureSponsorshipNeeded: Yup.string().required(
    "Please select your sponsorship status"
  ),
  email: Yup.string().email("Invalid email ").required("Email is required"),
  phoneNumber: Yup.string()
    .matches(/^[0-9]{10,15}$/, "Invalid phone number")
    .required("Phone is required"),
  zipCode: Yup.string()
    .matches(/^\d{5}$/, "Zip code must be exactly 5 digits")
    // .required("Zip Code is required"),
    .notRequired(),
  firstName: Yup.string().nullable().notRequired(),
  lastName: Yup.string().nullable().notRequired(),
  city: Yup.string().nullable().notRequired(),
  state: Yup.string().nullable().notRequired(),
});

const driverInitialValues = {
  firstName: "",
  lastName: "",
  email: "",
  phoneNumber: "",
  street: "",
  apartmentNumber: "",
  zipCode: "",
  city: "",
  state: "",
  isLegallyAuthorizedToWorkInUs: "yes",
  futureSponsorshipNeeded: "no",
};

interface Props {
  onFormSubmit: () => void;
  initialData?: DriverBasicInfo | null;
  currentStep: number;
  currentStage: number;
}

const BasicInfo = ({ onFormSubmit, initialData, currentStep, currentStage }: Props) => {
  const [isEditable, setIsEditable] = useState({
    firstName: false,
    lastName: false,
    email: false,
    phoneNumber: false,
    zipCode: false,
    city: false,
    state: false,
  });
  const [states, setStates] = useState<State[]>([]);

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  // close drop down when click outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    }
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const fetchStates = async () => {
      try {
        const res = await getStates();
        setStates(res);
      } catch (error) {
        console.error("Failed to fetch states:", error);
      }
    };
    fetchStates();
  }, []);

  const formik = useFormik({
    initialValues: driverInitialValues,
    enableReinitialize: true,
    validationSchema: driverValidationSchema,
    onSubmit: async (values) => {
      const cleanedValues = cleanFlatFormValues(values);
      const step = 1;
      const payload = {
        ...(currentStage <= 2 && { currentStage: 2 }),
        ...(currentStage === 2 && step >= currentStep && { currentStep: step }),
        driver: {
          ...cleanedValues,
          zipCode: cleanedValues.zipCode ? Number(cleanedValues.zipCode) : null,
          isLegallyAuthorizedToWorkInUs:
            cleanedValues.isLegallyAuthorizedToWorkInUs === "yes",
          futureSponsorshipNeeded:
            cleanedValues.futureSponsorshipNeeded === "yes",
        },
      };

      const success = await submitDriverDetails(payload);
      if (success && success.status) {
        toast.success("Driver basic information submitted successfully");

        try {
          const data = await getUserData();
          if (data?.status && data?.data?.user) {
            const isPhoneVerified = data.data.user.isPhoneNumberVerified;
            if (!isPhoneVerified) {
              router.push("/register/driver");
              return;
            }
            onFormSubmit();
          } else {
            toast.error("Could not verify phone status");
          }
        } catch (error) {
          console.error("Error checking phone verification status:", error);
          toast.error("Error checking verification status");
        }
      } else {
        const errorMessage = success?.message || success?.error?.message || "Failed to submit basic information";
        toast.error(errorMessage);
      }
    },
  });

 

  useEffect(() => {
    if (initialData) {
      formik.setValues({
        firstName: initialData.firstName || "",
        lastName: initialData.lastName || "",
        email: initialData.email || "",
        phoneNumber: initialData.phoneNumber || "",
        street: initialData.street || "",
        apartmentNumber: initialData.apartmentNumber || "",
        zipCode: initialData.zipCode?.toString() || "",
        city: initialData.city || "",
        state: initialData.state || "",
        isLegallyAuthorizedToWorkInUs: initialData.isLegallyAuthorizedToWorkInUs
          ? "yes"
          : "no",
        futureSponsorshipNeeded: initialData.futureSponsorshipNeeded
          ? "yes"
          : "no",
      });
      setIsEditable((prev) => ({
        ...prev,
        firstName: !initialData.firstName,
        lastName: !initialData.lastName,
        city: !initialData.city,
        state: !initialData.state,
      }));
    }
  }, []);

    if (!initialData) {
    return <div>Loading...</div>;
  }
  type EditableField = keyof typeof isEditable;
  const toggleEdit = (field: EditableField) => {
    setIsEditable((prev) => ({ ...prev, [field]: !prev[field] }));
  };

  return (
    <form onSubmit={formik.handleSubmit} className={css.commonForm}>
      <div className={css.formRow}>
        {/* First Name */}
        <div className={css.col03}>
          <div className={css.labelDiv}>
            <label>First Name</label>
            <button type="button" onClick={() => toggleEdit("firstName")}>
              <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
            </button>
          </div>
          <input
            type="text"
            name="firstName"
            placeholder="John"
            value={formik.values.firstName}
            onChange={formik.handleChange}
            disabled={!isEditable.firstName}
          />
        </div>

        {/* Last Name */}
        <div className={css.col03}>
          <div className={css.labelDiv}>
            <label>Last Name</label>
            <button type="button" onClick={() => toggleEdit("lastName")}>
              <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
            </button>
          </div>
          <input
            type="text"
            name="lastName"
            placeholder="Doe"
            value={formik.values.lastName}
            onChange={formik.handleChange}
            disabled={!isEditable.lastName}
          />
        </div>

        {/* Email */}
        <div className={css.col03}>
          <div className={css.labelDiv}>
            <label>Company Email Address</label>
            <button type="button" onClick={() => toggleEdit("email")}>
              <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
            </button>
          </div>
          <input
            type="text"
            name="email"
            placeholder="<EMAIL>"
            value={formik.values.email}
            onChange={formik.handleChange}
            disabled={!isEditable.email}
          />
          {formik.touched.email && formik.errors.email && (
            <div className={css.error}>{formik.errors.email}</div>
          )}
        </div>

        {/* Phone Number */}
        <div className={css.col03}>
          <div className={css.labelDiv}>
            <label>Phone Number</label>
            <button type="button" onClick={() => toggleEdit("phoneNumber")}>
              <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
            </button>
          </div>
          <input
            type="text"
            name="phoneNumber"
            placeholder="(*************"
            maxLength={10}
            value={formik.values.phoneNumber}
            onChange={formik.handleChange}
            disabled={!isEditable.phoneNumber}
          />
          {formik.touched.phoneNumber && formik.errors.phoneNumber && (
            <div className={css.error}>{formik.errors.phoneNumber}</div>
          )}
        </div>

        {/* Street */}
        <div className={css.col03}>
          <label>Street Address</label>
          <input
            type="text"
            name="street"
            placeholder="Enter Street Address"
            value={formik.values.street}
            onChange={formik.handleChange}
          />
        </div>

        {/* Apartment Number */}
        <div className={css.col03}>
          <label>Apartment, Unit, Building, Floor, Room etc.</label>
          <input
            type="text"
            name="apartmentNumber"
            placeholder="Enter Apartment, Unit Building etc"
            value={formik.values.apartmentNumber}
            onChange={formik.handleChange}
          />
        </div>

        {/* Zip Code */}
        <div className={css.col03}>
          <div className={css.labelDiv}>
            <label>Primary Location (Zip Code)</label>
            <button type="button" onClick={() => toggleEdit("zipCode")}>
              <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
            </button>
          </div>
          <input
            type="text"
            name="zipCode"
            placeholder="90210"
            value={formik.values.zipCode}
            onChange={formik.handleChange}
            maxLength={5}
            disabled={!isEditable.zipCode}
          />
          {formik.touched.zipCode && formik.errors.zipCode && (
            <div className={css.error}>{formik.errors.zipCode}</div>
          )}
        </div>

        {/* City */}
        <div className={css.col03}>
          <div className={css.labelDiv}>
            <label>Primary Location (City)</label>
            <button type="button" onClick={() => toggleEdit("city")}>
              <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
            </button>
          </div>
          <input
            type="text"
            name="city"
            placeholder="Los Angeles"
            value={formik.values.city}
            onChange={formik.handleChange}
            disabled={!isEditable.city}
          />
        </div>

        {/* State */}
        <div className={css.col03}>
          <div className={css.labelDiv}>
            <label>Primary Location (State)</label>
            <button type="button" onClick={() => toggleEdit("state")}>
              <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
            </button>
          </div>

          <div className={css.dropdown} ref={dropdownRef}>
            <button
              type="button"
              className={css.dropdownToggle}
              disabled={!isEditable.state}
              onClick={() => setIsDropdownOpen((prev) => !prev)}
            >
              {states.find((s) => s.stateId.toString() === formik.values.state)
                ?.name?.en || "Select State"}
            </button>

            {isEditable.state && isDropdownOpen && (
              <div className={css.dropdownMenu}>
                {states.map((state) => (
                  <button
                    type="button"
                    key={state.stateId}
                    className={css.dropdownItem}
                    onClick={() => {
                      formik.setFieldValue("state", state.stateId.toString());
                      setIsDropdownOpen(false);
                    }}
                  >
                    {state.name.en}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Authorization Question */}
        <div>
          <label>
            Are you legally authorized to work in [your country, e.g., the
            United States]? <sup>*</sup>
          </label>
          <ul className={`${css.checkboxList} ${css.radioList}`}>
            <li className={css.radioGroup}>
              <label className={css.radioLabel}>
                <input
                  type="radio"
                  name="isLegallyAuthorizedToWorkInUs"
                  value="yes"
                  checked={
                    formik.values.isLegallyAuthorizedToWorkInUs === "yes"
                  }
                  onChange={formik.handleChange}
                />
                <span className={css.checkmark}></span>
                <p>Yes</p>
              </label>
            </li>
            <li className={css.radioGroup}>
              <label className={css.radioLabel}>
                <input
                  type="radio"
                  name="isLegallyAuthorizedToWorkInUs"
                  value="no"
                  checked={formik.values.isLegallyAuthorizedToWorkInUs === "no"}
                  onChange={formik.handleChange}
                />
                <span className={css.checkmark}></span>
                <p>No</p>
              </label>
            </li>
          </ul>
          {formik.touched.isLegallyAuthorizedToWorkInUs &&
            formik.errors.isLegallyAuthorizedToWorkInUs && (
              <div className={css.error}>
                {formik.errors.isLegallyAuthorizedToWorkInUs}
              </div>
            )}
        </div>

        {/* Sponsorship Question */}
        <div>
          <label>
            Will you now or in the future require sponsorship for employment
            visa status (e.g., H-1B)? <sup>*</sup>
          </label>
          <ul className={`${css.checkboxList} ${css.radioList}`}>
            <li className={css.radioGroup}>
              <label className={css.radioLabel}>
                <input
                  type="radio"
                  name="futureSponsorshipNeeded"
                  value="yes"
                  checked={formik.values.futureSponsorshipNeeded === "yes"}
                  onChange={formik.handleChange}
                />
                <span className={css.checkmark}></span>
                <p>Yes</p>
              </label>
            </li>
            <li className={css.radioGroup}>
              <label className={css.radioLabel}>
                <input
                  type="radio"
                  name="futureSponsorshipNeeded"
                  value="no"
                  checked={formik.values.futureSponsorshipNeeded === "no"}
                  onChange={formik.handleChange}
                />
                <span className={css.checkmark}></span>
                <p>No</p>
              </label>
            </li>
          </ul>
          {formik.touched.futureSponsorshipNeeded &&
            formik.errors.futureSponsorshipNeeded && (
              <div className={css.error}>
                {formik.errors.futureSponsorshipNeeded}
              </div>
            )}
        </div>
      </div>

      <div className={`${css.formRow} ${css.submitRow}`}>
        <button type="submit" className={css.submitBtn}>
          Save and Continue
        </button>
      </div>
    </form>
  );
};

export default BasicInfo;
