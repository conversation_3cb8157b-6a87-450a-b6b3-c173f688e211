@import '../../../../styles/global.scss';

.drivingExperienceForm {
    h2 {
        color: $black;
        font-weight: 700;
        font-size: 24px;
        line-height: 32px;
        margin-bottom: 24px;
    }

    .formGroup {
        margin-bottom: 28px;
    }

    label {
        display: block;
        color: $black;
        font-weight: 700;
        font-size: 14px;
        line-height: 22px;
        margin-bottom: 16px;

        span {
            font-weight: 400;
        }

        sup {
            color: #ff0000;
        }
    }

    .error {
        color: #F91313;
        font-size: 12px;
        line-height: 18px;
        font-weight: 400;
        margin-top: 4px;
    }

    .btnGroup {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-top: 50px;

        button {
            border-radius: 8px;
            border: 1px solid;
            cursor: pointer;
            font-weight: 500;
            font-size: 18px;
            line-height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            height: 50px;

            &.back {
                background-color: $white;
                border-color: #555555;
                color: $black;
                width: 120px;
            }

            &.exit {
                background-color: #555555;
                border-color: #555555;
                color: $white;
                width: 220px;
                margin-left: auto;
            }

            &.continue {
                background-color: $secondary;
                border-color: $secondary;
                color: $black;
                width: 220px;
            }
        }
    }
}

