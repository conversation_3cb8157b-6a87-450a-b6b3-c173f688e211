import { redirect } from "next/navigation";
import { cookies } from "next/headers";
import DriverRegistration from "@/components/RegistrationForm/Driver";

export default async function Page({
  params,
  searchParams
}: {
  params: Promise<{ lang: "en" | "es" }>;
  searchParams: Promise<{ step?: string }>;
}) {
  const { lang } = await params;
  const { step } = await searchParams;
  const cookieStore = await cookies();
  const token = cookieStore.get("authToken");

  if (!token) {
    redirect(`/${lang}`);
  }

  let data;
  try {
    const res = await fetch(`${process.env.NEXT_PUBLIC_API_V1}auth/me`, {
      method: 'GET',
      headers: {
        'Authorization': token.value,
        'Content-Type': 'application/json',
      },
      cache: 'no-store',
    });
    data = await res.json();
  } catch (fetchError) {
    console.error("Error fetching profile:", fetchError);
    redirect(`/${lang}`);
  }

  if (!data?.status || !data?.data?.user) {
    redirect(`/${lang}`);
  }

  const user = data.data.user;
  if (user.isCompany) {
    redirect(`/${lang}`);
  }

  if (!user.isPhoneNumberVerified) {
    redirect(`/${lang}/register/driver`);
  }

  return <DriverRegistration lang={lang} initialStep={step ? parseInt(step) : undefined} />;
}
