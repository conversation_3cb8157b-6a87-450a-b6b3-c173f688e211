import css from "./logincontainer.module.scss";
// import DriverLoginForm from "../DriverLogin/driverloginform";
import EmployerLoginForm from "../EmployerLogin/employerloginform";

interface LoginContainerProps {
  type?: string;
}

const LoginContainer = ({ type = '' }: LoginContainerProps) => {

  const renderForm = () => {
    // switch (type) {
      // case 'driver':
      //   return <DriverLoginForm />;
      // case 'company':
        return <EmployerLoginForm type={type}/>;
      // default:
      //   return 
    // }
  };

  return (
    <div className={css.loginWrapper}>
      {renderForm()}
    </div>
  );
};

export default LoginContainer;
