import React from "react";
import styles from "./Tooltip.module.scss";

interface TooltipProps {
  className?: string;
  tooltipMsg?: string;
}

const Tooltip = ({
  className = "",
  tooltipMsg
}: TooltipProps) => {

  return (
    <span className={`${styles.tooltipIcon} ${styles[className]}`}>
        <img src="/images/icons/icon-info.svg" alt="tooltip" />
        <span className={styles.tooltip}>
            {tooltipMsg}
        </span>
    </span>     
  );
};

export default Tooltip;
