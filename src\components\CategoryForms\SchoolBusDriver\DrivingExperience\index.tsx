"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useSchoolBusDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import { fetchSchoolBusDriverFormFields, FormValue, submitDriverDetails, fetchDriverDetails, FetchDriverDetailsResponse } from "@/services/driverFormService";
import Dropdown from "@/components/Common/Dropdown";
import css from './schoolBusExperience.module.scss';

interface DrivingExperienceFormValues {
  schoolBusExperience: string;
  cdlExperience: string;
  vehicleTypes: number[];
  ageGroups: number[];
  specialNeedsExperience: number[];
  routeTypes: number[];
  transmissionTypes: number[];
  additionalSkills: number[];
}

interface SchoolBusDriverData {
  totalVerifiableBusDriverExperience?: number;
  totalVerifiableCdlExperience?: number;
  studentTransportationVehicle?: number[];
  ageGroupTransported?: number[];
  specialNeedTransported?: number[];
  routeTypes?: number[];
  transmissionTypes?: number[];
  additionalSkills?: number[];
}

type DriverWithSchoolBusData = FetchDriverDetailsResponse["data"]["driver"] & SchoolBusDriverData;

const DrivingExperience: React.FC = () => {
  const { updateStepFromApiResponse } = useSchoolBusDriverCategory();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isDataLoading, setIsDataLoading] = useState(true);

  const [ageGroupOptions, setAgeGroupOptions] = useState<FormValue[]>([]);
  const [specialNeedsOptions, setSpecialNeedsOptions] = useState<FormValue[]>([]);
  const [routeTypeOptions, setRouteTypeOptions] = useState<FormValue[]>([]);
  const [transmissionOptions, setTransmissionOptions] = useState<FormValue[]>([]);
  const [additionalSkillsOptions, setAdditionalSkillsOptions] = useState<FormValue[]>([]);

  const experienceYears = [
    "< 1 Year",
    "1 Year", 
    "2 Years",
    "3 Years",
    "4 Years", 
    "5 Years",
    "6 - 10 Years",
    "10+ Years"
  ];

  const vehicleTypeOptions = [
    { id: 1, label: "Standard Passenger Car / Sedan (Specific student transport)" },
    { id: 2, label: "Mini-van (Up to 7 passengers)" },
    { id: 3, label: "Passenger Van (8-15 passengers)" },
    { id: 4, label: "Small Bus / Cutaway Van (Approx. 16-25 passengers)" },
    { id: 5, label: "Conventional School Bus (Approx. 25-55 passengers)" },
    { id: 6, label: "Large Transit-Style School Bus (Approx. 55+ passengers)" },
    { id: 7, label: "Vehicle equipped with Wheelchair Lift" }
  ];

  useEffect(() => {
    const loadFormFields = async () => {
      setIsDataLoading(true);
      try {
        const [formFields, driverDetails] = await Promise.all([
          fetchSchoolBusDriverFormFields(),
          fetchDriverDetails()
        ]);

        setAgeGroupOptions(formFields["age-groups-transported-driver-school-bus-driver"] || []);
        setSpecialNeedsOptions(formFields["special-needs-transport-experience-driver-school-bus-driver"] || []);
        setRouteTypeOptions(formFields["route-types-driven-driver-school-bus-driver"] || []);
        setTransmissionOptions(formFields["transmission-type-experience-driver-school-bus-driver"] || []);
        setAdditionalSkillsOptions(formFields["additional-skills-experience-driver-school-bus-driver"] || []);

        if (driverDetails?.data?.driver) {
          const driver = driverDetails.data.driver as DriverWithSchoolBusData;

          console.log("Raw driver data from API:", {
            studentTransportationVehicle: driver.studentTransportationVehicle,
            ageGroupTransported: driver.ageGroupTransported,
            specialNeedTransported: driver.specialNeedTransported,
            routeTypes: driver.routeTypes,
            transmissionTypes: driver.transmissionTypes,
            additionalSkills: driver.additionalSkills
          });

          const experienceReverseMapping: Record<number, string> = {
            0: "< 1 Year",
            1: "1 Year",
            2: "2 Years",
            3: "3 Years",
            4: "4 Years",
            5: "5 Years",
            6: "6 - 10 Years",
            10: "10+ Years"
          };

          const cleanArrayToNumbers = (arr: (string | number)[]): number[] => {
            if (!Array.isArray(arr)) return [];
            const cleaned = [...new Set(arr.map(item => Number(item)).filter(num => !isNaN(num)))];
            console.log(`Cleaning array [${arr}] -> [${cleaned}]`);
            return cleaned;
          };

          const cleanedValues = {
            schoolBusExperience: driver.totalVerifiableBusDriverExperience !== undefined
              ? experienceReverseMapping[driver.totalVerifiableBusDriverExperience] || ""
              : "",
            cdlExperience: driver.totalVerifiableCdlExperience !== undefined
              ? experienceReverseMapping[driver.totalVerifiableCdlExperience] || ""
              : "",
            vehicleTypes: cleanArrayToNumbers(driver.studentTransportationVehicle || []),
            ageGroups: cleanArrayToNumbers(driver.ageGroupTransported || []),
            specialNeedsExperience: cleanArrayToNumbers(driver.specialNeedTransported || []),
            routeTypes: cleanArrayToNumbers(driver.routeTypes || []),
            transmissionTypes: cleanArrayToNumbers(driver.transmissionTypes || []),
            additionalSkills: cleanArrayToNumbers(driver.additionalSkills || [])
          };
          formik.setValues(cleanedValues);
        }

      } catch (error) {
        console.error("Failed to load form fields:", error);
        toast.error("Failed to load form options. Please refresh the page.");
      } finally {
        setIsDataLoading(false);
      }
    };

    loadFormFields();
  }, []);

  const validationSchema = Yup.object().shape({
    schoolBusExperience: Yup.string().required("School bus experience is required"),
    cdlExperience: Yup.string().required("CDL experience is required"),
    vehicleTypes: Yup.array().min(1, "Please select at least one vehicle type"),
    ageGroups: Yup.array().min(1, "Please select at least one age group"),
    transmissionTypes: Yup.array().min(1, "Please select at least one transmission type"),
    specialNeedsExperience: Yup.array().min(1,"Please select at least one special need experience"),
    routeTypes:Yup.array().min(1,"Please select at least one route types"),
  });

  const formik = useFormik<DrivingExperienceFormValues>({
    initialValues: {
      schoolBusExperience: "",
      cdlExperience: "",
      vehicleTypes: [],
      ageGroups: [],
      specialNeedsExperience: [],
      routeTypes: [],
      transmissionTypes: [],
      additionalSkills: [],
    },
    validationSchema,
    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  const handleSubmit = async (values: DrivingExperienceFormValues, shouldContinue: boolean = true) => {
    setIsLoading(true);
    try {
      const experienceMapping: Record<string, number> = {
        "< 1 Year": 0,
        "1 Year": 1,
        "2 Years": 2,
        "3 Years": 3,
        "4 Years": 4,
        "5 Years": 5,
        "6 - 10 Years": 6,
        "10+ Years": 10
      };

      const ensureNumberArray = (arr: (string | number)[]): number[] => {
        if (!Array.isArray(arr)) return [];
        return [...new Set(arr.map(item => Number(item)).filter(num => !isNaN(num)))];
      };

      const payload = {
        currentStage: 3,
        currentStep: 1,
        driver: {
          totalVerifiableBusDriverExperience: experienceMapping[values.schoolBusExperience] || 0,
          totalVerifiableCdlExperience: experienceMapping[values.cdlExperience] || 0,
          studentTransportationVehicle: ensureNumberArray(values.vehicleTypes),
          ageGroupTransported: ensureNumberArray(values.ageGroups),
          specialNeedTransported: ensureNumberArray(values.specialNeedsExperience),
          routeTypes: ensureNumberArray(values.routeTypes),
          transmissionTypes: ensureNumberArray(values.transmissionTypes),
          additionalSkills: ensureNumberArray(values.additionalSkills)
        }
      };


      const response = await submitDriverDetails(payload);

      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Experience saved successfully!");
          window.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to save experience data";
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error("Error submitting experience:", error);
      toast.error("Failed to save experience. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCheckboxChange = (fieldName: keyof DrivingExperienceFormValues, value: number) => {
    const currentValues = formik.values[fieldName] as number[];
    const newValues = currentValues.includes(value)
      ? currentValues.filter(item => item !== value)
      : [...currentValues, value];
    formik.setFieldValue(fieldName, newValues);
  };

  if (isDataLoading) {
    return (
      <div style={{ padding: "2rem", textAlign: "center" }}>
        <div style={{ fontSize: "18px", color: "#666" }}>Loading form options...</div>
      </div>
    );
  }

  return (
    <div className={css.schoolBusExperience}>
      <h3>Step 1: Driving Experience (School Bus Driver)</h3>
      <h5>Tell us about your experience driving school buses and working with students.</h5>
      <h6 className={css.required}>Required fields are marked with <sup>*</sup></h6>

      <form onSubmit={formik.handleSubmit} className={css.drivingExperienceForm}>
        {/* School Bus Experience */}
        <div className={css.card}>
          <h3>School Bus Driving Experience</h3>
          <div className={css.formRow}>
            <div className={css.col02}>
              <label htmlFor="schoolBusExperience">Total Years of Verifiable School Bus Driving Experience:<sup>*</sup></label>
              <Dropdown
                options={experienceYears.map(year => ({ value: year, label: year }))}
                value={formik.values.schoolBusExperience}
                placeholder="Select Years"
                onChange={(value) => formik.setFieldValue("schoolBusExperience", value)}
                error={formik.touched.schoolBusExperience && formik.errors.schoolBusExperience ? formik.errors.schoolBusExperience : undefined}
                name="schoolBusExperience"
              />
            </div>
            <div className={css.col02}>
              <label htmlFor="cdlExperience">Total Years of Verifiable CDL Experience (Must hold Class B or A with P & S): <sup>*</sup><span>(Confirm CDL level from Stage 2)</span>
              </label>
              <Dropdown
                options={experienceYears.map(year => ({ value: year, label: year }))}
                value={formik.values.cdlExperience}
                placeholder="Select Years"
                onChange={(value) => formik.setFieldValue("cdlExperience", value)}
                error={formik.touched.cdlExperience && formik.errors.cdlExperience ? formik.errors.cdlExperience : undefined}
                name="cdlExperience"
              />
            </div>
          </div>
        </div>

        {/* Vehicle Types */}
        <div className={css.card}>
          <h3>Type(s) of Vehicles Driven for Student Transportation <sup>*</sup></h3>
          <label>(Check all that apply)</label>

          <ul className={`${css.checkBox} ${css.column}`}>
            {vehicleTypeOptions.map((option) => (
              <li key={option.id}>
                <input
                  type="checkbox"
                  checked={formik.values.vehicleTypes.includes(option.id)}
                  onChange={() => handleCheckboxChange('vehicleTypes', option.id)}
                />
                <span className={css.checkmark}></span>
                <p>{option.label}</p>
              </li>
            ))}
          </ul>
          {formik.touched.vehicleTypes && formik.errors.vehicleTypes && (
            <span className={css.error}>
              {formik.errors.vehicleTypes}
            </span>
          )}
        </div>

        {/* Passenger & Route Experience */}
        <div className={css.card}>
          <h3>Passenger & Route Experience <sup>*</sup></h3>
          <label>(Check all that apply)</label>
          <h5>Age Groups Transported:</h5>
          <ul className={`${css.checkBox} ${css.column}`}>
              {ageGroupOptions.map((option) => (
                <li key={option.formValueId}>
                  <input
                    type="checkbox"
                    checked={formik.values.ageGroups.includes(option.formValueId)}
                    onChange={() => handleCheckboxChange('ageGroups', option.formValueId)}
                  />
                  <span className={css.checkmark}></span>
                  <p>{option.label.en}</p>
                </li>
              ))}
          </ul>
          {formik.touched.ageGroups && formik.errors.ageGroups && (
            <span className={css.error}>
              {formik.errors.ageGroups}
            </span>
          )}
        </div>

        <div className={css.card}>
          <h3>Special Needs Transport Experience:</h3>
          <ul className={`${css.checkBox} ${css.column}`}>
            {specialNeedsOptions.map((option) => (
              <li key={option.formValueId}>
                <input
                  type="checkbox"
                  checked={formik.values.specialNeedsExperience.includes(option.formValueId)}
                  onChange={() => handleCheckboxChange('specialNeedsExperience', option.formValueId)}
                />
                <span className={css.checkmark}></span>
                <p>{option.label.en}</p>
              </li>
            ))}
          </ul>
             {formik.touched.specialNeedsExperience && formik.errors.specialNeedsExperience && (
            <span className={css.error}>
              {formik.errors.specialNeedsExperience}
            </span>
          )}
        </div>

        <div className={css.card}>
          <h3>Route Types Driven:</h3>
          <ul className={`${css.checkBox} ${css.column}`}>
            {routeTypeOptions.map((option) => (
              <li key={option.formValueId} style={{ display: "flex", alignItems: "flex-start", gap: "0.5rem", cursor: "pointer" }}>
                <input
                  type="checkbox"
                  checked={formik.values.routeTypes.includes(option.formValueId)}
                  onChange={() => handleCheckboxChange('routeTypes', option.formValueId)}
                />
                <span className={css.checkmark}></span>
                <p>{option.label.en}</p>
              </li>
            ))}
          </ul>
               {formik.touched.routeTypes && formik.errors.routeTypes && (
            <span className={css.error}>
              {formik.errors.routeTypes}
            </span>
          )}
        </div>

        {/* Transmission Type Experience */}
        <div className={css.card}>
          <h3>Transmission Type Experience <sup>*</sup></h3>
          <label>(In School Buses/Vehicles Driven) (Check all that apply)</label>

          <ul className={`${css.checkBox} ${css.column}`}>
            {transmissionOptions.map((option) => (
              <li key={option.formValueId}>
                <input
                  type="checkbox"
                  checked={formik.values.transmissionTypes.includes(option.formValueId)}
                  onChange={() => handleCheckboxChange('transmissionTypes', option.formValueId)}
                />
                <span className={css.checkmark}></span>
                <p>{option.label.en}</p>
              </li>
            ))}
          </ul>
          {formik.touched.transmissionTypes && formik.errors.transmissionTypes && (
            <span className={css.error}>
              {formik.errors.transmissionTypes}
            </span>
          )}
        </div>

        {/* Additional Skills & Experience */}
        <div className={css.card}>
          <h3>Additional Skills &amp; Experience</h3>
          <label>(Optional)</label>

          <ul className={`${css.checkBox} ${css.column}`}>
            {additionalSkillsOptions.map((option) => (
              <li key={option.formValueId}>
                <input
                  type="checkbox"
                  checked={formik.values.additionalSkills.includes(option.formValueId)}
                  onChange={() => handleCheckboxChange('additionalSkills', option.formValueId)}
                />
                <span className={css.checkmark}></span>
                <p>{option.label.en}</p>
              </li>
            ))}
          </ul>
        </div>

        {/* Navigation Buttons */}
        <div className={css.btnGroup}>
          <button
            type="button"
            onClick={() => router.back()}
            disabled={isLoading}
            className={css.back}
          >
            <img src="/images/icons/arrow_back.svg"/>Back
          </button>

          <button
              type="submit"
              disabled={isLoading}
              className={css.exit}
            >
              {isLoading ? "Saving..." : "Save & Continue"}
            </button>

            <button
              type="button"
              onClick={() => handleSubmit(formik.values, false)}
              disabled={isLoading}
              className={css.continue}
            >
              Save & Exit
            </button>
        </div>
      </form>
    </div>
  );
};

export default DrivingExperience;
