"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
 
import {
  submitDriverDetails,
  fetchDriverDetails
} from "@/services/driverFormService";
import BrowseFiles, { FileItem } from "@/components/Browse/BrowseFiles";
import { useSchoolBusAideCategory } from "@/contexts/CommonDriverCategoryContext";


interface DocumentFormValues {
  dotMedicalCardFiles: FileItem[];
  twicCardFiles: FileItem[];
  hazmatAndOthersFiles: FileItem[];
}

const Documents: React.FC = () => {
  const { updateStepFromApiResponse,  goToPreviousStep, canGoBack } = useSchoolBusAideCategory();
  const router = useRouter();
   const [dotMedicalCardFiles, setDotMedicalCardFiles] = useState<FileItem[]>([]);
   const [twicCardFiles, setTwicCardFiles] = useState<FileItem[]>([]);
   const [hazmatAndOthersFiles, setHazmatAndOthersFiles] = useState<FileItem[]>([]);
   const [isLoading, setIsLoading] = useState(true);
 
 
   useEffect(() => {
     const loadDriverDetails = async () => {
       try {
         const response = await fetchDriverDetails();
         if (response?.status && response?.data?.driver) {
           const driver = response.data.driver;
    
 
           if (driver.documents) {
             setDotMedicalCardFiles(driver.documents.dot_medical_card || []);
             setTwicCardFiles(driver.documents.twic_card || []);
             setHazmatAndOthersFiles(driver.documents.hazmat_and_others || []);
           }
         }
       } catch (error) {
         console.error("Error fetching driver details:", error);
         toast.error("Failed to load existing documents");
       } finally {
         setIsLoading(false);
       }
     };
 
     loadDriverDetails();
   }, []);
 
   const validationSchema = Yup.object({
     dotMedicalCardFiles: Yup.array()
       .min(1, "DOT Medical Card document is required")
       .required("DOT Medical Card document is required"),
     twicCardFiles: Yup.array()
       .min(1, "TWIC Card document is required")
       .required("TWIC Card document is required"),
     hazmatAndOthersFiles: Yup.array()
       .min(1, "Hazmat endorsement proof is required")
       .required("Hazmat endorsement proof is required"),
   });

     const handleSubmit = async (shouldContinue: boolean = true) => {
    try {
      const payload = {
        currentStage: 3,
        currentStep: 4,
        driver: {
          documents: {
            dot_medical_card: dotMedicalCardFiles,
            twic_card: twicCardFiles,
            hazmat_and_others: hazmatAndOthersFiles,
          },
        },
      };
      console.log("Submitting documents payload:", JSON.stringify(payload, null, 2));
      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Documents uploaded successfully!");
          window.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to upload documents. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error: unknown) {
      console.error("Error submitting documents:", error);
      const errorMessage = error instanceof Error ? error.message : "An error occurred while processing documents.";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

   const formik = useFormik<DocumentFormValues>({
     initialValues: {
       dotMedicalCardFiles: [],
       twicCardFiles: [],
       hazmatAndOthersFiles: [],
     },
     validationSchema,
     onSubmit: async () => {
       try {
         await handleSubmit(true); 
       } catch (error) {
         console.error("Error submitting documents:", error);
         toast.error("An error occurred while uploading documents.");
       }
     },
   });

  useEffect(() => {
    formik.setFieldValue("dotMedicalCardFiles", dotMedicalCardFiles);
  }, [dotMedicalCardFiles]);

  useEffect(() => {
    formik.setFieldValue("twicCardFiles", twicCardFiles);
  }, [twicCardFiles]);

  useEffect(() => {
    formik.setFieldValue("hazmatAndOthersFiles", hazmatAndOthersFiles);
  }, [hazmatAndOthersFiles]);

  if (isLoading) {
    return <div>Loading existing documents...</div>;
  }

 

  return (
    <div style={{ padding: "2rem", maxWidth: "1200px", margin: "0 auto" }}>
      <div style={{ marginBottom: "2rem" }}>
        <h2 style={{ fontSize: "24px", fontWeight: "600", marginBottom: "1rem", color: "#333" }}>
          Step 4: Additional Document Uploads (School Bus Driver)
        </h2>
        <p style={{ fontSize: "16px", color: "#666", marginBottom: "1rem" }}>
          Upload required and relevant documents. Your CDL/ID was uploaded in Stage 2. These docs support your qualifications and will only be shared via approved &quot;Hiring Packet Requests&quot;.
        </p>
        <p style={{ fontSize: "14px", color: "#666", fontStyle: "italic", marginBottom: "0.5rem" }}>
          (Accepted formats: JPG, PNG, PDF. Max size: 5MB per file)
        </p>
        <p style={{ fontSize: "14px", color: "#333" }}>
          <strong>Required fields are marked with * and depend on your selections in Step 3</strong>
        </p>
      </div>

      <form onSubmit={formik.handleSubmit}>
               <div style={{ marginBottom: "2rem" }}>
            <h3>Upload DOT Medical Card and Medical Variance/Exemption Document *</h3>
            <BrowseFiles
              label="DOT Medical Card Documents"
              maxFiles={2}
              onUploadComplete={setDotMedicalCardFiles}
              initialFiles={dotMedicalCardFiles}
            />
            {formik.touched.dotMedicalCardFiles && formik.errors.dotMedicalCardFiles && (
              <p style={{ color: "red", fontSize: "12px", marginTop: "0.5rem" }}>
                {typeof formik.errors.dotMedicalCardFiles === 'string'
                  ? formik.errors.dotMedicalCardFiles
                  : 'DOT Medical Card document is required'}
              </p>
            )}
          </div>

          <div style={{ marginBottom: "2rem" }}>
            <h3>Upload Scan / Photo of TWIC Card *</h3>
            <BrowseFiles
              label="TWIC Card Documents"
              maxFiles={2}
              onUploadComplete={setTwicCardFiles}
              initialFiles={twicCardFiles}
            />
            {formik.touched.twicCardFiles && formik.errors.twicCardFiles && (
              <p style={{ color: "red", fontSize: "12px", marginTop: "0.5rem" }}>
                {typeof formik.errors.twicCardFiles === 'string'
                  ? formik.errors.twicCardFiles
                  : 'TWIC Card document is required'}
              </p>
            )}
          </div>

          <div style={{ marginBottom: "2rem" }}>
            <h3>Upload Hazmat Endorsement Proof and Any Other Relevant Certifications *</h3>
            <BrowseFiles
              label="Hazmat and Other Certifications"
              maxFiles={2}
              onUploadComplete={setHazmatAndOthersFiles}
              initialFiles={hazmatAndOthersFiles}
            />
            {formik.touched.hazmatAndOthersFiles && formik.errors.hazmatAndOthersFiles && (
              <p style={{ color: "red", fontSize: "12px", marginTop: "0.5rem" }}>
                {typeof formik.errors.hazmatAndOthersFiles === 'string'
                  ? formik.errors.hazmatAndOthersFiles
                  : 'Hazmat endorsement proof is required'}
              </p>
            )}
          </div>


        {/* Navigation Buttons */}
        <div style={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          marginTop: "2rem",
          padding: "1.5rem",
          borderTop: "1px solid #e5e5e5"
        }}>
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              disabled={isLoading}
              style={{
                padding: "0.75rem 1.5rem",
                backgroundColor: "#6c757d",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: isLoading ? "not-allowed" : "pointer",
                fontSize: "16px"
              }}
            >
              &lt; Back  
            </button>
          )}

          <div style={{ display: "flex", gap: "1rem" }}>
            <button
              type="submit"
              disabled={isLoading}
              style={{
                padding: "0.75rem 1.5rem",
                backgroundColor: "#007bff",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: isLoading ? "not-allowed" : "pointer",
                fontSize: "16px"
              }}
            >
              {isLoading ? "Saving..." : "Save & Continue (To Step 5: Consents & Review) >"}
            </button>

            <button
              type="button"
              onClick={() => handleSubmit(false)}
              disabled={isLoading}
              style={{
                padding: "0.75rem 1.5rem",
                backgroundColor: "#28a745",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: isLoading ? "not-allowed" : "pointer",
                fontSize: "16px"
              }}
            >
              Save & Exit (Complete Later)
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default Documents;
