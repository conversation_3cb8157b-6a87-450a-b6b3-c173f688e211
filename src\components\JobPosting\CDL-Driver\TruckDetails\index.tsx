"use client";

import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import styles from "../../jobPosting.module.scss";
import CheckboxField from "@/components/Common/Form/CheckboxField";
import TextField from "@/components/Common/Form/TextField";
import RadioField from "@/components/Common/Form/RadioField";
import { VehicleFormValues, VehicleProps } from "../../../../types/jobpostingform";
import { getJobPosting, jobPostingUpdate } from "@/services/jobPostingService";
import ButtonComponent from "@/components/Common/Form/ButtonComponent";
import { otherValue } from "@/utils/constant";
import { useRouter } from "next/navigation";
import { getCookie } from "cookies-next";
import { deepCleanValues, getCommonKeys, handleArrayFields, safeFetch, scrollToFirstError } from "@/utils/utils";
import OverlayScreen from "@/components/Common/Form/OverlayScreen";
import { truckFormCDL } from "@/initialValues/truckFormValues";
import { getTruckFormSchema } from "@/schemas/truckFormSchema";
import { truckFormPayload } from "@/submitHandler/truckSubmit";

const TruckDetails = ({ formFields, setCurrentStep }: VehicleProps) => {
  const [isDraft, setIsDraft] = useState(false);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const jobIdStr = getCookie("jobId");
  const jobId = jobIdStr ? Number(jobIdStr) : null;

  const formik = useFormik<VehicleFormValues>({
    initialValues: truckFormCDL,
    validationSchema: getTruckFormSchema(isDraft),
    onSubmit: async(values: VehicleFormValues) => {
      setLoading(true);
      const payload = truckFormPayload(values, isDraft);

      try {
        await jobPostingUpdate(payload, jobId, setCurrentStep, 4, isDraft, router);
      } catch (error) {
        console.error("Failed to fetch", error);
      } finally {
        setLoading(false);
      }
    },
  });

  useEffect(() => {
    if (submitAttempted && Object.keys(formik.errors).length > 0) {
      scrollToFirstError(formik.errors);
      setSubmitAttempted(false);
    }
  }, [formik.errors, submitAttempted]);

  useEffect(() => {
    const fetchData = async () => {
      const result = await safeFetch(() => getJobPosting("", jobId), {});
      
      if(result?.jobPost) {
        const initialKeys = getCommonKeys(truckFormCDL, result.jobPost);
        const cleanedJobPost = deepCleanValues(initialKeys);
        
        const arrayFields = handleArrayFields(cleanedJobPost, {
          vehicleType: { outputKey: "otherVehicleText", addField: 301 },
          truckAmenities: { outputKey: "truckAmenitiesText", addField: "other" },
          inCabTech: { outputKey: "inCabTechText", addField: 328 },
          eldSystems: { outputKey: "eldSystemsText", addField: 336 },
          freightTypes: { outputKey: "freightTypesText", addField: "other" },
          trailerTypes: {},
          truckAge: {},
          transmission: {},
          truckAssignment: {},
          trailerAge: {},
          loadingReqs: {}
        });

        const newObject = {} as Record<string, unknown>;

        if (typeof cleanedJobPost.jobTitle === 'string' && !isNaN(Number(cleanedJobPost.jobTitle))) {
          newObject.jobTitle = Number(cleanedJobPost.jobTitle);
        } else {
          cleanedJobPost.otherJob = cleanedJobPost.jobTitle;
          newObject.jobTitle = "other";
        }

        if(cleanedJobPost.customHomeTimeFrequency) {
          newObject.homeTimeFrequency = "other";
        }

        formik.setValues({
          ...formik.values,
          ...cleanedJobPost,
          ...arrayFields,
          ...newObject
        });
      }
    };

    fetchData();
  }, [])

  return (
    <div className={styles.payStructureInfo}>
      {loading && <OverlayScreen />}
      <form onSubmit={formik.handleSubmit}>
        <div>
          <h2 className={styles.heading}>Truck / Vehicle Details</h2>
          <CheckboxField 
            label="Truck Type Primarily Operated"
            fieldName="vehicleType"
            formik={formik}
            checkboxArray={formFields?.["truck-type-primarily-operated-job-posting-cdl"]}
          />
          {formik.values.vehicleType.includes(301) &&
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                fieldName="otherVehicleText"
                placeholder="Enter here"
                formik={formik}
                hide={true}
              />
            </div>
          }
          <div className={styles.rowField}>
            <TextField
              className="columnWidth_3"
              label="Truck Make / Model"
              fieldName="vehicleTypeText"
              placeholder="e.g., Freightliner Cascadia, Kenworth T680, Hino 268"
              formik={formik}
              hide={true}
            />
          </div>
          <RadioField
            label="Truck Age"
            fieldName="truckAge"
            formik={formik}
            radioArray={formFields?.["typical-truck-age-job-posting-cdl"]}
          />
          <RadioField
            label="Transmission Type"
            fieldName="transmission"
            formik={formik}
            radioArray={formFields?.["transmission-type-job-posting-cdl"]}
          />
          <RadioField
            label="Truck Assignment"
            fieldName="truckAssignment"
            formik={formik}
            radioArray={formFields?.["truck-assignment-job-posting-cdl"]}
          />
          <div className={styles.rowField}>
            <TextField
              className="columnWidth_3"
              label="Governed Speed"
              fieldName="governedSpeed"
              placeholder="e.g., 1 MPH"
              formik={formik}
              hide={true}
            />
          </div>
          <CheckboxField 
            label="Truck Amenities"
            desc=" - Check all that apply (Primarily for Sleeper Cabs)"
            fieldName="truckAmenities"
            formik={formik}
            checkboxArray={[
              ...(formFields?.["truck-amenities-job-posting-cdl"] || []),
              otherValue
            ]}
            hide={true}
          />
          {formik.values.truckAmenities.includes("other") &&
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                fieldName="truckAmenitiesText"
                placeholder="Enter here"
                formik={formik}
                hide={true}
              />
            </div>
          }
          <CheckboxField 
            label="In-Cab Technology / Safety Features"
            desc=" - Check all that apply"
            fieldName="inCabTech"
            formik={formik}
            checkboxArray={formFields?.["incab-technology-safety-features-job-posting-cdl"]}
            hide={true}
          />
          {formik.values.inCabTech.includes(328) &&
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                fieldName="inCabTechText"
                placeholder="Enter here"
                formik={formik}
                hide={true}
              />
            </div>
          }
          <CheckboxField 
            label="ELD Systems"
            fieldName="eldSystems"
            formik={formik}
            checkboxArray={formFields?.["eld-system-used-if-known-job-posting-cdl"]}
            hide={true}
          />
          {formik.values.eldSystems.includes(336) &&
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                fieldName="eldSystemsText"
                placeholder="Enter here"
                formik={formik}
                hide={true}
              />
            </div>
          }
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Trailer / Equipment Details <span>(If Applicable)</span></h2>
          <CheckboxField 
            label="Trailer Type Primarily Hauled"
            desc=" (Select N/A if Straight Truck only)"
            fieldName="trailerTypes"
            formik={formik}
            checkboxArray={formFields?.["trailer-type-primarily-hauled-job-posting-cdl"]}
          />
          <div className={styles.rowField}>
            <TextField
              className="columnWidth_3"
              label="Typical Trailer Length(s)"
              desc=" (If applicable)"
              fieldName="trailerLength"
              placeholder="e.g., 1 MPH"
              formik={formik}
              hide={true}
            />
            <TextField
              className="columnWidth_3"
              label="Specify Tanker Contents"
              fieldName="tankerContents"
              placeholder="e.g., 1 MPH"
              formik={formik}
              hide={true}
            />
            {formik.values.trailerTypes.includes(355) &&
              <TextField
                className="columnWidth_3"
                label="Specify Other Trailer Type"
                fieldName="otherTrailer"
                placeholder="e.g., 1 MPH"
                formik={formik}
                hide={true}
              />
            }
          </div>
          <RadioField
            label="Trailer Age"
            fieldName="trailerAge"
            formik={formik}
            radioArray={formFields?.["typical-trailer-age-optional-job-posting-cdl"]}
            hide={true}
          />
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Freight & Loading Details</h2>
          <CheckboxField 
            label="Primary Freight Type(s)"
            desc=" - Check all that apply"
            fieldName="freightTypes"
            formik={formik}
            checkboxArray={[
              ...(formFields?.["primary-freight-types-job-posting-cdl"] || []),
              otherValue
            ]}
          />
          {formik.values.freightTypes.includes("other") &&
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                fieldName="freightTypesText"
                placeholder="Enter here"
                formik={formik}
                hide={true}
              />
            </div>
          }
          <CheckboxField 
            label="Loading/Unloading Requirements"
            fieldName="loadingReqs"
            formik={formik}
            checkboxArray={formFields?.["loadingunloading-requirements-job-posting-cdl"]}
          />
          <RadioField
            label="Drop & Hook Percentage"
            desc=" (Estimate - If applicable)"
            fieldName="dropHookPercent"
            formik={formik}
            radioArray={formFields?.["drop-hook-percentage-estimate-if-applicable-job-posting-cdl"]}
            hide={true}
          />
          <div className={styles.rowField}>
            <TextField
              className="columnWidth_3"
              label="Average Load Weight"
              fieldName="avgLoadWeight"
              placeholder="e.g., 1 MPH"
              formik={formik}
              hide={true}
            />
          </div>
          <ButtonComponent
            backBtnHandler={() => setCurrentStep(2)}
            draftBtnHandler={() => {
              setIsDraft(true);
              formik.submitForm()
            }}
            saveBtnHandler={() => {
              setIsDraft(false);
              setSubmitAttempted(true);
            }}
          />
        </div>
      </form>
    </div>
  );
};

export default TruckDetails;
