@import '../../../styles/global.scss';

.header {
    background-color: $secondary;
    padding:  20px 0px;

    @include for-size(big-tablet-down) {
        display: none
    }

    .container {
        @include container;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
    }

    nav {
        margin-left: 60px;

        ul {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 20px;

            a, span {
                font-size: 16px;
                line-height: 140%;
                color: $dark-900;
                text-transform: capitalize;
                text-decoration: none;
                padding: 8px 12px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                gap: 12px;
            }
        }
    }

    .headerBtnBox {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 20px;
        margin-left: auto;

        button {
            border-radius: 4px;
            color: #121212;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            width: 160px;
            height: 50px;
            font-weight: 400;
            font-size: 19px;
            line-height: 26px;
            cursor: pointer;

            &.btn01 {
                border: none;
                background-color: $white;
            }

            &.btn02 {
                border: 1px solid #121212;
                background-color: transparent;
            }
        }

        // .dropdown {
        //     position: relative;
        // }

        // .dropdownToggle {
        //     background-color: transparent;
        //     border: none;
        //     display: inline-flex;
        //     align-items: center;
        //     gap: 10px;
        //     font-size: 19px;
        //     line-height: 140%;
        //     color: $dark-900;
        //     font-weight: 400;
        //     padding: 8px 10px 8px 12px;

        //     &.bold {
        //         font-weight: 600;;
        //     }
        // }

        // .dropdownMenu {
        //     background-color: $white;
        //     border: 1px solid $light;
        //     width: 200px;
        //     position: absolute;
        //     left: -70px;
        //     top: 40px;

        //     li {
        //         border-bottom: 1px solid $gray-50;

        //         &:last-child {
        //             border: none;
        //         }
        //     }

        //     a {
        //         font-size: 19px;
        //         line-height: 140%;
        //         color: $black;
        //         text-decoration: none;
        //         height: 51px;
        //         display: inline-flex;
        //         align-items: center;
        //         justify-content: center;
        //         width: 100%;

        //         &:hover {
        //             background-color: $gray-50;
        //         }
        //     }
        // }
    }

    .dropdown {
        position: relative;

        .dropdownMenu {
            gap: 0px;
            background-color: $white;
            border-radius: 4px;
            position: absolute;
            right: 0px;
            top: 54px;
            z-index: 9;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

            &.dropdownMenu01 {
                padding: 1px;
                width: 200px;

                li {
                    width: 100%;
                }
    
                a {
                    border-radius: 2px;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: 400;
                    font-size: 19px;
                    line-height: 25px;
                    padding: 2px 12px;
                    text-align: center;
                    height: 51px;
                    width: 100%;
    
                    &:hover,
                    .active {
                        background-color: #E7E7EC;
                        font-weight: 500;
                    }
                }

                &.language {
                    a {
                        justify-content: flex-start;

                        img {
                            height: 24px;
                        }

                        &:hover {
                            font-weight: 400;
                        }
                    }
                }
            }

            &.dropdownMenu02 {
                padding: 12px;
                width: 288px;

                li {
                    width: 100%;

                    &.google {
                        display: inline-flex;
                        align-items: center;
                        justify-content: space-between;
                        padding: 6px 0px;

                        .close {
                            border: none;
                            background-color: transparent;
                            width: 24px;
                            height: 24px;
                            display: inline-flex;
                            align-items: center;
                            justify-content: center;
                        }
                    }

                    a {
                        border-radius: 4px;
                        display: inline-flex;
                        align-items: center;
                        justify-content: flex-start;
                        gap: 8px;
                        font-weight: 400;
                        font-size: 16px;
                        line-height: 22px;
                        padding: 2px 8px;
                        text-align: center;
                        height: 56px;
                        width: 100%;

                        &:hover,
                        .active {
                            background-color: #E7E7EC;
                        }
                    }
                }
            }
        }
    }
}

.phoneHeader {
    background-color: $secondary;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0px 16px;
    height: 60px;

    @include for-size(big-tablet-up) {
        display: none;
    }

    .menuIcon {
        background-color: transparent;
        border: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
    }
}