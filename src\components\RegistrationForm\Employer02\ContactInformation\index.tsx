import { useFormik } from 'formik';
import css from '../employerRegistration.module.scss'
import * as Yup from "yup";
import { submitCompanyDetails } from '@/services/employerFormService';
import { toast } from "react-toastify";
import { ContactValues, CompanyValues } from '../types';
const contactValidationSchema = Yup.object({
    contactPersonName: Yup.string().required("Contact person name is required"),
    jobTitle: Yup.string().required("Job title is required"),
});

type ContactInformationProps = {
    formInitialValues: ContactValues;
    companyValues: CompanyValues;
    currentStep: number;
    setIsSecondAccordionComplete: (val: boolean) => void;
    setIsSecondAccordionOpen: (val: boolean) => void;
    setIsThirdAccordionOpen: (val: boolean) => void;
};

const ContactInformation: React.FC<ContactInformationProps> = ({ formInitialValues, companyValues, currentStep, setIsSecondAccordionComplete, setIsSecondAccordionOpen, setIsThirdAccordionOpen }) => {
    const formikContact = useFormik({
        initialValues: formInitialValues,
        validationSchema: contactValidationSchema,
        enableReinitialize: true,
        onSubmit: async (values) => {
            const step = 2
            const payload = {
                currentStage: 2,
                ...(step >= currentStep && { currentStep: step }),
                user: values,
            };
            const success = await submitCompanyDetails(payload);
            if (success) {
                toast.success("Contact information submitted");
                setTimeout(() => {
                    setIsSecondAccordionComplete(true);
                    setIsSecondAccordionOpen(false);
                    setIsThirdAccordionOpen(true);
                }, 2000)

            } else {
                toast.error("Failed to submit contact information.");
            }
        },
    });
    return (
        <>
            <form
                className={css.commonForm}
                onSubmit={formikContact.handleSubmit}
            >
                <div className={css.formRow}>
                    <div className={css.col03}>
                        <div className={css.labelDiv}>
                            <label>
                                Contact Person Name <sup>*</sup>
                            </label>
                        </div>
                        <input
                            type="text"
                            name="contactPersonName"
                            placeholder="Enter your full name"
                            value={formikContact.values.contactPersonName}
                            onChange={formikContact.handleChange}
                        />
                        {formikContact.touched.contactPersonName &&
                            formikContact.errors.contactPersonName && (
                                <div className={css.error}>
                                    {formikContact.errors.contactPersonName}
                                </div>
                            )}
                    </div>

                    <div className={css.col03}>
                        <div className={css.labelDiv}>
                            <label>
                                Contact Person Title <sup>*</sup>
                            </label>
                        </div>
                        <input
                            type="text"
                            name="jobTitle"
                            placeholder="Enter your job title (e.g., Recruiter, Owner)"
                            value={formikContact.values.jobTitle}
                            onChange={formikContact.handleChange}
                        />
                        {formikContact.touched.jobTitle &&
                            formikContact.errors.jobTitle && (
                                <div className={css.error}>
                                    {formikContact.errors.jobTitle}
                                </div>
                            )}
                    </div>
                </div>

                <div className={css.formRow}>
                    <div className={css.col03}>
                        <div className={css.labelDiv}>
                            <label>Company Email Address</label>
                        </div>
                        <input
                            type="email"
                            name="companyEmailAddress"
                            value={companyValues.email}
                            placeholder="<EMAIL>"
                            disabled
                        />
                    </div>

                    <div className={css.col03}>
                        <div className={css.labelDiv}>
                            <label>
                                Company Phone Number
                                <span className={css.tooltipIcon}>
                                    <img src="/images/icons/icon-info.svg" alt="" />
                                    <span className={css.tooltip}>
                                       This number will be used for mandatory phone verification.
                                    </span>
                                </span>
                            </label>
                        </div>
                        <input
                            type="text"
                            name="companyPhoneNumber"
                            placeholder="(*************"
                            value={companyValues.phoneNumber}
                            disabled
                        />
                    </div>
                </div>

                <div className={`${css.formRow} ${css.submitRow}`}>
                    <button type="submit" className={css.submitBtn}>
                        Save and Continue
                    </button>
                </div>
            </form>
        </>
    )
}
export default ContactInformation