import React from "react";
import styles from "./InputFieldRange.module.scss";
import TextField from "../TextField";
import { FormikProps } from "formik";
import Tooltip from "../Tooltip";

interface InputRangeProps<T extends Record<string, unknown>> {
  className?: string;
  label?: string;
  desc?: string;
  fieldName: string;
  fieldName1: string;
  firstClass?: string;
  secondClass?: string;
  textName?: string;
  placeholder?: string;
  placeholder1?: string;
  hide?: boolean;
  formik: FormikProps<T>;
  tooltipMsg?: string;
  decimalAllowed?: boolean;
  handleChange?: (
    event: React.ChangeEvent<HTMLInputElement>,
    fieldName: string,
    formik: FormikProps<T>,
    decimalAllowed: boolean
  ) => void;
}

const InputFieldRange = <T extends Record<string, unknown>>({
  className = "",
  label = "",
  desc = "",
  fieldName = "",
  fieldName1 = "",
  firstClass = "",
  secondClass = "",
  textName = "",
  placeholder = "",
  placeholder1 = "",
  hide = false,
  formik,
  handleChange,
  tooltipMsg,
  decimalAllowed = false,
}: InputRangeProps<T>) => {
  return (
    <div className={`${styles.columnField} ${styles[className]}`}>
      {label && (
        <label htmlFor={fieldName}>
          {label} {desc && <span className={styles.desc}>{desc}</span>}{" "}
          {!hide && <span className={styles.important}>*</span>}
          {tooltipMsg && <Tooltip tooltipMsg={tooltipMsg} />}
        </label>
      )}
      <div className={styles.rangeField}>
        <TextField
          className={firstClass}
          fieldName={fieldName}
          placeholder={placeholder}
          formik={formik}
          handleChange={handleChange}
          decimalAllowed={decimalAllowed}
        />
        <span className={styles.textClass}>{textName}</span>
        <TextField
          className={secondClass}
          fieldName={fieldName1}
          placeholder={placeholder1}
          formik={formik}
          handleChange={handleChange}
          decimalAllowed={decimalAllowed}
        />
      </div>
    </div>
  );
};

export default InputFieldRange;
