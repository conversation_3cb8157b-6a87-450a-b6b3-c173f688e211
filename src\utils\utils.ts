import { deleteC<PERSON>ie, getCookie, setCookie } from "cookies-next";
import { FormikProps } from "formik";
import {
  bonusOfferArray,
  driverRecords,
  numDecimalRegex,
  numRegex,
  proficiencyOptions,
} from "./constant";
import {
  CleanConfig,
  DropdownItem,
  FieldCheck,
} from "@/types/jobpostingform";

export const isUserLoggedIn = (): boolean => {
  return !!getCookie("authToken");
};

export const numericInput = <T extends Record<string, unknown>>(
  e: React.ChangeEvent<HTMLInputElement>,
  fieldName: string,
  formik: FormikProps<T>,
  decimalAllowed?: boolean
) => {
  const inputValue = e.target.value;

  if (decimalAllowed) {
    if (inputValue === "" || numDecimalRegex.test(inputValue)) {
      formik.setFieldValue(fieldName, inputValue);
    }
  } else {
    if (inputValue === "" || numRegex.test(inputValue)) {
      formik.setFieldValue(fieldName, inputValue);
    }
  }
};

export const slugPayload: { [key: number]: string[] } = {
  1: [
    "job-title-job-posting-cdl",
    "employment-type-check-all-that-apply-job-posting-cdl",
    "route-type-job-posting-cdl",
    "primary-lanes-operating-area-job-posting-cdl",
    "home-time-frequency-job-posting-cdl",
    "typical-work-schedule-job-posting-cdl",
    "target-start-date-job-posting-cdl",
    "distance-range-job-posting-cdl",
  ],
  2: [
    "mileage-calculation-method-job-posting-cdl",
    "period-job-posting-cdl",
    "period-frequency-job-posting-cdl",
    "percentage-based-on-job-posting-cdl",
    "pay-schedule-job-posting-cdl",
    "benefits-package-includes-job-posting-cdl",
    "driver-perks-programs-job-posting-cdl",
    "time-unit-job-posting-cdl",
    "expenses-covered-job-posting-cdl",
    "overtime-job-posting-cdl",
  ],
  3: [
    "truck-type-primarily-operated-job-posting-cdl",
    "typical-truck-age-job-posting-cdl",
    "transmission-type-job-posting-cdl",
    "truck-assignment-job-posting-cdl",
    "truck-amenities-job-posting-cdl",
    "incab-technology-safety-features-job-posting-cdl",
    "eld-system-used-if-known-job-posting-cdl",
    "trailer-type-primarily-hauled-job-posting-cdl",
    "typical-trailer-age-optional-job-posting-cdl",
    "primary-freight-types-job-posting-cdl",
    "loadingunloading-requirements-job-posting-cdl",
    "drop-hook-percentage-estimate-if-applicable-job-posting-cdl",
  ],
  4: [
    "cdl-requirement-job-posting-cdl",
    "cdl-endorsements-job-posting-cdl",
    "experience-level-job-posting-cdl",
    "equipment-experience-type-job-posting-cdl",
    "driving-routes-and-experience-job-posting-cdl",
    "pre-employment-screening-testing-job-posting-cdl",
    "physical-abilities-requirements-job-posting-cdl",
    "ability-to-lift-carry-up-to-job-posting-cdl",
    "other-requirements-job-posting-cdl",
  ],
  5: [
    "key-responsibilities-job-posting-cdl",
    "interview-process-steps-job-posting-cdl",
    "hiring-process-timeline-estimate-job-posting-cdl",
  ],
};

type FlatFormValues = Record<string, string | number | null | undefined>;

export const cleanFlatFormValues = (values: FlatFormValues): FlatFormValues => {
  return Object.entries(values).reduce((acc, [key, value]) => {
    if (typeof value === "string") {
      acc[key] = value.trim() === "" ? null : value;
    } else {
      acc[key] = value;
    }
    return acc;
  }, {} as FlatFormValues);
};

// common try catch block
export async function safeFetch<T>(
  fn: () => Promise<T>,
  fallback: T
): Promise<T> {
  try {
    return await fn();
  } catch (err) {
    console.error("API fetch failed:", err);
    return fallback;
  }
}

export const saveCookieStore = (
  cookieName: string,
  value: string | number | boolean
) => {
  const expires = new Date(new Date().setMonth(new Date().getMonth() + 1)); // 1 month expiry added to cookie
  setCookie(cookieName, { path: "/", value, expires });
};

export const getCookieStore = (cookieName: string) => {
  getCookie(cookieName);
};

export const removeCookieStore = (cookieName: string) => {
  deleteCookie(cookieName);
};

export const clearStorage = () => {
  deleteCookie("jobCategory");
  deleteCookie("jobId");
};

// comparing initial values and get api values, and returning values which are similar
export const getCommonKeys = <T extends object, U extends object>(obj1: T, obj2: U): Partial<U> => {
  const result: Partial<U> = {};

  for (const key in obj2) {
    if (key in obj1) {
      result[key] = obj2[key];
    }
  }

  return result;
}

// removing null or undefined from api values
export const deepCleanValues = <T extends Record<string, unknown>>(
  obj: T
): Partial<T> => {
  const cleaned: Partial<T> = {};

  for (const key in obj) {
    if (!Object.prototype.hasOwnProperty.call(obj, key)) continue;

    const value = obj[key];

    if (value === null || value === undefined) {
      continue;
    }

    if (Array.isArray(value)) {
      const filteredArray = value
        .map((item) =>
          typeof item === "object" && item !== null
            ? deepCleanValues(item as Record<string, unknown>)
            : item
        )
        .filter((item) => {
          if (item === null || item === undefined) return false;
          if (typeof item === "object") return Object.keys(item).length > 0;
          return true;
        });

      if (filteredArray.length > 0) {
        cleaned[key as keyof T] = filteredArray as T[keyof T];
      }
    } else if (typeof value === "object") {
      const cleanedObject = deepCleanValues(value as Record<string, unknown>);
      if (Object.keys(cleanedObject).length > 0) {
        cleaned[key as keyof T] = cleanedObject as T[keyof T];
      }
    } else {
      if (typeof value === "string" && value.trim() !== "" && !isNaN(Number(value))) {
        cleaned[key as keyof T] = Number(value) as T[keyof T];
      } else {
        cleaned[key as keyof T] = value;
      }
    }
  }

  return cleaned;
};

// passing array values and removing other key value added and storing it in another key
export const handleArrayFields = <T extends Record<string, unknown>>(
  data: T,
  fieldsToSplit: CleanConfig
): T => {
  const updated: Record<string, unknown> = { ...data };

  Object.entries(fieldsToSplit).forEach(([field, config]) => {
    const value = data[field];

    if (!Array.isArray(value)) return;

    const numericValues: (string | number)[] = [];
    let removedItem: string | string[] = "";

    const shouldAddField1 =
      config.addField1 &&
      config.field1DependsOn &&
      data.hasOwnProperty(config.field1DependsOn);

    if (config.string) {
      const matchedItems = value.filter(
        (item) => typeof item === "string" && bonusOfferArray.includes(item)
      );

      const unmatchedItems = value.filter(
        (item) => typeof item === "string" && !bonusOfferArray.includes(item)
      );

      numericValues.push(...matchedItems);

      if (config.addField && unmatchedItems.length > 0) {
        numericValues.push(config.addField);
      }

      if (config.addField1 && unmatchedItems.length > 0) {
        numericValues.push(config.addField1);
      }

      updated[field] = Array.from(new Set(numericValues)); 

      if (config.outputKey && unmatchedItems.length > 0) {
        updated[config.outputKey] =
          unmatchedItems.length === 1 ? unmatchedItems[0] : "";
      }
    } else {
      value.forEach((item) => {
        if (typeof item === "string" && !isNaN(Number(item))) {
          numericValues.push(Number(item));
        } else {
          removedItem = item;        
        }
      });

      if (removedItem && config.addField) numericValues.push(config.addField);

      if (shouldAddField1 && config.addField1)
        numericValues.push(config.addField1);

      updated[field] = Array.from(new Set(numericValues));

      if (config.outputKey && removedItem) {
        updated[config.outputKey] = removedItem;
      }
    }
  });

  return updated as T;
};

export const normalizePerString = (input: string): string => {
  const stripped = input.replace(/^per\s+/i, "");

  return stripped.charAt(0).toLowerCase() + stripped.slice(1);
};

export const addPerPrefix = (input: string): string => {
  const trimmed = input.trim();

  const capitalized =
    trimmed.charAt(0).toUpperCase() + trimmed.slice(1).toLowerCase();

  return `Per ${capitalized}`;
};

export const languageReq = (
  languages: DropdownItem[],
  otherLanguages: DropdownItem[]
) => [
  {
    key: 1,
    fields: [
      {
        title: "Required Spoken Language",
        field: "languageId",
        defaultLabel: "Select Language",
        options: languages,
      },
      {
        title: "Proficiency",
        field: "proficiency",
        defaultLabel: "Select",
        options: proficiencyOptions,
      },
    ],
  },
  {
    key: 2,
    fields: [
      {
        title: "Other Spoken Language",
        field: "languageId",
        defaultLabel: "Select Language",
        options: otherLanguages,
      },
      {
        title: "Proficiency",
        field: "proficiency",
        defaultLabel: "Select",
        options: proficiencyOptions,
      },
    ],
  },
];

export const removeAllowedFields = <T extends Record<string, any>>(data: T): Partial<T> => {
  const cleanedData: any = {};

  driverRecords.forEach(({ key }) => {
    const item = data[key];

    if (item && typeof item === "object" && item.allowed === true) {
      cleanedData[key] = {
        count: item.count ?? null,
        years: item.years ?? null,
      } as T[typeof key];
    } else {
      cleanedData[key] = {
        count: null,
        years: null,
      } as T[typeof key];
    }
  });

  return cleanedData;
};

export const addAllowedFields = <T extends Record<string, any>>(data: T): Partial<T> => {
  const result: any = {};

  driverRecords.forEach(({ key }) => {
    const item = data[key] || {};

    const count = item.count ?? null;
    const years = item.years ?? null;

    const countValid = count !== "" && count !== "none" && count !== null && count !== undefined;
    const yearsValid = years !== "" && years !== "none" && years !== null && years !== undefined;

    result[key] = {
      count: countValid ? count : null,
      years: yearsValid ? years : null,
      allowed: (countValid && yearsValid) ? true : null,
    };
  });

  return result;
};

export const scrollToFirstError = (errors: Record<string, any>) => {
  const timeout = setTimeout(() => {
    const firstErrorKey = Object.keys(errors)[0];
    const error = document.querySelector(`[name="${firstErrorKey}"]`);

    if (error) {
      error.scrollIntoView({ behavior: "smooth", block: "center" });
      (error as HTMLElement).focus?.();
    }
  }, 100);

  return () => clearTimeout(timeout);
};

export const cleanHiddenFields = (
  values: Record<string, any>,
  hiddenFieldConfig: FieldCheck[]
) => {
  const cleaned = { ...values };

  hiddenFieldConfig.forEach(({ fields, condition }) => {
    if (condition(values)) {
      fields.forEach((field) => {
        cleaned[field] = null;
      });
    }
  });

  return cleaned;
};
