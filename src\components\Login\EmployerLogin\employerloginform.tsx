"use client";
import Link from "next/link";
import { useFormik } from "formik";
import * as Yup from "yup";
import css from "./employerloginform.module.scss";
import { loginUser } from "@/services/userService";
import { saveToken } from "@/utils/loginRegister";
import { useRouter } from "next/navigation";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { useState } from "react";

interface FormValues {
  email: string;
  password: string;
  agree?: boolean;
}

interface EmployerLoginFormProps {
  type?: string;
}

const EmployerLoginForm = ({ type = '' }: EmployerLoginFormProps) => {
  const [showPassword, setShowPassword] = useState(false)
  const router = useRouter();
  const formik = useFormik({
    initialValues: {
      email: "",
      password: "",
      agree: false,
    },
    validationSchema: Yup.object({
      email: Yup.string().email("Invalid email address").required("Required"),
      password: Yup.string().required("Required"),
      agree: Yup.boolean().oneOf([true], "You must accept to continue"),
    }),
    onSubmit: (values) => {
      console.log("Form submitted:", values);
      handleSubmit(values);
    },
  });

  const handleSubmit = async (values: FormValues) => {
    const payload = {
      emailOrContact: values.email,
      password: values.password,
    };

    try {
      const res = await loginUser(payload);
      if (res?.status) {
        toast.success("Login successful!");
        saveToken(
          res?.data?.user?.accessToken,
          res?.data?.user?.refreshToken,
          values.email,
          res?.data?.user?.isCompany
        );
        setTimeout(() => {
          router.push("/");
        }, 2000);
      } else {
        toast.error(res?.error?.message || "Login failed");
      }
    } catch (err) {
      toast.error(`An error occurred. Please try again.${err}`);
    }
  };

  return (
    <section className={css.registrationSection}>
        <div className={css.loginWrapper}>
          <h1>Find your ideal drivers. Sign in to access our qualified talent pool.</h1>
          <p>
            
          </p>
          <form onSubmit={formik.handleSubmit} className={css.registrationForm}>
            <div className={css.formGroup}>
              <label>Email</label>
              <input
                id="email"
                name="email"
                type="email"
                placeholder="Enter your email"
                onChange={formik.handleChange}
            
                value={formik.values.email}
              />
              {formik.touched.email && formik.errors.email ? (
                <span className={css.error}>{formik.errors.email}</span>
              ) : null}
            </div>
            <div className={css.formGroup}>
                <label htmlFor="password">Password</label>
                <input
                  id="password"
                  name="password"
                  type={showPassword ? "text":"password"}
                  placeholder="Enter your password"
                  onChange={formik.handleChange}
              
                  value={formik.values.password}
                />
                <button type="button" className={css.showPassword} onClick={()=>setShowPassword(!showPassword)}>
                 {showPassword ? <FaEye /> : <FaEyeSlash />}
                </button>
                {formik.touched.password && formik.errors.password ? (
                  <span className={css.error}>{formik.errors.password}</span>
                ) : null}
            </div>
            <div className={css.formGroup}>
              <div className={css.checkBox}>
                <input
                  type="checkbox"
                  id="agree"
                  name="agree"
                  onChange={formik.handleChange}
                
                  checked={formik.values.agree}
                />
                <span className={css.checkmark}></span>
                <p>I agree to receive updates,
                marketing emails and offers from Driverjobz</p>
                {formik.touched.agree && formik.errors.agree ? (
                <span className={css.error}>{formik.errors.agree}</span>
              ) : null}
            </div>
            </div>
            <div className={css.formGroup}>
              <button type="submit" className={css.submitBtn}>Continue</button>
              <span className={css.forgotPassword}>
                <Link href="/forgot-password" className={css.forgotLink}>
                  Forgot Password?
                </Link>
              </span>
              <p>
                {`Don't have an account?`}{" "}
                <Link href={type==='driver' ? "/register/driver" : "/register/company"} className={css.registerLink}>
                  Register
                </Link>
              </p>
            </div>
          </form>
          <ToastContainer
            position="top-right"
            autoClose={3000}
            hideProgressBar={false}
          />
        </div>
    </section>
  );
};

export default EmployerLoginForm;
