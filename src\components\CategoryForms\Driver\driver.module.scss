@import '../../../styles/global.scss';

// .driverCategoryForm {
//     padding: 2rem;
//     max-width: 1200px;
//     margin: 0 auto;

//     .container {
//         width: 100%;
//     }

//     .backButtonContainer {
//         margin-bottom: 1rem;
//     }

//     .backButton {
//         padding: 0.5rem 1rem;
//         background-color: #6c757d;
//         color: white;
//         border: none;
//         border-radius: 4px;
//         cursor: pointer;
//         font-size: 14px;
//         display: flex;
//         align-items: center;
//         gap: 0.5rem;
//         transition: background-color 0.2s ease;

//         &:hover {
//             background-color: #5a6268;
//         }

//         &:active {
//             background-color: #545b62;
//         }
//     }

//     .required {
//         color: $black;
//         font-size: 14px;
//         margin-bottom: 2rem;

//         span {
//             color: #F91313;
//         }
//     }

//     .progressSteps {
//         display: flex;
//         align-items: center;
//         justify-content: center;
//         margin-bottom: 3rem;
//         padding: 2rem 0;
//         background: #f8f9fa;
//         border-radius: 8px;

//         .stepItem {
//             display: flex;
//             flex-direction: column;
//             align-items: center;
//             position: relative;
//             flex: 1;
//             max-width: 200px;

//             .stepCircle {
//                 width: 40px;
//                 height: 40px;
//                 border-radius: 50%;
//                 display: flex;
//                 align-items: center;
//                 justify-content: center;
//                 font-weight: 600;
//                 font-size: 16px;
//                 margin-bottom: 0.5rem;
//                 position: relative;
//                 z-index: 2;

//                 &.completed {
//                     background-color: #28a745;
//                     color: white;

//                     img {
//                         width: 20px;
//                         height: 20px;
//                         filter: brightness(0) invert(1);
//                     }
//                 }

//                 &.active {
//                     background-color: #007bff;
//                     color: white;
//                 }

//                 &.inactive {
//                     background-color: #e9ecef;
//                     color: #6c757d;
//                     border: 2px solid #dee2e6;
//                 }
//             }

//             .stepTitle {
//                 font-size: 12px;
//                 font-weight: 500;
//                 text-align: center;
//                 color: #495057;
//                 max-width: 120px;
//                 line-height: 1.3;
//             }

//             .stepLine {
//                 position: absolute;
//                 top: 20px;
//                 left: 50%;
//                 width: 100%;
//                 height: 2px;
//                 z-index: 1;

//                 &.completed {
//                     background-color: #28a745;
//                 }

//                 &.inactive {
//                     background-color: #dee2e6;
//                 }
//             }

//             &:last-child .stepLine {
//                 display: none;
//             }
//         }
//     }

//     .stepContent {
//         background: white;
//         border-radius: 8px;
//         padding: 2rem;
//         box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

//         h2 {
//             margin-bottom: 2rem;
//             color: $black;
//             font-size: 24px;
//             font-weight: 600;
//         }
//     }

//     @media (max-width: 768px) {
//         padding: 1rem;

//         .progressSteps {
//             .stepItem {
//                 max-width: 150px;

//                 .stepCircle {
//                     width: 32px;
//                     height: 32px;
//                     font-size: 14px;
//                 }

//                 .stepTitle {
//                     font-size: 11px;
//                     max-width: 100px;
//                 }
//             }
//         }

//         .stepContent {
//             padding: 1rem;

//             h2 {
//                 font-size: 20px;
//                 margin-bottom: 1rem;
//             }
//         }
//     }
// }

.driverCategoryForm {
    padding-top: 28px;
    padding-bottom: 70px;

    .container {
        @include container;
    }
}


.allSteps {
    display: flex;
    position: relative;

    &:after {
        background-color: #E5E5E5;
        content: '';
        width: 100%;
        height: 2px;
        position: absolute;
        top: 16px;
        left: 0px;
        right: 0px;
        z-index: 1;
    }

    li {
        width: calc(100% / 5);
        display: inline-flex;
        flex-direction: column;
        justify-content: center;
        gap: 6px;

        &:last-child {
            &:after {
                display: none;
            }
        }

        figure {
            background-color: #E5E5E5;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            line-height: 0px;
            width: 28px;
            height: 28px;
            position: relative;
            z-index: 3;
            font-family: Epilogue;

            img {
                width: 100%;
            }
            
            span {
                color: #9D9D9D;
                font-family: "Montserrat", sans-serif;
                font-weight: 500;
                font-size: 14px;
                line-height: 16px;
                letter-spacing: 0.5px;
            }

            &.active {
                background-image: url('/images/icons/icon-tick-green.svg');
                background-size: 100%;

                span {
                    display: none;
                }
            }
        }

        p {
            color: #9D9D9D;
            font-weight: 500;
            font-style: Medium;
            font-size: 14px;
            line-height: 16px;
            letter-spacing: 0.5px;

        }
    }
}

.required {
    color: $black;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    margin-top: 16px;

    sup {
        color: #ff0000;
    }
}

.stepContent {
    margin-top: 32px;

    h2 {
        color: $black;
        font-weight: 700;
        font-size: 24px;
        line-height: 32px;
        margin-bottom: 24px;
    }
}