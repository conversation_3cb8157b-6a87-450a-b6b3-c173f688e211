// import NemtDriver from "@/components/CategoryForms/NemtDriver";

// export default async function NemtDriverCategory(){
//     return(
//         <>
//         <NemtDriver/>
//         </>
//     )
// }
import { redirect } from "next/navigation";
import { cookies } from "next/headers";
import { fetchDriverDetailsAndCheckAccess } from "@/utils/categoryAccessControl";
import NemtDriver from "@/components/CategoryForms/NemtDriver";

export default async function NemtDriverCategory({ params }: {params: Promise<{ lang: "en" | "es" }> }) {
    const { lang } = await params;
    const cookieStore = await cookies();
    const token = cookieStore.get("authToken");

    if (!token) {
        redirect(`/${lang}`);
    }

    let data;
    try {
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_V1}auth/me`, {
            method: 'GET',
            headers: {
                'Authorization': token.value,
                'Content-Type': 'application/json',
            },
            cache: 'no-store',
        });
        data = await res.json();
    } catch (fetchError) {
        console.error("Error fetching profile:", fetchError);
        redirect(`/${lang}`);
    }

    if (!data?.status || !data?.data?.user) {
        redirect(`/${lang}`);
    }

    const user = data.data.user;
    if (user.isCompany) {
        redirect(`/${lang}`);
    }
    await fetchDriverDetailsAndCheckAccess(token.value, 97, lang, 'nemt-driver category');

    return (
        <>
            <NemtDriver/>
        </>
    );
};
