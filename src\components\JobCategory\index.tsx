"use client";

import React from "react";
import DropdownField from "../Common/Form/DropdownField";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import * as Yup from "yup";
import styles from "./JobCategory.module.scss";
import ButtonComponent from "../Common/Form/ButtonComponent";
import { DropdownItem } from "../../types/jobpostingform";
import { setCookie } from "cookies-next";

interface CategoryProps {
    categories: DropdownItem[];
}

type CategoryForm = {
  category: string | number;
}

const JobCategory = ({ categories }: CategoryProps) => {
  const router = useRouter();

  const formik = useFormik<CategoryForm>({
    initialValues: {
      category: "",
    },
    validationSchema: Yup.object({
        category: Yup.string().required("Please select job category"),
    }),

    onSubmit: async (values: CategoryForm) => {
      setCookie("jobCategory", values?.category);
      router.push("/job-posting");
    },
  });

  return (
    <div className={styles.container}>
      <form onSubmit={formik.handleSubmit}>
        <div className={styles.jobWrapper}>
          <h2 className={styles.title}>Job Posting Category</h2>
          <p className={styles.subText}>Choose a job category for which you need to post a job</p>
          <DropdownField
            label="Choose Job Category"
            fieldName="category"
            defaultLabel="Select job category"
            formik={formik}
            dropdownArray={categories}
          />
          <ButtonComponent 
            className="jobCategoryClass"
            backText=""
            backBtn
            btnText="" 
            btnText1="Continue" 
        />
        </div>
      </form>
    </div>
  );
};

export default JobCategory;
