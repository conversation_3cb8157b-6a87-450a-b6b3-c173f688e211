"use client";
import React, { useEffect, useRef } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { submitDriverDetails } from "@/services/driverFormService";
// import { DriverDetailsResponse } from "..";
import {DriverSafety} from '../types'
import { toast } from "react-toastify";
import css from '../driverRegistration.module.scss';
 
interface Props {
  onFormSubmit: () => void;
  initialData: DriverSafety | null
  currentStep:number
  currentStage:number
}
const Safety = ({ onFormSubmit, initialData ,currentStep,currentStage}: Props) => {
  const [accidentDropdownOpen, setAccidentDropdownOpen] = React.useState(false);
const [violationDropdownOpen, setViolationDropdownOpen] = React.useState(false);

  const accidentOptions = ["1", "2", "3", "more than 3"];
const violationOptions = ["1", "2", "3", "4 or more"];


  const accidentRef = useRef<HTMLDivElement>(null);
  const violationRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        accidentRef.current &&
        !accidentRef.current.contains(e.target as Node)
      ) {
        setAccidentDropdownOpen(false);
      }
      if (
        violationRef.current &&
        !violationRef.current.contains(e.target as Node)
      ) {
        setViolationDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);



  const formik = useFormik({
    initialValues: {
      hadAccidents: "no",
      numberOfAccidents: "",
      accidentDetails: "",

      hadViolations: "no",
      numberOfViolations: "",
      violationDetails: "",

      licenseSuspended: "no",
      suspensionDetails: "",
    },
    enableReinitialize: true,
    validationSchema: Yup.object({
      hadAccidents: Yup.string().required(),
      numberOfAccidents: Yup.string().when("hadAccidents", {
        is: "yes",
        then: (schema) => schema.required("Please select number of accidents"),
        otherwise: (schema) => schema.notRequired(),
      }),
      hadViolations: Yup.string().required(),
      numberOfViolations: Yup.string().when("hadViolations", {
        is: "yes",
        then: (schema) => schema.required("Please select number of violations"),
        otherwise: (schema) => schema.notRequired(),
      }),
      licenseSuspended: Yup.string().required(),
    }),
    validateOnChange: false,
    validateOnBlur: true,
    onSubmit: async (values) => {
      const step=5;
      const payload = {
        ...(currentStage <= 2 && { currentStage: 2 }),
        ...(currentStage === 2 && step >= currentStep && { currentStep: step }),
        driver: {
          isAccidentLast3Years: values.hadAccidents === "yes",
          numberOfAccidentsLast3Years:
            values.hadAccidents === "yes"
              ? values.numberOfAccidents === "more than 3"
                ? 4
                : parseInt(values.numberOfAccidents)
              : null,
          briefDescriptionOfAccidentsLast3Years:
            values.hadAccidents === "yes" ? values.accidentDetails : null,

          isTrafficViolationLast3Years: values.hadViolations === "yes",
          numberOfTrafficViolationsLast3Years:
            values.hadViolations === "yes"
              ? values.numberOfViolations === "4 or more"
                ? 4
                : parseInt(values.numberOfViolations)
              : null,
          briefDescriptionOfTrafficViolationsLast3Years:
            values.hadViolations === "yes" ? values.violationDetails : null,

          isDriverLicenseSuspended: values.licenseSuspended === "yes",
          briefDescriptionOfSuspension:
            values.licenseSuspended === "yes" ? values.suspensionDetails : null,
        },
      };

      try {
        const response = await submitDriverDetails(payload);
        console.log("Submitted:", response);
        toast.success("Driver Safety & compliance details submitted successfully");
        onFormSubmit();
      } catch (err: unknown) {
        if (err instanceof Error) {
          toast.error( "Failed to submit safety & compliance details");
        } else {
          toast.error(" Something went wrong. Please try again");
        }
      }
    },
  });
useEffect(() => {
  if (!initialData) return;

  formik.setValues({
    hadAccidents: initialData.isAccidentLast3Years ? "yes" : "no",
    numberOfAccidents:
      initialData.isAccidentLast3Years &&
      Number(initialData.numberOfAccidentsLast3Years) >= 4
        ? "more than 3"
        : initialData.numberOfAccidentsLast3Years?.toString() || "",

    accidentDetails: initialData.briefDescriptionOfAccidentsLast3Years || "",

    hadViolations: initialData.isTrafficViolationLast3Years ? "yes" : "no",
    numberOfViolations:
      initialData.isTrafficViolationLast3Years &&
      Number(initialData.numberOfTrafficViolationsLast3Years) >= 4
        ? "4 or more"
        : initialData.numberOfTrafficViolationsLast3Years?.toString() || "",

    violationDetails: initialData.briefDescriptionOfTrafficViolationsLast3Years || "",

    licenseSuspended: initialData.isDriverLicenseSuspended ? "yes" : "no",
    suspensionDetails: initialData.briefDescriptionOfSuspension || "",
  });
}, [initialData]);


  return (
    <form onSubmit={formik.handleSubmit} className={css.commonForm}>
      {/* Accidents Section */}
      <div className={`${css.formRow} ${css.dBlaco}`}>
        <div className={css.labelDiv}>
          <label>
              Accidents (Any Vehicle, Last 3 Yrs)
              <span className={css.tooltipIcon}>
                <img src="/images/icons/icon-info.svg" alt="" />
                <span className={css.tooltip}>
                    Select the general geographic areas your company serves.
                </span>
              </span>
          </label>
        </div>
        <ul className={`${css.checkboxList} ${css.radioList}`}>
          <li className={css.radioGroup}>
            <label className={css.radioLabel}>
                <input
                    type="radio"
                    name="hadAccidents"
                    value="yes"
                    checked={formik.values.hadAccidents === "yes"}
                    onChange={formik.handleChange}
                  />
                <span className={css.checkmark}></span>
                <p>Yes</p>
            </label>
          </li>
          <li className={css.radioGroup}>
            <label className={css.radioLabel}>
                <input
                  type="radio"
                  name="hadAccidents"
                  value="no"
                  checked={formik.values.hadAccidents === "no"}
                  onChange={formik.handleChange}
                />
                <span className={css.checkmark}></span>
                <p>No</p>
            </label>
          </li>
        </ul>
      </div>
      {formik.values.hadAccidents === "yes" && (
      <>
        <div className={css.formRow}>
          <div className={css.col03}>
            <div className={css.labelDiv}>
              <label>
                  Number of Accidents (Last 3 Yrs)<sup>*</sup>
              </label>
            </div>
       <div className={css.dropdown} ref={accidentRef} >
  <button
    type="button"
    className={css.dropdownToggle}
    onClick={() => setAccidentDropdownOpen(!accidentDropdownOpen)}
  >
    {formik.values.numberOfAccidents || "Select"}
  </button>
  {accidentDropdownOpen && (
    <div className={css.dropdownMenu}>
      {accidentOptions.map((option) => (
        <button
          key={option}
          type="button"
          className={css.dropdownItem}
          onClick={() => {
            formik.setFieldValue("numberOfAccidents", option);
            setAccidentDropdownOpen(false);
          }}
        >
          {option}
        </button>
      ))}
    </div>
  )}
</div>
{formik.touched.numberOfAccidents &&
  formik.errors.numberOfAccidents && (
    <div className={css.error}>{formik.errors.numberOfAccidents}</div>
)}

            {/* <select
              name="numberOfAccidents"
              value={formik.values.numberOfAccidents}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              style={{ display: "block", marginTop: "0.5rem" }}
            >
              <option value="">-- Select --</option>
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="more than 3">More than 3</option>
            </select> */}
          </div>
        </div>
        <div className={css.formRow}>
              <div className={css.col02}>
                <div className={css.labelDiv}>
                  <label>
                      Brief Explanation <span>(Optional, but Recommended)</span>
                  </label>
                </div> 
                <textarea
                    name="accidentDetails"
                    value={formik.values.accidentDetails}
                    onChange={formik.handleChange}
                    maxLength={500}
                    style={{ width: "100%" }}
                  />
                  <div className={css.characterLimit}>{formik.values.accidentDetails.length}/500</div>
              </div>
        </div>
      </>
      )}

      {/* Violations Section */}
      <div className={`${css.formRow} ${css.dBlaco}`}>
        <div className={css.labelDiv}>
          <label>
              Moving Violations (Any Vehicle, Last 3 Yrs)
              <span className={css.tooltipIcon}>
                <img src="/images/icons/icon-info.svg" alt="" />
                <span className={css.tooltip}>
                    Select the general geographic areas your company serves.
                </span>
              </span>
          </label>
        </div>
        <ul className={`${css.checkboxList} ${css.radioList}`}>
          <li className={css.radioGroup}>
            <label className={css.radioLabel}>
                <input
                  type="radio"
                  name="hadViolations"
                  value="yes"
                  checked={formik.values.hadViolations === "yes"}
                  onChange={formik.handleChange}
                />
                <span className={css.checkmark}></span>
                <p>Yes</p>
            </label>
          </li>
          <li className={css.radioGroup}>
            <label className={css.radioLabel}>
                <input
                  type="radio"
                  name="hadViolations"
                  value="no"
                  checked={formik.values.hadViolations === "no"}
                  onChange={formik.handleChange}
                />
                <span className={css.checkmark}></span>
                <p>No</p>
            </label>
          </li>
        </ul>
      </div>
      {formik.values.hadViolations === "yes" && (
        <>
          <div className={css.formRow}>
            <div className={css.col03}>
              <div className={css.labelDiv}>
                <label>
                    Number of Moving Violations (Last 3 Yrs)<sup>*</sup>
                </label>
              </div>
             <div className={css.dropdown} ref={violationRef}>
  <button
    type="button"
    className={css.dropdownToggle}
    onClick={() => setViolationDropdownOpen(!violationDropdownOpen)}
  >
    {formik.values.numberOfViolations || "Select"}
  </button>
  {violationDropdownOpen && (
    <div className={css.dropdownMenu}>
      {violationOptions.map((option) => (
        <button
          key={option}
          type="button"
          className={css.dropdownItem}
          onClick={() => {
            formik.setFieldValue("numberOfViolations", option);
            setViolationDropdownOpen(false);
          }}
        >
          {option}
        </button>
      ))}
    </div>
  )}
</div>
{formik.touched.numberOfViolations &&
  formik.errors.numberOfViolations && (
    <div className={css.error}>{formik.errors.numberOfViolations}</div>
)}


              {/* <select
                name="numberOfViolations"
                value={formik.values.numberOfViolations}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                style={{ display: "block", marginTop: "0.5rem" }}
              >
                <option value="">-- Select --</option>
                <option value="1">1</option>
                <option value="2">2</option>
                <option value="3">3</option>
                <option value="4 or more">4 or more</option>
              </select> */}
            </div>
          </div>
          <div className={css.formRow}>
                <div className={css.col02}>
                  <div className={css.labelDiv}>
                    <label>
                        Brief Explanation <span>(Optional, but Recommended)</span>
                    </label>
                  </div> 
                  <textarea
                    name="violationDetails"
                    value={formik.values.violationDetails}
                    onChange={formik.handleChange}
                    maxLength={500}
                    style={{ width: "100%" }}
                  />
                    <div className={css.characterLimit}>{formik.values.violationDetails.length}/500</div>
                </div>
          </div>
        </>
      )}

      {/* License Suspension Section */}
      <div className={`${css.formRow} ${css.dBlaco}`}>
        <div className={css.labelDiv}>
          <label>
              Has your driver&apos;s license ever been suspended or revoked?
              <span className={css.tooltipIcon}>
                <img src="/images/icons/icon-info.svg" alt="" />
                <span className={css.tooltip}>
                    Select the general geographic areas your company serves.
                </span>
              </span>
          </label>
        </div>
        <ul className={`${css.checkboxList} ${css.radioList}`}>
          <li className={css.radioGroup}>
            <label className={css.radioLabel}>
                <input
                  type="radio"
                  name="licenseSuspended"
                  value="yes"
                  checked={formik.values.licenseSuspended === "yes"}
                  onChange={formik.handleChange}
                />
                <span className={css.checkmark}></span>
                <p>Yes</p>
            </label>
          </li>
          <li className={css.radioGroup}>
            <label className={css.radioLabel}>
                <input
                  type="radio"
                  name="licenseSuspended"
                  value="no"
                  checked={formik.values.licenseSuspended === "no"}
                  onChange={formik.handleChange}
                />
                <span className={css.checkmark}></span>
                <p>No</p>
            </label>
          </li>
        </ul>
      </div>
      {formik.values.licenseSuspended === "yes" && (
          <div className={css.formRow}>
            <div className={css.col02}>
              <div className={css.labelDiv}>
                <label>
                    Brief Explanation <span>(Optional, but Recommended)</span>
                </label>
              </div> 
              <textarea
                name="suspensionDetails"
                value={formik.values.suspensionDetails}
                onChange={formik.handleChange}
                maxLength={500}
                style={{ width: "100%" }}
              />
              <div className={css.characterLimit}>{formik.values.suspensionDetails.length}/500</div>
            </div>
          </div>
      )}

      <div className={`${css.formRow} ${css.submitRow}`}>
          <button type="submit" className={css.submitBtn}>
            Save and Continue
        </button>
      </div>
    </form>
  );
};

export default Safety;
