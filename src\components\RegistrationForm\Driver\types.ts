export type DriverBasicInfo ={
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  street: string;
  apartmentNumber: string;
  zipCode: number;
  city: string;
  state: string;
  isLegallyAuthorizedToWorkInUs: boolean;
  futureSponsorshipNeeded: boolean;
}


export type DriverCategoryData ={
  driverCategory: number;
}

export type DriverLicenseFormValues ={

  driverLicenseClass?: number;
  driverLicenseState?: number;
  driverLicenseNumber?: string |null;
  driverLicenseExpiration?: string;
  driverLicenseEndorsements?: number[];
  driverLicenseRestrictions?: number[];
 
}



export type SpecialPermit ={
  isSpecialLicenses: boolean;
  driverSpecialLicense: {
    driverSpecialLicenseId?: number;
    nameOfSpecialLicensesOrCertifications: string;
    specialLicensesOrCertificationsNumber: string;
  }[];
}

export type LanguageSkillsType ={
   driverLanguages?: {
    driverLanguageId?: number;
    languageId: string | number;
    proficiency: string;
  }[];
}
export type DriverSafety= {
  isAccidentLast3Years: boolean;
  numberOfAccidentsLast3Years?: number ;
  briefDescriptionOfAccidentsLast3Years?: string;
  isTrafficViolationLast3Years: boolean;
  numberOfTrafficViolationsLast3Years?: number;
  briefDescriptionOfTrafficViolationsLast3Years?: string;
  isDriverLicenseSuspended: boolean;
  briefDescriptionOfSuspension?: string;
}

// export type FormInitialValues = {
//   basicInfo: DriverBasicInfo;
//   driverCategory: DriverCategoryData;
//   driverLicense: DriverLicenseFormValues;
//   specialPermit: SpecialPermit;
//   safety:Safety
// };
export type DriverDetailsResponse = DriverBasicInfo &
  DriverCategoryData &
  DriverLicenseFormValues &
  LanguageSkillsType&
  SpecialPermit &
  DriverSafety;
