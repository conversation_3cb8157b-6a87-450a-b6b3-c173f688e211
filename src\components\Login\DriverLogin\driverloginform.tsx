// "use client"
// import <PERSON> from "next/link";
// import css from "./driverloginform.module.scss";
// import { useFormik } from "formik";
// import * as Yup from "yup";
// import Button from "@/components/Common/Buttons/buttons";

// const DriverLoginForm = () => {

//   // use formik hook 
//   const formik = useFormik({
//     initialValues: {
//       email: "",
//       mobile: "",
//       password: "",
//       agree: false,
//     },
//     validationSchema: Yup.object({
//       email: Yup.string().email("Invalid email address").required("Email is required"),
//       mobile: Yup.string().required("Mobile number is required"),
//       password: Yup.string().min(6, "Password should be at least 6 characters").required("Password is required"),
//       agree: Yup.boolean().oneOf([true], "You must agree to the terms and conditions").required("Agreement is required"),
//     }),
//     onSubmit: (values) => {
//       console.log("Form Submitted:", values);
//     },
//   });

//   return (
//     <section className={css.registrationSection}>
//       <div className={css.loginWrapper}>
//         <h1>Drive your career forward. Sign in to access opportunities</h1>
//         <p>{`Don't have an account?`}{" "}<Link href="/register/driver" className={css.registerLink}>Register</Link></p>
//         {/* Formik form */} 
//         <form onSubmit={formik.handleSubmit} className={css.registrationForm}>
//           <div className={css.formGroup}>
//             <label htmlFor="email">Email</label>
//             <input
//               type="email"
//               id="email"
//               name="email"
//               placeholder="Enter your email"
//               value={formik.values.email}
//               onChange={formik.handleChange}
//               onBlur={formik.handleBlur}
//             />

//             {formik.touched.email && formik.errors.email ? (
//               <div className={css.error}> {formik.errors.email}</div>
//             ) : null}
//           </div>
//           <div className={`${css.formGroup} ${css.orText}`}>OR</div>
//           <div className={css.formGroup}>
//             <label htmlFor="mobile">Mobile</label>
//             <input
//               type="text"
//               id="mobile"
//               name="mobile"
//               placeholder="Enter your mobile"
//               value={formik.values.mobile}
//               onChange={formik.handleChange}
//               onBlur={formik.handleBlur}
//             />

//             {formik.touched.mobile && formik.errors.mobile ? (
//               <div className={css.error}>{formik.errors.mobile}</div>
//             ) : null}
//           </div>
//           <div className={css.formGroup}>
//               <label htmlFor="password">Password</label>
//               <input
//                 type="password"
//                 id="password"
//                 name="password"
//                 placeholder="Enter your password"
//                 value={formik.values.password}
//                 onChange={formik.handleChange}
//                 onBlur={formik.handleBlur}
//               />

//               {formik.touched.password && formik.errors.password ? (
//                 <div className={css.error}>{formik.errors.password}</div>
//               ) : null}
//           </div>

//           <div className={css.formGroup}>
//             <div className={css.checkBox}>
//               <input 
//                 type="checkbox" 
//                 id="agree" 
//                 name="agree" 
//                 checked={formik.values.agree} 
//                 onChange={formik.handleChange}
//               />
//               <span className={css.checkmark}></span>
//               <p>I agree to receive updates, marketing emails and offers from Driverjobz</p>
//             </div>

//             {formik.touched.agree && formik.errors.agree ? (
//               <div className={css.error}>{formik.errors.agree}</div>
//             ) : null}
//           </div>

//           <div className={css.formGroup}>
//             <Button type="submit" label="Continue" onClick={formik.handleSubmit} />
//             <span className={css.forgotPassword}>
//               <Link href="/forgot-password" className={css.forgotLink}>Forgot Password?</Link>
//             </span>
//           </div>
//         </form>
//       </div>
//     </section>
//   );
// };

// export default DriverLoginForm;
