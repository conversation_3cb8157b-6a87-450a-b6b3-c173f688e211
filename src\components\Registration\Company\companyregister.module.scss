@import '../../../styles/global.scss';

.loginContainer {
    
    top: 154px;
    left: 316px;
    border: 1.5px solid #E5E5E5;
    border-radius: 16px;
    margin-top: 40px;
}

.loginHeader {
    text-align: center;
    display: flex;
    flex-direction: column;
    width: 699px;
    height: 74px;
    margin-top: 80px;
    left: 371px;
    gap: 16px;

    .mainHeading {
        font-size: 22px;
        font-family: Epilogue;
        font-weight: 500;
        font-size: 22px;
        line-height: 140%;
        letter-spacing: 0%;
        vertical-align: middle;
        color: #2a2a2a;
    }

    .subHeading {
        font-family: Epilogue;
        font-weight: 400;
        font-size: 19px;
        line-height: 140%;
        letter-spacing: 0%;
        text-align: center;
        vertical-align: middle;
        color: #707070;

        .registerLink {
            font-weight: bold;
            color: #000000;
            text-decoration: none;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}

.employerInputContainer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    width: 660px;
    margin-top: 16px;
    gap: 32px;
    height: 70px;

    .inputGroup {
        display: flex;
        flex-direction: column;
        flex: 1;

        label {
            font-weight: 500;
            margin-bottom: 6px;
        }

        input {
            width: 314px;
            height: 44px;
            gap: 8px;
            border-radius: 4px;
            border-width: 1px;
            padding: 10px 8px;
            border-color: #707070;

            &::placeholder {
                font-family: Epilogue;
                font-weight: 400;
                font-size: 16px;
                line-height: 24px;
                letter-spacing: 0%;
                vertical-align: middle;
                color: #707070;
                // optional
            }
        }

        .passwordWrapper {
            position: relative;

            input {
                width: 100%;
                padding-right: 2.5rem; // space for the eye icon
            }
        }

        .eyeIcon {
            position: absolute;
            top: 50%;
            right: 0.75rem;
            transform: translateY(-50%);
            cursor: pointer;
            color: #888;
        }
    }
}

.checkboxContainer {
    display: flex;
    margin-top: 16px;
    width: 518px;
    height: 24px;
}

.checkboxLabel {
    display: flex;
    gap: 8px;
    white-space: nowrap;
    align-items: center;
    font-weight: 400;
    font-size: 14px;
    line-height: 160%;
    letter-spacing: 0%;
    color: #000000;
    cursor: pointer;

    .checkbox {
        width: 18px;
        height: 18px;

        accent-color: black;
        cursor: pointer;
    }
}

.buttonWrapper {
    display: flex;
    flex-direction: column;
    width: 660px;
    height: 88px;
    top: 466px;
    left: 374px;
    gap: 16px;
    margin-top: 44px;
    margin-bottom: 208px;

    .submitBtn {
        all: unset;
        text-align: center;
        width: 660;
        height: 50;
        gap: 10px;
        border-radius: 4px;
        padding: 12px;
        background: #FBD758;

        font-weight: 500;
        font-size: 18px;
        line-height: 120%;
        letter-spacing: 0%;


    }
}

.forgotLink {
    font-weight: 400px;
    font-size: 16px;
    line-height: 140%;
    letter-spacing: 0%;
    text-align: right;
}

.button {
    background: #FBD758;
    width: 660;
    height: 50;
    top: 776px;
    left: 392px;
    gap: 16px;

}

.error {
    color: red;
    font-size: 12px;
    margin-top: 2px;
}