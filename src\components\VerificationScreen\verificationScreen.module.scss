@import '../../styles/global.scss';

.registrationSection {
    padding: 56px 0px 40px;
    width: 100%;

    .loginWrapper {
        background-color: $white;
        border: 1px solid #E5E5E5;
        border-radius: 16px;
        width: 100%;
        max-width: 804px;
        margin: 0 auto;
        padding: 40px 0px 52px;

        h1 {
            font-weight: 500;
            font-size: 24px;
            line-height: 32px;
            color: $dark;
            text-align: center;
            max-width: 600px;
            margin: 0px auto;
        }

        p {
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            text-align: center;
            margin: 12px auto 0px;
            max-width: 400px;

            a {
              color: $black;
              font-weight: 600;
            }
        }
    }

    .registrationForm {
        display: flex;
        flex-wrap: wrap;
        column-gap: 24px;
        row-gap: 36px;
        max-width: 600px;
        margin: 32px auto 0px;

        .formGroup {
            position: relative;
            display: flex;
            flex-wrap: wrap;
            gap: 24px;
            width: 100%;

            .col6 {
              width: calc((100% - 24px) / 2);
              position: relative;
            }

            &:has( .error) {
              input {
                border-color: #F91313;
              }
            }
        }

        .labelDiv {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;

            &.mb8 {
                margin-bottom: 8px;
            }

            button {
                border: none;
                background-color: transparent;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
                color: #555555;
                font-weight: 600;
                font-size: 13px;
                line-height: 21px;
                cursor: pointer;
                margin-left: auto;
            }
        }

        label {
            color: $black;
            font-weight: 700;
            font-size: 14px;
            line-height: 22px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
            width: 100%;

            span {
                font-weight: 400;
            }

            sup {
                color: $red;
                line-height: 0px;
            }
        }

        input[type='text'],
        input[type='password'] {
            background-color: #FFFFFF;
            border: 1px solid #707070;
            border-radius: 4px;
            width: 100%;
            height: 44px;
            color: #515B6F;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            text-align: left;
            padding: 10px 8px;
            outline: none !important;

            &:disabled {
                background-color: #F7F7F7;
                font-style: italic;
            }

            &::placeholder {
                color: #9D9D9D;
                font-weight: 400;
                opacity: 1;
            }
        }

        .showPassword {
            border: none;
            background-color: transparent;
            cursor: pointer;
            width: 22px;
            height: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            right: 8px;
            top: 39px;
        }

        .error {
          color: #F91313;
          font-size: 12px;
          line-height: 18px;
          position: absolute;
          left: 0px;
          top: calc(100% + 4px);
          width: 100%;

          @include for-size(tablet-phone) {
              top: 100%;
          }
        }

        .btnGroup {
          display: block;
          text-align: center;
          gap: 0px;
        }

        .Cancel {
          background-color: transparent;
          border: none;
          display: inline-block;
          color: $black;
          font-weight: 500;
          font-size: 18px;
          line-height: 24px;
          text-align: center;
          margin-top: 20px;
          cursor: pointer;
        }

        .submitBtn {
            background-color: $secondary;
            border: none;
            border-radius: 8px;
            color: $black;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 18px;
            line-height: 24px;
            padding: 4px;
            width: 100%;
            height: 60px;
        }
    }
}