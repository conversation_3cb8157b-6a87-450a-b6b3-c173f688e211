import { DescriptionFormValues } from "@/types/jobpostingform";

export const jobFormPayload = (
  values: DescriptionFormValues,
  isDraft: boolean
) => {
  const cloneObject = JSON.parse(
    JSON.stringify(values)
  ) as DescriptionFormValues;

  const jobPost: Record<string, unknown> = {
    jobDescription: cloneObject.jobDescription,
    keyResponsibilities: cloneObject.keyResponsibilities,
    applicationMethod: cloneObject.applicationMethod,
    additionalNotes: cloneObject.additionalNotes,
    interviewSteps: cloneObject.interviewSteps,
    contactPersonName: cloneObject.contactPersonName,
    contactPersonEmail: cloneObject.contactPersonEmail,
    contactPersonPhone: cloneObject.contactPersonPhone,
    visibility: "Public",
    postingDurationDays: 30,
    eeoConfirmed: cloneObject.eeoConfirmed.includes(1) ? true : false,
  };

  if (cloneObject.applicationDocs) {
    jobPost.applicationDocs = cloneObject.applicationDocs;
  }

  if (cloneObject.hiringProcessTimeline) {
    jobPost.hiringProcessTimeline = cloneObject.hiringProcessTimeline;
  }

  if (cloneObject.joiningDate) {
    jobPost.joiningDate = cloneObject.joiningDate;
  }

  return {
    currentStep: 5,
    currentStepStatus: isDraft ? "DRAFT" : "COMPLETED",
    jobPost: {
      
    },
  };
};
