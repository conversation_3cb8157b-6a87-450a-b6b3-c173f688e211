.nemtDriverExperience {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #333;
  }

  h5 {
    font-size: 1rem;
    margin-bottom: 1.5rem;
    color: #555;
    font-weight: normal;
  }

  h6.required {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
    color: #666;
    font-weight: normal;

    sup {
      color: #d32f2f;
    }
  }

  .card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1.5rem;

    h3 {
      font-size: 1.2rem;
      margin-bottom: 1rem;
      color: #333;
    }

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: #555;
      font-size: 0.95rem;

      span {
        font-size: 0.85rem;
        color: #777;
        margin-left: 0.5rem;
      }

      sup {
        color: #d32f2f;
      }
    }
  }

  .formRow {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1rem;
  }

  .col02 {
    flex: 1;
    min-width: 250px;
  }

  .checkBox {
    list-style: none;
    padding: 0;
    margin: 0;

    &.column {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 0.5rem;
    }

    li {
      position: relative;
      padding-left: 30px;
      margin-bottom: 0.5rem;
      display: flex;
      align-items: flex-start;
      cursor: pointer;

      input[type="checkbox"] {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
      }

      .checkmark {
        position: absolute;
        top: 2px;
        left: 0;
        height: 20px;
        width: 20px;
        background-color: #fff;
        border: 1px solid #ccc;
        border-radius: 4px;
      }

      &:hover input ~ .checkmark {
        background-color: #f5f5f5;
      }

      input:checked ~ .checkmark {
        background-color: #2196f3;
        border-color: #2196f3;
      }

      .checkmark:after {
        content: "";
        position: absolute;
        display: none;
      }

      input:checked ~ .checkmark:after {
        display: block;
      }

      .checkmark:after {
        left: 7px;
        top: 3px;
        width: 5px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
      }

      p {
        margin: 0;
        font-size: 0.95rem;
        color: #333;
      }
    }
  }

  .error {
    color: #d32f2f;
    font-size: 0.85rem;
    margin-top: 0.25rem;
    display: block;
  }

  .btnGroup {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
    flex-wrap: wrap;
    gap: 1rem;

    button {
      padding: 0.75rem 1.5rem;
      border-radius: 4px;
      font-size: 1rem;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      gap: 0.5rem;

      &:disabled {
        opacity: 0.7;
        cursor: not-allowed;
      }

      &.back {
        background-color: #f5f5f5;
        border: 1px solid #ddd;
        color: #333;

        &:hover:not(:disabled) {
          background-color: #e5e5e5;
        }
      }

      &.continue {
        background-color: #2196f3;
        border: 1px solid #2196f3;
        color: white;

        &:hover:not(:disabled) {
          background-color: #0d8bf2;
        }
      }

      &.exit {
        background-color: #fff;
        border: 1px solid #2196f3;
        color: #2196f3;

        &:hover:not(:disabled) {
          background-color: #f0f8ff;
        }
      }
    }
  }
}