import { FormikProps } from "formik";
import React from "react";
import "react-datepicker/dist/react-datepicker.css";
import styles from "./DatePickerComponent.module.scss";
import DateInput from "../../DateInput/DateInput";

interface DatePickerProps<T extends Record<string, unknown>> {
  className?: string;
  styleClass?: string;
  label?: string;
  desc?: string;
  fieldName: string;
  hide?: boolean;
  formik: FormikProps<T>;
}

const DatePickerComponent = <T extends Record<string, unknown>>({
  className = "",
  label = "",
  desc = "",
  fieldName = "",
  hide = false,
  formik,
}: DatePickerProps<T>) => {
  return (
    <div className={`${styles.datePicker} ${styles[className]}`}>
      {label && (
        <label htmlFor={fieldName}>
          {label} {desc && <span className={styles.desc}>{desc}</span>}{" "}
          {!hide && <span className={styles.important}>*</span>}
        </label>
      )}
      <DateInput
        placeholder="MM/DD/YYYY"
        dateFormat="MM/dd/yyyy"
        selected={
          formik.values[fieldName]
            ? new Date(formik.values[fieldName] as string | number)
            : null
        }
        onChange={(date) => formik?.setFieldValue(fieldName, date)}
      />
      {formik?.touched[fieldName] &&
        typeof formik?.errors[fieldName] === "string" && (
          <div className="error_msg">{formik?.errors[fieldName]}</div>
        )}
    </div>
  );
};

export default DatePickerComponent;
