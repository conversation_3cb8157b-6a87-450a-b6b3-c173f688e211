export type StepThreeValues = {
  companyLogo:"",
  companyTypes: string[];
  yearsInBusiness: string;
  estimatedDrivers: string;
  otherCompanyType: string;
  primaryAreas: string[];
};

export type FileItem = {
  filename?: string;
  filepath?: string;
  multimediaId?: number;
};

export type CompanyValues = {
  name: string;
  email: string;
  phoneNumber: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  legalName: string;
  einNumber: string;
  website: string | null;
  usDotNumber: string | null;
  mcNumber: string | null;
  noDotOrMcNumber: boolean;
};

export type ContactValues = {
  contactPersonName: string;
  jobTitle: string;
};

export type uploadValues = {
  docFiles?: {
    businessRegistration?: FileItem[];
    einLetter?: FileItem[];
  };
};

export type FormInitialValues = {
  fileUploads: uploadValues;
  company: CompanyValues;
  contact: ContactValues;
  stepThree: StepThreeValues;
};

