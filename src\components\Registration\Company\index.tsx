"use client";
import Link from "next/link";
import css from "./companyregister.module.scss";
import { useFormik } from "formik";
import * as Yup from "yup";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { useState } from "react";
import { registration } from "@/services/userService";
import { saveToken } from "@/utils/loginRegister";
import { useRouter } from "next/navigation";

interface FormValues {
  companyName: string;
  email: string;
  phone: string;
  city: string;
  state: string;
  zip: string;
  createPassword: string;
  confirmPassword: string;
  agree: boolean;
}

const CompanyRegisterForm = () => {
  const router = useRouter();
  const [showCreatePassword, setShowCreatePassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const formik = useFormik({
    initialValues: {
      companyName: "",
      email: "",
      phone: "",
      city: "",
      state: "",
      zip: "",
      createPassword: "",
      confirmPassword: "",
      agree: false,
    },
    validationSchema: Yup.object({
      companyName: Yup.string().required("Company Name is required"),
      email: Yup.string()
        .email("Invalid email address")
        .required("Email is required"),
      phone: Yup.string()
        .matches(/^[0-9]{10,15}$/, "Enter a valid phone number")
        .required("Phone is required"),
      city: Yup.string().required("City is required"),
      state: Yup.string().required("State is required"),
      zip: Yup.string()
        .matches(/^[0-9]{5,6}$/, "Enter a valid zip code")
        .required("Zip Code is required"),
      createPassword: Yup.string()
        .min(6, "Password must be at least 6 characters")
        .required("Create Password is required"),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref("createPassword")], "Passwords must match")
        .required("Confirm Password is required"),

      agree: Yup.boolean().oneOf([true], "You must agree to continue"),
    }),
    onSubmit: (values) => {
      console.log("Form Submitted", values);
      handleSubmit(values);
    },
  });

  const handleSubmit = async (values: FormValues) => {
    const payload = {
      email: values.email,
      password: values.createPassword,
      name: values.companyName,
      contactNumber: values.phone,
      city: values.city,
      isCompany: true,
      state: values.state,
      zipCode: values.zip,
    };
    const res = await registration(payload);
    if (res.status) {
      saveToken(
        res?.data?.user?.accessToken,
        res?.data?.user?.refreshToken,
        values.email,
        res?.data?.user?.isCompany
      );
      router.push("/profile/company");
    } else {
      alert(res.message);
    }
  };

  return (
    <div className={css.loginContainer}>
      <div className={css.loginHeader}>
        <p className={css.mainHeading}>
          Connect with qualified drivers today. Register your company to start
          hiring.
        </p>
        <p className={css.subHeading}>
          Already have an account?
          <Link href="/login/company" className={css.registerLink}>
            Login
          </Link>
        </p>
      </div>

      <form onSubmit={formik.handleSubmit}>
        <div className={css.employerInputContainer}>
          <div className={css.inputGroup}>
            <label htmlFor="companyName">Company Name</label>
            <input
              type="text"
              id="companyName"
              name="companyName"
              placeholder="Enter Here"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.companyName}
            />
            {formik.touched.companyName && formik.errors.companyName && (
              <div className={css.error}>{formik.errors.companyName}</div>
            )}
          </div>

          <div className={css.inputGroup}>
            <label htmlFor="email">Company Email</label>
            <input
              type="email"
              id="email"
              name="email"
              placeholder="Enter Here"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.email}
            />
            {formik.touched.email && formik.errors.email && (
              <div className={css.error}>{formik.errors.email}</div>
            )}
          </div>
        </div>

        <div className={css.employerInputContainer}>
          <div className={css.inputGroup}>
            <label htmlFor="phone">Phone</label>
            <input
              type="text"
              id="phone"
              name="phone"
              placeholder="Enter Here"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.phone}
            />
            {formik.touched.phone && formik.errors.phone && (
              <div className={css.error}>{formik.errors.phone}</div>
            )}
          </div>

          <div className={css.inputGroup}>
            <label htmlFor="city">City</label>
            <input
              type="text"
              id="city"
              name="city"
              placeholder="Enter Here"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.city}
            />
            {formik.touched.city && formik.errors.city && (
              <div className={css.error}>{formik.errors.city}</div>
            )}
          </div>
        </div>

        <div className={css.employerInputContainer}>
          <div className={css.inputGroup}>
            <label htmlFor="state">State</label>
            <input
              type="text"
              id="state"
              name="state"
              placeholder="Enter Here"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.state}
            />
            {formik.touched.state && formik.errors.state && (
              <div className={css.error}>{formik.errors.state}</div>
            )}
          </div>

          <div className={css.inputGroup}>
            <label htmlFor="zip">Zip Code</label>
            <input
              type="text"
              id="zip"
              name="zip"
              placeholder="Enter Here"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              value={formik.values.zip}
            />
            {formik.touched.zip && formik.errors.zip && (
              <div className={css.error}>{formik.errors.zip}</div>
            )}
          </div>
        </div>

        <div className={css.employerInputContainer}>
          {/* Create Password */}
          <div className={css.inputGroup}>
            <label htmlFor="createPassword">Create Password</label>
            <div className={css.passwordWrapper}>
              <input
                type={showCreatePassword ? "text" : "password"}
                id="createPassword"
                name="createPassword"
                placeholder="Enter Here"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.createPassword}
              />
              <span
                className={css.eyeIcon}
                onClick={() => setShowCreatePassword((prev) => !prev)}
              >
                {showCreatePassword ? <FaEyeSlash /> : <FaEye />}
              </span>
            </div>
            {formik.touched.createPassword && formik.errors.createPassword && (
              <div className={css.error}>{formik.errors.createPassword}</div>
            )}
          </div>

          {/* Confirm Password */}
          <div className={css.inputGroup}>
            <label htmlFor="confirmPassword">Confirm Password</label>
            <div className={css.passwordWrapper}>
              <input
                type={showConfirmPassword ? "text" : "password"}
                id="confirmPassword"
                name="confirmPassword"
                placeholder="Enter Here"
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                value={formik.values.confirmPassword}
              />
              <span
                className={css.eyeIcon}
                onClick={() => setShowConfirmPassword((prev) => !prev)}
              >
                {showConfirmPassword ? <FaEyeSlash /> : <FaEye />}
              </span>
            </div>
            {formik.touched.confirmPassword &&
              formik.errors.confirmPassword && (
                <div className={css.error}>{formik.errors.confirmPassword}</div>
              )}
          </div>
        </div>

        <div className={css.checkboxContainer}>
          <label htmlFor="agree" className={css.checkboxLabel}>
            <input
              type="checkbox"
              id="agree"
              name="agree"
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              checked={formik.values.agree}
              className={css.checkbox}
            />
            I agree to receive updates, marketing emails and offers from
            Driverjobz
          </label>
        </div>
        {formik.touched.agree && formik.errors.agree && (
          <div className={css.error}>{formik.errors.agree}</div>
        )}

        <div className={css.buttonWrapper}>
          <button type="submit" className={css.submitBtn}>
            Continue
          </button>
          <Link href="/" className={css.forgotLink}>
            Forgot Password?
          </Link>
        </div>
      </form>
    </div>
  );
};

export default CompanyRegisterForm;
