@import "../../styles/global.scss";

// Section 1
.secHome01 {
  background-color: $light;
  padding: 40px 0px;
  position: relative;
  
 

  .container {
    @include container;
  }

  h1 {
    color: $black;
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
    text-align: center;
    max-width: 470px;
    margin: 0 auto;
  }

  h2 {
    color: $black;
    font-weight: 500;
    font-size: 22px;
    line-height: 28px;
    margin-bottom: 24px;

    button {
      background-color: transparent;
      border: none;
      border-radius: 0px;
      font-size: 16px;
      color: #26a4ff;
      padding-left: 8px;
    }
  }

  span {
    color: #707070;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    text-align: center;
    display: block;
    margin-top: 16px;
  }

  .flexBox {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-top: 40px;
    width: 1139px;
    height: 126px;

    .column {
      width: 500px;
    }
  }

  .search {
    border-radius: 50px;
    border: 1px solid #7c8493;
    height: 76px;
    padding: 15px 32px 15px 24px;
    display: flex;
    justify-content: space-between;
    width: 498px;

    input[type="search"] {
      background-color: $light;
      border: none;
      width: calc(100% - 46px);
      height: 44px;
      color: $black;
      font-weight: 400;
      font-size: 18px;
      line-height: 44px;

      &::-webkit-input-placeholder {
        color: #7c8493;
        font-weight: 400;
        opacity: 1;
      }

      &::-moz-placeholder {
        color: #7c8493;
        font-weight: 400;
        opacity: 1;
      }

      &:-ms-input-placeholder {
        color: #7c8493;
        font-weight: 400;
        opacity: 1;
      }

      &:-moz-placeholder {
        color: #7c8493;
        font-weight: 400;
        opacity: 1;
      }

      &:focus,
      &:active,
      &:focus-visible {
        border: none;
        box-shadow: none;
        outline: none;
      }
    }

    input[type="submit"] {
      background-color: $secondary;
      border: none;
      width: 138px;
      height: 44px;
      border-radius: 40px;
      color: #695a25;
      font-weight: 700;
      font-size: 15px;
      line-height: 22px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
    }
  }
}
.searchfield {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.searchIcon {
  width: 24px;
  height: 24px;
}

.sideIcon {
 
  height: 80px;
  width: 80px;
 
}
.sideIconContainer{
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  margin-top: -37px;
  position: relative;
     
}
// Section 2
.secHome02 {
  .container {
    @include container;
  }

  ul {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    li {
      padding: 20px;
      width: calc((100% - 48px) / 4);
      display: inline-flex;
      align-items: center;
      gap: 20px;

      figure {
        border-radius: 4px;
        background-color: #fffbee;
        width: 60px;
        height: 64px;
        display: inline-flex;
        align-items: center;
        justify-content: center;

        img {
          max-width: 36px;
          max-height: 40px;
        }
      }

      .jobsDetails {
        width: calc(100% - 80px);
      }

      h6 {
        color: #18191c;
        font-weight: 500;
        font-size: 16px;
        line-height: 22px;
      }

      span {
        color: #767f8c;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
      }
    }
  }
}

// Section 3
.secHome03 {
  padding-top: 64px;

  .container {
    @include container;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .aside {
    width: 42%;
  }

  h3 {
    color: $black;
    font-weight: 500;
    font-size: 19px;
    line-height: 26px;
  }

  ul {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    gap: 16px;
    margin-top: 16px;

    li {
      color: #707070;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      display: inline-flex;
      gap: 16px;
      width: 100%;

      img {
        width: 24px;
      }

      span {
        width: calc(100% - 40px);
      }
    }
  }
}

// Section 4
.secHome04 {
  padding-top: 64px;
  padding-bottom: 50px;
  overflow: hidden;

  .container {
    @include container;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .aside01 {
    width: 52%;
  }

  .aside02 {
    width: 40%;
  }

  h3 {
    color: #25324b;
    font-weight: 500;
    font-size: 22px;
    line-height: 28px;
    text-align: center;
  }

  .flexBox {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    margin-top: 40px;
  }

  h4 {
    color: $black;
    font-weight: 500;
    font-size: 19px;
    line-height: 25px;
  }

  ul {
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
    gap: 16px;
    margin-top: 16px;

    li {
      color: #707070;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      display: inline-flex;
      gap: 16px;
      width: 100%;

      img {
        width: 24px;
      }

      span {
        width: calc(100% - 40px);
      }
    }
  }

  .register {
    border: none;
    border-radius: 4px;
    background-color: #333333;
    width: 138px;
    height: 44px;
    color: $white;
    font-weight: 700;
    font-size: 15px;
    line-height: 22px;
    margin-top: 26px;
    margin-left: 10px;
  }
}

// Section 5
.secHome05 {
  background-color: $light;
  padding: 32px 0px;
  position: relative;

  .container {
    @include container;
  }

  h2 {
    color: #18191c;
    font-weight: 500;
    font-size: 22px;
    line-height: 28px;
    text-align: center;
  }

  h3 {
    color: $black;
    font-weight: 500;
    font-size: 19px;
    line-height: 26px;
    text-align: center;
  }

  .flexBox {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    max-width: 960px;
    margin: 52px auto 0px;
  }

  ul {
    margin-top: 24px;
    display: flex;
    flex-direction: column;
    gap: 24px;

    li {
      display: inline-flex;
      gap: 24px;
    }

    figure {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      line-height: 0px;
      overflow: hidden;

      img {
        width: 32px;
        height: 32px;
      }
    }

    div {
      width: calc(100% - 56px);
    }

    h6 {
      color: $black;
      font-weight: 500;
      font-size: 16px;
      line-height: 22px;
    }

    span {
      color: #707070;
      display: block;
      font-weight: 400;
      font-size: 16px;
      line-height: 22px;
      margin-top: 8px;
    }
  }
}
