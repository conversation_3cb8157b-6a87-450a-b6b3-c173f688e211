@import '../../../styles/global.scss';

.employer {
    background-color: $white;
    padding-bottom: 120px;

    .container {
        @include container;

        .wrapper {
            max-width: 1030px;
        }
    }

    // .basicForm {
    //     margin-top: 45px;
    // }

    // .formHeading {
    //     border-bottom: 1px solid #D6DDEB;
    //     padding: 24px 0px;
    //     margin-bottom: 24px;

    //     &:last-child {
    //         margin-bottom: 0px;
    //     }

    //     h3 {
    //         color: $dark-900;
    //         font-weight: 500;
    //         font-size: 23px;
    //         line-height: 140%;
    //     }
    // }

    .buttons {
        display: flex;
        justify-content: flex-end;

        button {
            border: none;
            border-radius: 8px;
            background-color: $secondary;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            width: 240px;
            font-size: 18px;
            line-height: 26px;
            color: $black;
            cursor: pointer;
        }
    }
}