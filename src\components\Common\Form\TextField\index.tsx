import React, { useState } from "react";
import styles from "./TextField.module.scss";
import { FormikProps } from "formik";
import Image from "next/image";
import Tooltip from "../Tooltip";

interface TextProps<T extends Record<string, unknown>> {
  className?: string;
  styleClass?: string;
  label?: string;
  desc?: string;
  fieldName: string;
  placeholder?: string;
  hide?: boolean;
  formik: FormikProps<T>;
  maxLength?: number;
  disabled?: boolean;
  editIcon?: boolean;
  defaultValue?: string;
  errorClass?: string;
  tooltipMsg?: string;
  decimalAllowed?: boolean;
  handleChange?: (
    event: React.ChangeEvent<HTMLInputElement>,
    fieldName: string,
    formik: FormikProps<T>,
    decimalAllowed: boolean
  ) => void;
}

const TextField = <T extends Record<string, unknown>>({
  className = "",
  styleClass = "",
  label = "",
  desc = "",
  fieldName,
  placeholder = "",
  handleChange,
  hide = false,
  formik,
  maxLength,
  disabled,
  editIcon,
  defaultValue,
  errorClass = "",
  tooltipMsg,
  decimalAllowed = false,
}: TextProps<T>) => {
  const [disabledState, setDisabledState] = useState(true);

  const changeHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (handleChange) {
      handleChange(e, fieldName, formik, decimalAllowed);
    } else {
      formik?.handleChange(e);
    }
  };

  return (
    <div
      className={`${styles.columnField} ${styles[className]} ${styleClass} 
    ${editIcon ? styles.alignColumn : ""} ${
        disabled && disabledState ? styles.disabled : ""
      }`}
    >
      {label && (
        <label htmlFor={fieldName}>
          {label} {desc && <span className={styles.desc}>{desc}</span>}{" "}
          {!hide && <span className={styles.important}>*</span>}
          {tooltipMsg && <Tooltip tooltipMsg={tooltipMsg} />}
          
          {editIcon && (
            <span
              className={styles.iconWrapper}
              onClick={() => setDisabledState(false)}
            >
              <Image
                src="/images/icons/icon-edit.svg"
                alt="edit"
                width={16}
                height={16}
              />
              Edit
            </span>
          )}
        </label>
      )}
      <input
        type="text"
        id={fieldName}
        name={fieldName}
        placeholder={placeholder}
        onChange={changeHandler}
        value={
          (formik?.values[fieldName] as string | number | undefined) ||
          defaultValue ||
          ""
        }
        maxLength={maxLength}
        disabled={disabled ? disabledState : false}
      />
      {formik?.touched[fieldName] &&
        typeof formik?.errors[fieldName] === "string" && (
          <div className={`error_msg ${styles[errorClass]}`}>
            {formik?.errors[fieldName]}
          </div>
        )}
    </div>
  );
};

export default TextField;
