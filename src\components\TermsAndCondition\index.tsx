// "use client";

// import { getDictionary } from "@/app/[lang]/dictionaries";
// import css from "./TermsAndConditions.module.scss";
// import Link from "next/link";

// type props={
//   params:{
//     lang:'en'|'es'
//   }
// }
// const TermsAndConditions = async({params}:props) => {

//   const dict = await getDictionary(params.lang)
//   return (
//     <div className={css.termsContainer}>
//       <h1 className={css.heading}>DriverJobz Registration Terms</h1>

//       <p>
//         By clicking <strong>"Create Account"</strong> /{" "}
//         <strong>"Register"</strong>, you confirm that you meet the eligibility
//         requirements (e.g., minimum age) and agree to the following key terms.
//         You also acknowledge you have read and agree to the full{" "}
//         <Link href="/terms-and-conditions" className={css.link}>
//           Terms & Conditions
//         </Link>{" "}
//         and{" "}
//         <Link href="/privacy-policy" className={css.link}>
//           Privacy Policy
//         </Link>
//         .
//       </p>

//       <h2 className={css.subHeading}>1. Service Overview:</h2>
//       <ul>
//         <li>
//           ★ DriverJobz is a platform connecting Drivers/Transportation
//           Professionals with Employers.
//         </li>
//         <li>
//           ★ We <strong>do not</strong> act as an employment agency, guarantee
//           jobs or candidates, or perform comprehensive background checks beyond
//           indicated verifications. We are not party to employment agreements.
//         </li>
//       </ul>

//       <h2 className={css.subHeading}>2. Your Account & Information:</h2>
//       <ul>
//         <li>
//           ★ You agree to provide{" "}
//           <strong>accurate, complete, and up-to-date information</strong> during
//           registration and on your profile/job postings. Misrepresentation may
//           lead to account suspension or termination.
//         </li>
//         <li>
//           ★ You are responsible for maintaining the{" "}
//           <strong>security of your account login</strong> details.
//         </li>
//         <li>
//           ★ You grant DriverJobz a license to use the information you provide{" "}
//           <strong>solely to operate the platform</strong> (e.g., display your
//           profile/job post, facilitate connections, provide job matches). You
//           retain ownership of your content.
//         </li>
//       </ul>

//       <h2 className={css.subHeading}>3. Platform Use:</h2>
//       <ul>
//         <li>
//           ★ You agree to use DriverJobz only for its intended purposes (job
//           searching, recruiting) and in compliance with all applicable laws.
//         </li>
//         <li>
//           ★ You will not upload prohibited content (illegal, defamatory,
//           infringing) or misuse the platform (spam, scraping, etc.).
//         </li>
//       </ul>

//       <h2 className={css.subHeading}>4. Disclaimers & Liability:</h2>
//       <ul>
//         <li>
//           ★ The platform is provided "as is." DriverJobz is{" "}
//           <strong>not responsible</strong> for the accuracy of user-provided
//           information, hiring decisions, or user interactions. Employers and
//           Drivers are responsible for their own due diligence.
//         </li>
//         <li>
//           ★ Our liability is strictly limited as detailed in the full Terms &
//           Conditions.
//         </li>
//       </ul>

//       <h2 className={css.subHeading}>5. Data & Privacy:</h2>
//       <ul>
//         <li>
//           ★ Your use of DriverJobz is subject to our{" "}
//           <Link href="/privacy-policy" className={css.link}>
//             Privacy Policy
//           </Link>
//           , which explains how we collect, use, and share your data, including
//           sharing based on your actions (applications, connections) and explicit
//           consent (Hiring Packet requests).
//         </li>
//       </ul>

//       <h2 className={css.subHeading}>6. Modifications:</h2>
//       <ul>
//         <li>
//           ★ These terms may be updated. We will notify you of significant
//           changes. Continued use constitutes acceptance.
//         </li>
//       </ul>

//       <div className={css.agreementBox}>
//         <label>
//           <input type="checkbox" required />
//           <span>
//             I have read and agree to the DriverJobz Registration Terms, the full{" "}
//             <Link href="/terms-and-conditions" className={css.link}>
//               Terms & Conditions
//             </Link>{" "}
//             and{" "}
//             <Link href="/privacy-policy" className={css.link}>
//               Privacy Policy
//             </Link>
//             .
//           </span>
//         </label>
//       </div>
//       <div className={css.buttonWrapper}>
//         {" "}
//         <button className={css.button}>Create Account / Register</button>
//       </div>
//     </div>
//   );
// };

// export default TermsAndConditions;
// "use client";





import css from "./TermsAndConditions.module.scss";
import Link from "next/link";

import { getDictionary } from "@/app/[lang]/dictionaries";

interface Props {
  lang: "en" | "es";
}

const TermsAndConditions = async ({ lang }: Props) => {
  const dict = await getDictionary(lang);

  return (
    <div className={css.termsContainer}>
      <h1 className={css.heading}>{dict.terms.title}</h1>

      <p>{dict.terms.intro}</p>

      <h2 className={css.subHeading}>{dict.terms.section1.title}</h2>
      <ul>
        <li>{dict.terms.section1.point1}</li>
        <li>{dict.terms.section1.point2}</li>
      </ul>

      <h2 className={css.subHeading}>{dict.terms.section2.title}</h2>
      <ul>
        <li>{dict.terms.section2.point1}</li>
        <li>{dict.terms.section2.point2}</li>
        <li>{dict.terms.section2.point3}</li>
      </ul>

      <h2 className={css.subHeading}>{dict.terms.section3.title}</h2>
      <ul>
        <li>{dict.terms.section3.point1}</li>
        <li>{dict.terms.section3.point2}</li>
      </ul>

      <h2 className={css.subHeading}>{dict.terms.section4.title}</h2>
      <ul>
        <li>{dict.terms.section4.point1}</li>
        <li>{dict.terms.section4.point2}</li>
      </ul>

      {/* <h2 className={css.subHeading}>{dict.terms.section5.title}</h2>
      <ul>
        <li>{dict.terms.section5.point1}</li>
      </ul> */}
      <h2 className={css.subHeading}>{dict.terms.section5.title}</h2>
      <ul>
        <li>
          {dict.terms.section5["point1.part1"]}{" "}
          <Link href="/privacy-policy" className={css.link}>
            {dict.terms.section5["point1.link"]}
          </Link>
          {dict.terms.section5["point1.part2"]}
        </li>
      </ul>

      <h2 className={css.subHeading}>{dict.terms.section6.title}</h2>
      <ul>
        <li>{dict.terms.section6.point1}</li>
      </ul>


    </div>
  );
};

export default TermsAndConditions;
 