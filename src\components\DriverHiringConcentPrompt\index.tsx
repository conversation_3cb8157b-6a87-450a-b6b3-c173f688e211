'use client';
import React from 'react';
import styles from './driverConcentPrompt.module.scss';

interface DriverHiringPacketConsentPromptProps {
  companyName: string;
  companyLogoUrl?: string;
  jobTitle?: string;
  onApprove: () => void;
  onDeny: () => void;
}

const DriverHiringPacketConsentPrompt: React.FC<DriverHiringPacketConsentPromptProps> = ({
  companyName,
  companyLogoUrl,
  jobTitle,
  onApprove,
  onDeny,
}) => {
  return (
    <div className={styles.promptWrapper}>
      <div className={styles.promptBox}>
        <h2>Grant Access to Detailed Hiring Information?</h2>

        {companyLogoUrl && (
          <img
            src={companyLogoUrl}
            alt={`${companyName} Logo`}
            className={styles.logo}
          />
        )}

        <p className={styles.highlight}>
          <strong>{companyName}</strong> is requesting access to your detailed profile
          information to proceed with pre-hiring checks
          {jobTitle && <> for the <strong>{jobTitle}</strong> position</>}.
        </p>

        <h3>Why is this needed?</h3>
        <p>
          Employers often need this information to perform background checks and verify your
          credentials before offering a job.
        </p>

        <h3>What will be shared if you approve?</h3>
        <ul className={styles.bulletPoints}>
          <li><strong>Full Legal Name</strong></li>
          <li><strong>Date of Birth</strong></li>
          <li><strong>Full Address History</strong></li>
          <li><strong>Driver&apos;s License Details</strong> (Number, State, Expiry, Class, etc.)</li>
          <li>Secure access to uploaded documents (e.g., License, Med Card, Certificates)</li>
        </ul>

        <h4>Important Notes:</h4>
        <ul className={styles.bulletPoints}>
          <li><strong>SSN Not Shared:</strong> Your Social Security Number is not included.</li>
          <li><strong>Secure In-Platform Access:</strong> Data is viewable securely within DriverJobz, not downloadable.</li>
          <li><strong>Revocable:</strong> You may manage or revoke access later from your account settings.</li>
        </ul>

        <p className={styles.agreementText}>
          By clicking <strong>&quot;Approve Access&quot;</strong>, you agree to share the above data with
          {` ${companyName} `} for hiring purposes, in accordance with our{' '}
          <a href="/terms" target="_blank">Terms & Conditions</a> and{' '}
          <a href="/privacy" target="_blank">Privacy Policy</a>.
        </p>

        <div className={styles.buttonGroup}>
          <button className={styles.acceptBtn} onClick={onApprove}>
            Approve Access
          </button>
          <button className={styles.cancelBtn} onClick={onDeny}>
            Deny Request
          </button>
        </div>
      </div>
    </div>
  );
};

export default DriverHiringPacketConsentPrompt;
