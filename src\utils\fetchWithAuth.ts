import { deleteC<PERSON>ie, get<PERSON><PERSON>ie, set<PERSON><PERSON>ie } from "cookies-next";

// Good version of fetchWithAuth
export async function fetchWithAuth(input: RequestInfo, init?: RequestInit): Promise<Response> {
  let accessToken = getCookie("authToken") as string | undefined;
  const refreshToken = getCookie("refreshToken") as string | undefined;
  const ROTATE_TOKEN_API = process.env.NEXT_PUBLIC_API_V1 + "auth/rotate-token";

  const options: RequestInit = {
    ...init,
    headers: {
      "Content-Type": "application/json",
      Accept: "application/json",
      ...(init?.headers || {}),
      ...(accessToken && {
        Authorization: `${accessToken}`,
      }),
    },
  };

  let response = await fetch(input, options);

  if (response.status === 401 && refreshToken) {
    const rotateResponse = await fetch(ROTATE_TOKEN_API, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
        "refresh-token": refreshToken,
      },
    });

    const contentType = rotateResponse.headers.get("Content-Type") || "";
    if (rotateResponse.ok && contentType.includes("application/json")) {
      const data = await rotateResponse.json();
      if (data?.data?.accessToken && data?.data?.refreshToken) {
        accessToken = data.data.accessToken;
        const newRefreshToken = data.data.refreshToken;

        setCookie("authToken", accessToken, {
          path: "/",
          expires: new Date(new Date().setMonth(new Date().getMonth() + 1)),
        });
        setCookie("refreshToken", newRefreshToken, {
          path: "/",
          expires: new Date(new Date().setMonth(new Date().getMonth() + 1)),
        });

        // Retry original request with new token
        options.headers = {
          ...options.headers,
          Authorization: `${accessToken}`,
        };
        response = await fetch(input, options);
      } else {
        deleteCookie("authToken");
        deleteCookie("refreshToken");
      }
    } else {
      deleteCookie("authToken");
      deleteCookie("refreshToken");
    }
  }

  return response;
}
