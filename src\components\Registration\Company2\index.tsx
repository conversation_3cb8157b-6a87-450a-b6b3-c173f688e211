"use client";

import React, { useEffect, useState } from "react";
import Link from "next/link";
import css from "./companyRegistration.module.scss";
import { useFormik } from "formik";
import * as Yup from "yup";
import { registration } from "@/services/userService";
import { saveToken } from "@/utils/loginRegister";
import { useRouter } from "next/navigation";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { getStates, State } from "@/services/locationService";
import { toast, ToastContainer } from "react-toastify";
import Dropdown from "@/components/Common/Dropdown";
interface FormValues {
  companyName: string;
  email: string;
  phone: string;
  city: string;
  state: string;
  createPassword: string;
  confirmPassword: string;
  agreeToTerms: boolean;
  agreeToMarketing: boolean;
}

interface Props {
  lang: "en" | "es";
}

const CompanyRegistration2: React.FC<Props> = ({ lang }) => {
  const router = useRouter();

  const [states, setStates] = useState<State[]>([]);

  const [showCreatePassword, setShowCreatePassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  useEffect(() => {
    const fetchStates = async () => {
      try {
        const res = await getStates();
        setStates(res);
      } catch (error) {
        console.error("Failed to fetch states:", error);
      }
    };
    fetchStates();
  }, []);

  const formik = useFormik({
    initialValues: {
      companyName: "",
      email: "",
      phone: "",
      city: "",
      state: "",
      createPassword: "",
      confirmPassword: "",
      agreeToTerms: false,
      agreeToMarketing: false,
    },
    validationSchema: Yup.object({
      companyName: Yup.string().required("Company Name is required"),
      email: Yup.string().email("Invalid email").required("Email is required"),
      phone: Yup.string()
        .matches(/^[0-9]{10,15}$/, "Invalid phone number")
        .required("Phone is required"),
      city: Yup.string().required("City is required"),
      state: Yup.string().required("State is required"),
      createPassword: Yup.string()
        .min(8, "Password must be at least 8 characters")
        .matches(
          /^(?=.*[0-9])(?=.*[!@#$%^&*])/,
          "Password must contain at least one number and one special character (!@#$%^&*)"
        )
        .required("New Password is required"),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref("createPassword")], "Passwords must match")
        .required("Confirm Password is required"),
      agreeToTerms: Yup.boolean().oneOf(
        [true],
        "You must agree to the Terms and Privacy Policy"
      ),
    }),

    onSubmit: (values) => {
      console.log("Form Submitted", values);
      handleSubmit(values);
    },
  });
  const handleSubmit = async (values: FormValues) => {
    const selectedState = states.find(
      (s) => s.stateId.toString() === values.state
    );
    const stateSlug = selectedState?.slug ?? "";

    const payload = {
      email: values.email,
      password: values.createPassword,
      name: values.companyName,
      contactNumber: values.phone,
      city: values.city,

      state: stateSlug,
      isCompany: true,
      marketingConsent: values.agreeToMarketing, // this is optional on backend
    };

    const res = await registration(payload);
    if (res.status) {
      toast.success("Registration successful");
      saveToken(
        res?.data?.user?.accessToken,
        res?.data?.user?.refreshToken,
        values.email,
        res?.data?.user?.isCompany
      );
      router.push("/profile/company");
    } else {
       toast.error(`${res.message|| "Registration failed "} `);
    }
  };

  return (
    <section className={css.registrationSection}>
      <div className={css.loginWrapper}>
        <h1>Create Your Employer Account</h1>
        <p>
          Register your company in seconds to begin the process of finding qualified drivers.
        </p>
        <span className={css.requiredFields}>All fields are required<sup>*</sup></span>

        <form onSubmit={formik.handleSubmit} className={css.registrationForm}>
          {/* Company Name */}
          <div className={`${css.formGroup} ${css.col01}`}>
            <label htmlFor="companyName">Company Name</label>
            <input
              type="text"
              name="companyName"
              placeholder="Enter your official company name"
              value={formik.values.companyName}
              onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            />
            {formik.touched.companyName && formik.errors.companyName && (
              <div className={css.error}>{formik.errors.companyName}</div>
            )}
          </div>

          {/* Email & Phone */}
          <div className={`${css.formGroup} ${css.col02}`}>
            <label htmlFor="email">
              Company Email Address
              <span className={css.tooltipIcon}>
                <img src="/images/icons/icon-info.svg" alt="" />
                <span className={css.tooltip}>
                  A verification link will be sent to this email. Please use a valid address you can access.
                </span>
              </span>
            </label>
            <input
              type="email"
              name="email"
              placeholder="<EMAIL>"
              value={formik.values.email}
              onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            />
            {formik.touched.email && formik.errors.email && (
              <div className={css.error}>{formik.errors.email}</div>
            )}
          </div>
          <div className={`${css.formGroup} ${css.col02}`}>
            <label htmlFor="phone">
              Company Phone Number
              <span className={css.tooltipIcon}>
                <img src="/images/icons/icon-info.svg" alt="" />
                <span className={css.tooltip}>
                  Main company line or HR/Recruiting dept.
                  A verification code may be sent later.
                </span>
              </span>
            </label>
            <input
              type="tel"
              name="phone"
              maxLength={10}
              placeholder="(*************"
              value={formik.values.phone}
              onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            />
            {formik.touched.phone && formik.errors.phone && (
              <div className={css.error}>{formik.errors.phone}</div>
            )}
          </div>

          {/* City & State */}
          <div className={`${css.formGroup} ${css.col02}`}>
            <label htmlFor="city">Company Location (City)</label>
            <input
              type="text"
              name="city"
              placeholder="Enter city"
              //  placeholder="Enter city where your company is based or primarily operates"
              //         placeholder="Enter city where..."
              value={formik.values.city}
              onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            />
            {formik.touched.city && formik.errors.city && (
              <div className={css.error}>{formik.errors.city}</div>
            )}
          </div>

          <div className={`${css.formGroup} ${css.col02}`}>
            <label htmlFor="state">Company Location (State)</label>
            <Dropdown
              options={states.map(state => ({
                value: state.stateId.toString(),
                label: state.name[lang] || state.name["en"]
              }))}
              value={formik.values.state}
              placeholder="Select State"
              onChange={(value) => formik.setFieldValue("state", value)}
              error={formik.touched.state && formik.errors.state ? formik.errors.state : undefined}
              name="state"
            />
          </div>

          {/* Passwords */}
          <div className={`${css.formGroup} ${css.col02}`}>
            <label htmlFor="createPassword">
              Create Password
              <span className={css.tooltipIcon}>
                <img src="/images/icons/icon-info.svg" alt="" />
                <span className={css.tooltip}>
                  Min 8 characters, 1 number, 1 symbol
                  (e.g., !@#$).
                </span>
              </span>            
            </label>
            <input
              type={showCreatePassword ? "text" : "password"}
              name="createPassword"
              placeholder="Create Password"
              value={formik.values.createPassword}
              onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            />
            <div
              className={css.showPassword}
              onClick={() => setShowCreatePassword((prev) => !prev)}
            >
              {showCreatePassword ? <FaEye /> : <FaEyeSlash />}
            </div>
            {formik.touched.createPassword &&
              formik.errors.createPassword && (
                <div className={css.error}>{formik.errors.createPassword}</div>
              )}
          </div>
          <div className={`${css.formGroup} ${css.col02}`}>
            <label htmlFor="confirmPassword">Confirm Password</label>
            <input
              type={showConfirmPassword ? "text" : "password"}
              name="confirmPassword"
              placeholder="Confirm Password"
              value={formik.values.confirmPassword}
              onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            />
            <span
              className={css.showPassword}
              onClick={() => setShowConfirmPassword((prev) => !prev)}
            >
              {showConfirmPassword ? <FaEye /> : <FaEyeSlash />}
            </span>
            {formik.touched.confirmPassword &&
              formik.errors.confirmPassword && (
                <div className={css.error}>{formik.errors.confirmPassword}</div>
              )}
          </div>

          {/* Marketing Consent (Optional) */}
          <div className={`${css.formGroup} ${css.col01}`}>
          <div className={css.checkBox}>
            <input
              type="checkbox"
              name="agreeToMarketing"
              checked={formik.values.agreeToMarketing}
              onChange={formik.handleChange}
            />
            <span className={css.checkmark}></span>

            <p>I agree to receive updates, marketing emails and offers from
              Driverjobz</p>

          </div>

          {/* Terms Agreement (Required) */}
          <div className={css.checkBox}>
            <input
              type="checkbox"
              name="agreeToTerms"
              checked={formik.values.agreeToTerms}
              onChange={formik.handleChange}
            />
            <span className={css.checkmark}></span>

            <p>I agree to the DriverJobz
             {" "} <Link href="/terms-and-conditions">
                Employer Terms of Service 
              </Link>
              {" "}
              and {" "}<Link href="/privacy-policy">Privacy Policy</Link></p>

            {formik.touched.agreeToTerms && formik.errors.agreeToTerms && (
              <div className={css.error}>{formik.errors.agreeToTerms}</div>
            )}
          </div>
          </div>
          <div className={`${css.formGroup} ${css.col01}`}>
            <button type="submit" className={css.submitBtn}>Create Account</button>
            <p>
              Already have an account? <Link href="/login/company">Login</Link>
            </p>
          </div>
        </form>
        <ToastContainer
          position="top-right"
          autoClose={3000}
          hideProgressBar={false}
        />
      </div>
    </section>
  );
};

export default CompanyRegistration2;
