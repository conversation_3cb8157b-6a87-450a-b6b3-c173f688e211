import React from "react";
import styles from "./ButtonComponent.module.scss";
import Image from "next/image";
import Link from "next/link";

interface ButtonProps {
  className?: string;
  backText?: string;
  backBtn?: boolean;
  btnText?: string;
  btnText1?: string;
  backBtnHandler?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  draftBtnHandler?: (event: React.MouseEvent<HTMLButtonElement>) => void;
  saveBtnHandler?: (event: React.MouseEvent<HTMLButtonElement>) => void;
}

const ButtonComponent: React.FC<ButtonProps> = ({
  className = "",
  backText = "Back",
  backBtn = false,
  btnText = "Save Draft & Exit",
  btnText1 = "Save and Continue",
  backBtnHandler,
  draftBtnHandler,
  saveBtnHandler
}) => {

  return (
    <div className={`${styles.btnContainer} ${styles[className]}`}>
        {backText && 
          <button className={styles.backBtn} onClick={backBtnHandler}>
              <Image src="/images/icons/back-button.svg" alt="back" width={16} height={16} />
              {backText}
          </button>
        }
        <div className={styles.btnWrapper}>
          {btnText && <button className={styles.draftBtn} onClick={draftBtnHandler}>{btnText}</button>}
          {btnText1 && <button type="submit" className={styles.saveBtn} onClick={saveBtnHandler}>{btnText1}</button>}
        </div>
        {backBtn && 
          <p className={styles.backBtnLink}>
            Back to&nbsp;
            <Link href="/" className={styles.linkClass}>
              Home
            </Link> 
          </p>
        }
    </div>
  );
};

export default ButtonComponent;
