@import "../../../styles/global.scss";

.registrationSection {
    padding: 56px 0px 40px;
    width: 100%;

    @include for-size(big-tablet-down) {
        padding: 32px 16px;
    }

    .loginWrapper {
        background-color: $white;
        border: 1px solid #E5E5E5;
        border-radius: 16px;
        width: 100%;
        max-width: 804px;
        margin: 0 auto;
        padding: 40px 0px 52px;

        @include for-size(big-tablet-down) {
            border: none;
            border-radius: 0px;
            padding: 0px;
        }

        h1 {
            font-weight: 500;
            font-size: 24px;
            line-height: 32px;
            color: $dark;
            text-align: center;
            max-width: 600px;
            margin: 0px auto;
        }

        p {
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            text-align: center;
            margin: 12px auto 0px;
            max-width: 600px;

            a {
              color: $black;
              font-weight: 600;
            }

            @include for-size(big-tablet-down) {
                margin-top: 16px;
            }
        }
    }

    .registrationForm {
        display: flex;
        flex-wrap: wrap;
        column-gap: 24px;
        row-gap: 28px;
        max-width: 600px;
        margin: 32px auto 0px;

        @include for-size(big-tablet-down) {
            row-gap: 20px;
        }

        .formGroup {
            position: relative;
            width: 100%;

            p {
                color: #707070;

                a {
                    color: $black;
                    font-weight: 500;
                }
            }

            &:has( .error) {
              input {
                border-color: #F91313;
              }
            }
        }

        label {
            color: $black;
            font-weight: 700;
            font-size: 14px;
            line-height: 22px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
            width: 100%;

            span {
                font-weight: 400;
            }

            sup {
                color: $red;
                line-height: 0px;
            }
        }

        input[type='text'],
        input[type='email'],
        input[type='password'],
        input[type='tel'],
        input[type='number'] {
            background-color: #FFFFFF;
            border: 1px solid #707070;
            border-radius: 4px;
            width: 100%;
            height: 44px;
            color: #515B6F;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            text-align: left;
            padding: 10px 8px;
            outline: none !important;

            &:disabled {
                background-color: #F7F7F7;
                font-style: italic;
            }

            &::placeholder {
                color: #9D9D9D;
                font-weight: 400;
                opacity: 1;
            }
        }

        input[type='password'] {
            padding-right: 44px;
        }

        .showPassword {
            border: none;
            background-color: transparent;
            cursor: pointer;
            width: 22px;
            height: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            right: 8px;
            top: 39px;
        }

        .error {
          color: #F91313;
          font-size: 12px;
          line-height: 18px;
          position: absolute;
          left: 0px;
          top: calc(100% + 4px);
          width: 100%;

            @include for-size(tablet-phone) {
                top: 100%;
            }
        }

        .checkBox {
            position: relative;
            cursor: pointer;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin-bottom: 16px;

            &:last-child {
                margin-bottom: 0px;
            }

            input {
                position: absolute;
                opacity: 0;
                cursor: pointer;
                height: 24px;
                width: 24px;
                z-index: 3;

                &:checked {
                    ~ .checkmark {
                        background-color: #555555;
                        border-color: #555555;

                        &:after {
                            display: block;
                        }
                    }
                } 
            }

            .checkmark {
                height: 18px;
                width: 18px;
                background-color: #FFFFFF;
                border-radius: 4px;
                border: 2px solid #555555;
                position: absolute;
                left: 0px;
                top: 2px;

                &:after {
                    content: "";
                    position: absolute;
                    display: none;
                    left: 4px;
                    top: 0px;
                    width: 5px;
                    height: 10px;
                    border: solid $white;
                    border-width: 0 1px 1px 0;
                    -webkit-transform: rotate(45deg);
                    -ms-transform: rotate(45deg);
                    transform: rotate(45deg);
                }
            }

            p {
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
                padding-left: 34px;
                text-align: left;

                a {
                    color: #0075F2;
                }
            }
        }

        .submitBtn {
            background-color: $secondary;
            border: none;
            border-radius: 8px;
            color: $black;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 18px;
            line-height: 24px;
            padding: 4px;
            width: 100%;
            height: 60px;

            @include for-size(big-tablet-down) {
                border-radius: 4px;
                height: 50px;
            }
        }

          .forgotPassword {
            display: block;
            margin-top: 16px;
            text-align: right;

            .forgotLink {
              color: $black;
              font-weight: 400px;
              font-size: 16px;
              line-height: 22px;
            }
          }
    }
}

