@import '../../../styles/global.scss';

.driversRegistration {
    padding: 40px 0px;

    .container {
        @include container;
    }

    .required {
        color: $black;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;

        span {
            color: $red;
        }
    }
}

.accordion {
    border: 1px solid #E5E5E5;
    border-radius: 16px;
    margin-top: 16px;
    padding: 16px 24px;

    @include for-size(tablet-phone) {
        padding: 8px;
    }

    &.active {
        border-color: $black;

        .accordionTop {
            img {
                transform: rotateX(180deg);
            }
        }
    }

    .accordionTop {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 16px;
        min-height: 48px;

        h3 {
            color: $black;
            font-weight: 500;
            font-size: 20px;
            line-height: 28px;
            width: calc(100% - 48px);
        }
    }

    .accordionBody {
        padding-top: 24px;
        padding-bottom: 12px;
        border-top: 1px solid #E5E5E5;

        &.pt0 {
            padding-top: 0px;
        }
    }
}

.businessVerification {
    background-color: #FFFBEE;
    color: $black;
    font-weight: 700;
    font-size: 20px;
    line-height: 28px;
    padding: 18px 20px;
    margin-bottom: 24px;
}

.commonForm {
    .formRow {
        display: flex;
        flex-wrap: wrap;
        column-gap: 24px;
        row-gap: 28px;
        margin-bottom: 28px;
        position: relative;

        &:last-child {
            margin-bottom: 0px;
        }

        &.submitRow {
            margin-top: 40px;
            display: flex;
            justify-content: flex-end;
        }

        &.dBlaco {
            display: block;
        }

        .error {
            color: #F91313;
            font-size: 12px;
            line-height: 18px;
            font-weight: 400;
            position: absolute;
            left: 0px;
            top: calc(100% + 4px);
        }

        > div {
            .error {
                color: #F91313;
                font-size: 12px;
                line-height: 18px;
                font-weight: 400;
                position: absolute;
                left: 0px;
                top: calc(100% + 4px);
            }

            .FMCSA {
                color: $black;
                font-size: 12px;
                line-height: 18px;
                font-weight: 400;

                a {
                    color: #0075F2;
                    text-decoration: underline;
                }

                @include for-size(tablet-phone) {
                    position: relative;
                    top: auto;
                }
            }

            &:has( .error) {
                input {
                    border-color: #F91313;
                }
            }
        }

        .additionalBtn {
            background-color: #555555;
            border: none;
            border-radius: 4px;
            color: $white;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            text-align: center;
            padding: 13px 24px;
            margin-top: 23px;
            cursor: pointer;
        }

        .additionalCloseBtn {
            border: none;
            background-color: transparent;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #555555;
            font-weight: 600;
            font-size: 13px;
            line-height: 20px;
            margin-top: 35px;
            cursor: pointer;
        }

        &.mt36 {
            margin-top: 36px;
        }

        .flexBox {
            display: flex;
            flex-wrap: wrap;
            gap: 32px;
            margin-top: 32px;

            &.mt32 {
                margin-top: 32px;
            }

            .col02 {
                width: calc((100% - 32px) / 2);

                @include for-size(tablet-phone) {
                    width: 100%;
                }
            }
        }
    }

    .col02 {
        width: calc((100% - 50px) / 2);
        position: relative;

        &:has( .error) {
            input {
                border-color: #F91313;
            }
        }

        @include for-size(tablet-phone) {
            width: 100%;
        }
    }

    .col03 {
        width: calc((100% - 48px) / 3);
        position: relative;

        &:has( .error) {
            input {
            border-color: #F91313;
            }
        }

        @include for-size(tablet-phone) {
            width: 100%;
        }
    }

    .labelDiv {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;

        &.mb8 {
            margin-bottom: 8px;
        }

        button {
            border: none;
            background-color: transparent;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #555555;
            font-weight: 600;
            font-size: 13px;
            line-height: 21px;
            cursor: pointer;
            margin-left: auto;
        }
    }

    label {
        color: $black;
        font-weight: 700;
        font-size: 14px;
        line-height: 22px;
        display: inline-flex;
        align-items: center;


        span {
            font-weight: 400;

            @include for-size(tablet-phone) {
                font-size: 12px;
            }
        }

        sup {
            color: $red;
            line-height: 0px;
        }

        .tooltipIcon {
            width: 22px;
            height: 22px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin-left: 2px;
            cursor: pointer;

            &:hover {
            .tooltip {
                display: block;
            }
            }
        }

        .tooltip {
            background-color: #1E1E1E;
            border-radius: 4px;
            color: $white;
            font-size: 14px;
            line-height: 24px;
            text-align: center;
            max-width: 330px;
            padding: 8px;
            width: 84vw;
            position: absolute;
            left: 50%;
            bottom: calc(100% + 10px);
            transform: translateX(-50%);
            display: none;

            &:after {
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid #1E1E1E;
            content: "";
            position: absolute;
            left: 50%;
            bottom: -9px;
            transform: translateX(-50%);
            }
        }
    }

    .selectRegion {
        color: #555555;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        display: inline-block;
        width: 100%;
        margin-top: 8px;
    }

    input[type='text'],
    input[type='email'],
    input[type='password'],
    input[type='tel'],
    input[type='number'],
    .dropdownToggle,
    textarea {
        background-color: #FFFFFF;
        border: 1px solid #707070;
        border-radius: 4px;
        width: 100%;
        height: 44px;
        color: #515B6F;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        text-align: left;
        padding: 10px 8px;
        outline: none !important;

        &:disabled {
            background-color: #F7F7F7;
            font-style: italic;
        }

        &::placeholder {
            color: #707070;
            font-weight: 400;
            opacity: 1;
        }
    }

    textarea {
        height: 88px;
        resize: none;
    }

    .characterLimit {
        color: $black;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
        margin-top: 4px;
    }

    .dropdown {
        position: relative;

        .dropdownToggle {
            display: inline-flex;
            align-items: center;
            justify-content: flex-start;
            background-image: url(/images/icons/icon-down-arrow.svg);
            background-repeat: no-repeat;
            background-position: right 12px center;
        }

        .dropdownMenu {
            background-color: #FFFFFF;
            border-radius: 4px;
            -webkit-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            -moz-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            -ms-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            -o-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            max-height: 180px;
            overflow-x: hidden;
            overflow-y: auto;
            position: absolute;
            width: 100%;
            left: 0px;
            top: calc(100% + 8px);
            z-index: 1;
        }

        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-track {
            background: #F5F5F5; 
        }

        &::-webkit-scrollbar-thumb {
            background: #929292; 
            border-radius: 24px;
        }
    }

    .dropdownItem {
        background-color: #FFFFFF;
        border: none;
        display: inline-flex;
        align-items: center;
        justify-content: flex-start;
        font-weight: 500;
        font-size: 15px;
        line-height: 22px;
        height: 44px;
        padding: 4px 12px;
        width: 100%;
    }

    .submitBtn {
        background-color: $secondary;
        border: none;
        border-radius: 8px;
        color: $black;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        font-size: 18px;
        line-height: 24px;
        padding: 4px 27px;
        min-width: 240px;
        height: 60px;

        @include for-size(tablet-phone) {
            height: 50px;
            width: 100%;
        }
    }

    .checkBox {
        position: relative;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        margin-bottom: 16px;

        &.mt36 {
            margin-top: 36px;

            @include for-size(tablet-phone) {
                margin: 0px;
            }
        }

        &:last-child {
            margin-bottom: 0px;
        }

        input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 24px;
            width: 24px;
            z-index: 3;

            &:checked {
                ~ .checkmark {
                    background-color: #555555;
                    border-color: #555555;

                    &:after {
                        display: block;
                    }
                }
            } 
        }

        .checkmark {
            height: 18px;
            width: 18px;
            background-color: #FFFFFF;
            border-radius: 4px;
            border: 2px solid #555555;
            position: absolute;
            left: 0px;
            top: 2px;

            &:after {
                content: "";
                position: absolute;
                display: none;
                left: 5px;
                top: 0px;
                width: 5px;
                height: 10px;
                border: solid $white;
                border-width: 0 1px 1px 0;
                -webkit-transform: rotate(45deg);
                -ms-transform: rotate(45deg);
                transform: rotate(45deg);
            }
        }

        p {
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            padding-left: 34px;
        }
    }

    .radioGroup {
        position: relative;
        padding-left: 32px;
        cursor: pointer;
        user-select: none;
        color: #555555;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;

        input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            width: 100%;
            height: 100%;
            left: 0px;

            &:checked {
                ~ .checkmark {
                    &:after {
                        display: block;
                    }
                }
            }
        }

        .checkmark {
            border: 2px solid #555555;
            background-color: $white;
            border-radius: 50%;
            position: absolute;
            top: 0;
            left: 0;
            height: 20px;
            width: 20px;

            &:after {
                background-color: #555555;
                content: "";
                position: absolute;
                display: none;
                top: 3px;
                left: 3px;
                width: 10px;
                height: 10px;
                border-radius: 50%;
            }
        }

        p {
            color: #555555;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px
        }
    }

    .verifyBtn {
        border: none;
        background-color: #555555;
        border-radius: 4px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: $white;
        font-weight: 500;
        font-size: 14px;
        line-height: 20px;
        text-align: center;
        height: 44px;
        width: 187px;
        cursor: pointer;
        margin-top: 26px;
    }
}


.BrowseUpload {
    border: 1px dashed #9D9D9D;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 12px;

    @include for-size(phone-only) {
        flex-direction: column;
        gap: 20px;
        padding: 20px 16px;
    }

    .dragDrop {
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        padding: 24px 8px;
        width: calc((100% - 58px) / 2);

        @include for-size(phone-only) {
            padding: 0px;
            width: 100%;
        }

        img {
            max-width: 58px;
        }

        span {
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            text-align: center;
        }
    }

    .or {
        background-color: #FFFBEE;
        border: 1px solid #FBD758;
        border-radius: 50%;
        font-weight: 700;
        font-size: 12px;
        line-height: 32px;
        text-align: center;
        text-transform: uppercase;
        width: 34px;
        height: 34px;
        position: relative;

        &:before,
        &:after {
            background-color: #FBD758;
            content: "";
            width: 1px;
            height: 30px;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
        }

        &:before {
            bottom: 100%;
        }

        &::after {
            top: 100%;
        }

        @include for-size(phone-only) {
            &:before,
            &:after {
                width: 80px;
                height: 1px;
                position: absolute;
                top: 50%;
                transform: translate(0px, -50%);
            }

            &:before {
                bottom: auto;
                left: -80px;
            }

            &::after {
                left: auto;
                right: -80px;
            }
        }
    }

    .browseFile {
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        width: calc((100% - 58px) / 2);

        @include for-size(phone-only) {
            width: 100%;
        }

        button {
            border: none;
            background-color: #555555;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: $white;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            text-align: center;
            height: 44px;
            width: 187px;
            cursor: pointer;
        }
    }
}

.supportsType {
    color: #555555;
    font-weight: 400;
    font-size: 12px;
    line-height: 18px;
    margin-top: 8px;

    strong {
        font-weight: 700;
        color: #000000;
    }
}

.checkboxList {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    column-gap: 24px;
    row-gap: 16px;
    margin-top: 16px;

    li {
        width: calc((100% - 48px) / 3);

        @include for-size(tablet-phone) {
            width: 100%;
        }

        &.flexList {
            display: flex;
            gap: 16px;

            .colIn02 {
                width: calc((100% - 16px) / 2);

                @include for-size(tablet-phone) {
                    width: 100%;
                }
            }
        }

        .checkBox {
            display: inline-block;
            max-width: 100%;

            input {
                width: 100%;
                height: 100%;
            }
        }
    }

    &.radioList {
        column-gap: 60px;

        li {
            width: auto;
        }
    }
}

.verificationOption {
    text-align: center;
    position: relative;
    margin-top: 44px;
    margin-bottom: 28px;

    span {
        background-color: #FFFBEE;
        border: 1px solid #FBD758;
        border-radius: 50%;
        display: inline-block;
        font-weight: 700;
        font-size: 16px;
        line-height: 46px;
        text-align: center;
        text-transform: uppercase;
        width: 48px;
        height: 48px;
        position: relative;
        z-index: 3;
    }

    &:before,
    &:after {
        background-color: #FBD758;
        content: "";
        width: 50%;
        height: 1px;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;
    }

    &:before {
        left: 0px;
    }

    &::after {
        right: 0px;
    }
}

.uploadDocuments {
    h5 {
        color: $black;
        font-weight: 700;
        font-size: 16px;
        line-height: 22px;
        text-align: center;
    }

    h6 {
        color: $black;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        text-align: center;
        margin-top: 12px;
    }

     .dot {
        text-align: center;
        padding-top: 8px;

        h5 {
            margin-top: 12px;
        }
    }
}

.radioLabel {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-weight: normal; // disables default bold
  gap: 8px;
}

.labelText {
  font-weight: normal; // ensures the label text stays normal
}

.browseFilesList {
    margin-top: 20px;

    li {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;

        &:last-child { 
            margin-bottom: 0px;
        }

        p {
            color: #555555;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }

        .remove {
            border: none;
            background-color: transparent;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: #555555;
            font-weight: 600;
            font-size: 13px;
            line-height: 20px;
            margin-left: auto;
        }

        .progressber {
            width: 100%;
            order: 4;
        }
    }
}