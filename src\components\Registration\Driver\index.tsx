"use client";

import React, { useEffect, useState } from "react";
import Link from "next/link";
import css from  "./driverregister.module.scss";
import { useFormik } from "formik";
import * as Yup from "yup";
import { registration } from "@/services/userService";
import { saveToken } from "@/utils/loginRegister";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { getStates, State } from "@/services/locationService";
import { toast, ToastContainer } from "react-toastify";
import Dropdown from "@/components/Common/Dropdown";

interface FormValues {
  firstName: string;
  lastName: string;
  zip: string;
  email: string;
  phone: string;
  city: string;
  state: string;
  createPassword: string;
  confirmPassword: string;
  agreeToTerms: boolean;
 
}

interface Props {
  lang: "en" | "es";
  onSuccess: (data: { userId: string; phone: string }) => void;
}

const DriverRegistration: React.FC<Props> = ({ lang, onSuccess }) => {
  const [states, setStates] = useState<State[]>([]);

  const [showCreatePassword, setShowCreatePassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  useEffect(() => {
    const fetchStates = async () => {
      try {
        const res = await getStates();
        setStates(res);
      } catch (error) {
        console.error("Failed to fetch states:", error);
      }
    };
    fetchStates();
  }, []);

  const formik = useFormik({
    initialValues: {
      firstName: "",
      lastName:"",
      email: "",
      phone: "",
      city: "",
      state: "",
      zip:"",
      createPassword: "",
      confirmPassword: "",
      agreeToTerms: false,

    },
    validationSchema: Yup.object({
      firstName: Yup.string().notRequired(),
      lastName: Yup.string().notRequired(),
      email: Yup.string().email("Invalid email").required("Email is required"),
        zip: Yup.string()
             .matches(/^\d{5}$/, "Enter a valid zip code")
             .required("Zip Code is required"),
      phone: Yup.string()
        .matches(/^[0-9]{10,15}$/, "Invalid phone number")
        .required("Phone is required"),
      city: Yup.string().notRequired(),
      state: Yup.string().notRequired(),
      createPassword: Yup.string()
        .min(8, "Password must be at least 8 characters")
        .matches(
          /^(?=.*[0-9])(?=.*[!@#$%^&*])/,
          "Password must contain at least one number and one special character (!@#$%^&*)"
        )
        .required("New Password is required"),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref("createPassword")], "Passwords must match")
        .required("Confirm Password is required"),
      agreeToTerms: Yup.boolean().oneOf(
        [true],
        "You must agree to the Terms and Privacy Policy"
      ),
    }),

    onSubmit: (values) => {
      console.log("Form Submitted", values);
      handleSubmit(values);
    },
  });

const handleSubmit = async (values: FormValues) => {
  const selectedState = states.find(
    (s) => s.stateId.toString() === values.state
  );
  const stateId = selectedState?.stateId ?? "";

  const payload = {
    email: values.email,
    password: values.createPassword,
    firstName:values.firstName,
    lastName:values.lastName,
    contactNumber: values.phone,
    city: values.city,
    state: stateId,
    zipCode: values.zip,       
    isCompany: false,
  };

  try {
    const res = await registration(payload);

    if (res.status) {
      toast.success("Registration successful");
      saveToken(
        res?.data?.user?.accessToken,
        res?.data?.user?.refreshToken,
        values.email,
        false,
        
        // res?.data?.user?.isCompany
      );
      onSuccess({ userId: res?.data?.user?.userId, phone: values.phone })
    } else {
      toast.error(`${res.message || "Registration failed"}`);
    }
  } catch (err) {
    toast.error("Something went wrong during registration.");
    console.error(err);
  }
};

  return (
    <section className={css.registrationSection}>
      <div className={css.loginWrapper}>
        <h1>Create Your Driver Account</h1>
        <p>
        Get started in seconds to browse job opportunities.
        </p>
        <span className={css.requiredFields}>Required fields are marked with<sup>*</sup></span>

        <form onSubmit={formik.handleSubmit} className={css.registrationForm}>
{/* first name last name */}
                 <div className={`${css.formGroup} ${css.col02}`}>
            <label htmlFor="firstName">First Name</label>
            <input
              type="text"
              name="firstName"
              placeholder="Enter your first name  "
    
              value={formik.values.firstName}
              onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            />
            {formik.touched.city && formik.errors.city && (
              <div className={css.error}>{formik.errors.city}</div>
            )}
          </div>
               <div className={`${css.formGroup} ${css.col02}`}>
            <label htmlFor="lastName">Last Name </label>
            <input
              type="text"
              name="lastName"
              placeholder="Enter  your last name"
              value={formik.values.lastName}
              onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            />
            {formik.touched.city && formik.errors.city && (
              <div className={css.error}>{formik.errors.city}</div>
            )}
          </div>
          {/* Company Name */}
         

          {/* Email & Phone */}
          <div className={`${css.formGroup} ${css.col02}`}>
            <label htmlFor="email">
              Email Address<sup>*</sup>
         
            </label>
            <input
              type="email"
              name="email"
              placeholder="<EMAIL>"
              value={formik.values.email}
              onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            />
            {formik.touched.email && formik.errors.email && (
              <div className={css.error}>{formik.errors.email}</div>
            )}
          </div>
          <div className={`${css.formGroup} ${css.col02}`}>
            <label htmlFor="phone">
          Phone Number<sup>*</sup>
            
             
            </label>
            <input
              type="tel"
              name="phone"
              maxLength={10}
              placeholder="(*************"
              value={formik.values.phone}
              onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            />
            {formik.touched.phone && formik.errors.phone && (
              <div className={css.error}>{formik.errors.phone}</div>
            )}
          </div>

          {/* City & State */}
          <div className={`${css.formGroup} ${css.col02}`}>
            <label htmlFor="city">Primary Location (City)</label>
            <input
              type="text"
              name="city"
              placeholder="Enter city"
              
              value={formik.values.city}
              onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            />
            {formik.touched.city && formik.errors.city && (
              <div className={css.error}>{formik.errors.city}</div>
            )}
          </div>

          <div className={`${css.formGroup} ${css.col02}`}>
            <label htmlFor="state">Primary Location (State)</label>
            <Dropdown
              options={states.map(state => ({
                value: state.stateId.toString(),
                label: state.name[lang] || state.name["en"]
              }))}
              value={formik.values.state}
              placeholder="Select State"
              onChange={(value) => formik.setFieldValue("state", value)}
              error={formik.touched.state && formik.errors.state ? formik.errors.state : undefined}
              name="state"
            />
          </div>

     {/*zip code */}
          <div className={`${css.formGroup} ${css.col02}`}>
            <label htmlFor="zipCode">Primary Location (Zip Code)<sup>*</sup></label>
            <input
              type="text"
              name="zip"
              placeholder="e.g., 90210"
              maxLength={5}
              value={formik.values.zip}
              onChange={formik.handleChange}
            
            />
            {formik.touched.zip && formik.errors.zip && (
              <div className={css.error}>{formik.errors.zip}</div>
            )}
          </div>
          
          <div className={`${css.formGroup} ${css.col02}`}></div>
          {/* Passwords */}
          <div className={`${css.formGroup} ${css.col02}`}>
            <label htmlFor="createPassword">
              Create Password<sup>*</sup>
              <span className={css.tooltipIcon}>
                <img src="/images/icons/icon-info.svg" alt="" />
                <span className={css.tooltip}>
                  Min 8 characters, 1 number, 1 symbol
                  (e.g., !@#$).
                </span>
              </span>            
            </label>
            <input
              type={showCreatePassword ? "text" : "password"}
              name="createPassword"
              placeholder="Create Password"
              value={formik.values.createPassword}
              onChange={formik.handleChange}
          
            />
            <div
              className={css.showPassword}
              onClick={() => setShowCreatePassword((prev) => !prev)}
            >
              {showCreatePassword ? <FaEye /> : <FaEyeSlash />}
            </div>
            {formik.touched.createPassword &&
              formik.errors.createPassword && (
                <div className={css.error}>{formik.errors.createPassword}</div>
              )}
          </div>
          <div className={`${css.formGroup} ${css.col02}`}>
            <label htmlFor="confirmPassword">Confirm Password<sup>*</sup></label>
            <input
              type={showConfirmPassword ? "text" : "password"}
              name="confirmPassword"
              placeholder="Confirm Password"
              value={formik.values.confirmPassword}
              onChange={formik.handleChange}
            // onBlur={formik.handleBlur}
            />
            <span
              className={css.showPassword}
              onClick={() => setShowConfirmPassword((prev) => !prev)}
            >
              {showConfirmPassword ? <FaEye /> : <FaEyeSlash />}
            </span>
            {formik.touched.confirmPassword &&
              formik.errors.confirmPassword && (
                <div className={css.error}>{formik.errors.confirmPassword}</div>
              )}
          </div>

        
          <div className={`${css.formGroup} ${css.col01}`}>
        

          {/* Terms Agreement (Required) */}
          <div className={css.checkBox}>
            <input
              type="checkbox"
              name="agreeToTerms"
              checked={formik.values.agreeToTerms}
              onChange={formik.handleChange}
            />
            <span className={css.checkmark}></span>

            <p>I agree to the DriverJobz {" "}
              <Link href="/terms-and-conditions">
                  Terms of Service
              </Link>
              {" "}
              and {" "}<Link href="/privacy-policy">Privacy Policy</Link></p>

            {formik.touched.agreeToTerms && formik.errors.agreeToTerms && (
              <div className={css.error}>{formik.errors.agreeToTerms}</div>
            )}
          </div>
          </div>
          <div className={`${css.formGroup} ${css.col01}`}>
            <button type="submit" className={css.submitBtn}>Create Account</button>
            <p>
              Already have an account? <Link href="/login/driver">Login</Link>
            </p>
          </div>
        </form>
        <ToastContainer
          position="top-right"
          autoClose={3000}
          hideProgressBar={false}
        />
      </div>
    </section>
  );
};

export default DriverRegistration;
