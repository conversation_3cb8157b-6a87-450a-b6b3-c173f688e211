// import { fetchWithAuth } from "@/utils/fetchWithAuth";
import { getCookie } from "cookies-next";

interface RegistrationRequest {
  email?: string;
  password?: string;
  name?: string;
  contactNumber?: string;
  city?: string;
  isCompany?: boolean;
  state?: string|number;
  zipCode?: string;
}

export interface ResetPasswordPayload {
  token?: string;
  newPassword?: string;
  email?: string;
}

// register user
export const registration = async (reqBody: RegistrationRequest) => {
  const apiUrl = `${process.env.NEXT_PUBLIC_API_V1}auth/register`;
  try {
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(reqBody),
    });
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Failed to fetch auth/register:", error);
  }
};
// login user
export const loginUser = async (reqBody: RegistrationRequest) => {
  const apiUrl = `${process.env.NEXT_PUBLIC_API_V1}auth/login`;
  try {
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(reqBody),
    });
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Failed to fetch auth/login:", error);
  }
};

export const getUserData = async () => {
  const apiUrl = `${process.env.NEXT_PUBLIC_API_V1}auth/me`;
  const token = getCookie("authToken");
  try {
    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `${token}`,
      },
    });
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Failed to fetch auth/profile:", error);
  }
};

/////////////////////////////////////////////////////////////
// forget password
export const forgetPasswordUser = async (reqBody: RegistrationRequest) => {
  const apiUrl = `${process.env.NEXT_PUBLIC_API_V1}auth/forget-password`;

  try {
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-type": "application/json",
      },
      body: JSON.stringify(reqBody),
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.log("Fieled to fetch auth/forget-password ", error);
  }
};

export const resetPassword = async (reqBody: ResetPasswordPayload) => {
  const apiUrl = `${process.env.NEXT_PUBLIC_API_V1}auth/reset-password`;
  try {
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-type": "application/json",
      },
      body: JSON.stringify(reqBody),
    });
    const data = await response.json();
    return data;
  } catch (error) {
    console.log("Fieled to fetch auth/forget-password ", error);
  }
};
