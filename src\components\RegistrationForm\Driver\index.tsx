"use client";
import React, { useEffect, useState } from "react";
import AccordionWrapper from "@/components/Common/Accordion";
import BasicInfo from "./BasicInfo";
import DriverCategory from "./DriverCategory";
import DriverLicense from "./DriverLicense";
import SpecialtyPermit from "./SpecialtyPermit";
import Safety from "./Safety";
import { getCookie } from "cookies-next";
import Verification from "./Verification";
import { ToastContainer } from "react-toastify";
import LanguageSkills from "./Languages";
import css from './driverRegistration.module.scss';
import {
  DriverBasicInfo,
  DriverCategoryData,
  DriverLicenseFormValues,
  SpecialPermit,
  DriverSafety,
  LanguageSkillsType,
} from "./types";

export type DriverDetailsResponse = DriverBasicInfo &
  DriverCategoryData &
  DriverLicenseFormValues &
  SpecialPermit &
  LanguageSkillsType &
  DriverSafety;

interface Props {
  lang: "en" | "es";
  initialStep?: number;
}

const registrationSteps = [
  { title: "Basic Info & Work Authorization", Component: BasicInfo },
  { title: "Primary Driver Category", Component: DriverCategory },
  { title: "Driver's License Info", Component: DriverLicense },
  {
    title: "Specialty Permit / License (Chauffeur, Taxi, Livery, etc.)",
    Component: SpecialtyPermit,
  },
  { title: "Safety & Compliance Summary (Last 3 Years)", Component: Safety },
  { title: "Language Skills", Component: LanguageSkills },
  { title: "Identity Verification Document Upload", Component: Verification },
];

const DriverRegistration = ({ lang, initialStep }: Props) => {
   console.log("**lang", lang);
  const allowedInitialStep = initialStep === 1 ? initialStep : undefined;
  const [activeAccordionIndex, setActiveAccordionIndex] = useState(allowedInitialStep ? allowedInitialStep - 1 : 0);
  const [hasInitialLoad, setHasInitialLoad] = useState(false);
  const [completedStepIndexes, setCompletedStepIndexes] = useState<number[]>([]);
  const [driverData, setDriverData] = useState<DriverDetailsResponse | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [currentStage, setCurrentStage] = useState(2);

  const fetchDriverDetails = async () => {
    try {
      const token = getCookie("authToken");
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_API_V1}driver/driver-details`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            ...(token ? { Authorization: `${token}` } : {}),
          },
        }
      );

      if (!res.ok) throw new Error("Fetch failed");
      const data = await res.json();

      const fetchedStep = data?.data?.currentStep ?? 0;
      const fetchedStage = data?.data?.currentStage ?? 2;
      setDriverData(data?.data?.driver);
      setCurrentStep(fetchedStep);
      setCurrentStage(fetchedStage);

      if (fetchedStage === 2) {
        setCompletedStepIndexes((prev) => {
          const updated = new Set(prev);
          for (let i = 0; i <= fetchedStep; i++) {
            updated.add(i);
          }
          return Array.from(updated);
        });
      } else if (fetchedStage >= 3) {
        const allSteps: any = [];
        for (let i = 0; i < registrationSteps.length; i++) {
          allSteps.push(i);
        }
        setCompletedStepIndexes(allSteps);
      }
      if (fetchedStage === 2 && fetchedStep < registrationSteps.length) {
        setActiveAccordionIndex(!hasInitialLoad && allowedInitialStep ? allowedInitialStep - 1 : fetchedStep);
      } else {
        setActiveAccordionIndex(!hasInitialLoad && allowedInitialStep ? allowedInitialStep - 1 : -1);
      }
      setHasInitialLoad(true);
    } catch (e) {
      console.error("Failed to load driver details: ", e);
    }
  };

  useEffect(() => {
    fetchDriverDetails();
  }, []);

  const handleStepSubmit = async(index: number) => {
    
    setCompletedStepIndexes((prev) => {
      const updated = new Set(prev);
      updated.add(index);
      return Array.from(updated);
    });

    const nextIndex = index + 1;

    if (nextIndex < registrationSteps.length) {
      setActiveAccordionIndex(nextIndex);
      setCurrentStep(nextIndex);  
    } else {
      setActiveAccordionIndex(-1);
      setCurrentStep(registrationSteps.length);
    }
    await  fetchDriverDetails();
  };

  const handleAccordionClick = (index: number) => {
    const isAllowedToClick =
      index === 0 || completedStepIndexes.includes(index - 1);
    if (isAllowedToClick) {
      setActiveAccordionIndex(activeAccordionIndex === index ? -1 : index);
    }
  };

  if (!driverData) {
    return <div>Loading...</div>;
  }

  return (
    <section className={css.driversRegistration}>
      <div className={css.container}>
        <h6 className={css.required}>
          Required fields are marked with <span>*</span>
        </h6>
        {registrationSteps.map(({ title, Component }, index) => (
          <AccordionWrapper
            key={index}
            title={title}
            isOpen={activeAccordionIndex === index}
            isComplete={completedStepIndexes.includes(index)}
            isClickable={index === 0 || completedStepIndexes.includes(index - 1)}
            setIsOpen={() => handleAccordionClick(index)}
          >
            {activeAccordionIndex === index && (
              <Component
                onFormSubmit={() => handleStepSubmit(index)}
                initialData={driverData}
                currentStep={currentStep}
                currentStage={currentStage}
              />
            )}
          </AccordionWrapper>
        ))}
      </div>
      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
      />
    </section>
  );
};

export default DriverRegistration;
