import React from "react";
import styles from "./Textarea.module.scss";
import { FormikProps } from "formik";
import Tooltip from "../Tooltip";

interface TextAreaProps<T extends Record<string, unknown>> {
  className?: string;
  label?: string;
  desc?: string;
  fieldName: string;
  placeholder?: string;
  hide?: boolean;
  limitText?: string;
  formik: FormikProps<T>;
  tooltipMsg?: string;
}

const Textarea = <T extends Record<string, unknown>>({
  className = "",
  label = "",
  desc = "",
  fieldName = "",
  placeholder = "",
  hide = false,
  limitText = "Max 500 Characters",
  formik,
  tooltipMsg
}: TextAreaProps<T>) => {

  return (
    <div className={`${styles.columnField} ${styles[className]}`}>
      {label && (
        <label htmlFor={fieldName}>
          {label} {desc && <span className={styles.desc}>{desc}</span>}{" "}
          {!hide && <span className={styles.important}>*</span>}
          {tooltipMsg && <Tooltip tooltipMsg={tooltipMsg} />}
        </label>
      )}
      <textarea
        id={fieldName}
        name={fieldName}
        placeholder={placeholder}
        onChange={formik?.handleChange}
        value={formik?.values?.[fieldName] as string | number | undefined ?? ""}
      />
      <span>{limitText}</span>
      {formik?.touched[fieldName] && typeof formik?.errors[fieldName] === "string" && (
        <div className="error_msg">
          {formik?.errors[fieldName]}
        </div>
      )}
    </div>
  );
};

export default Textarea;
