"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useNemtDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import {
  FormValue,
  submitDriverDetails,
  fetchDriverDetails,
  fetchSchoolBusAideFormFields,
  fetchDotMedicalCard,
} from "@/services/driverFormService";

interface MedicalCertification {
  driverOtherCertificationId: number;
  certificateName: string;
  issuingBody: string;
  expirationDate?: string;
  dateIssued?: string;
}



import DateInput from "@/components/Common/DateInput/DateInput";
import RadioGroup from "@/components/Common/RadioGroup";
import Dropdown from "@/components/Common/Dropdown";
import css from './nemtMedical.module.scss';

interface MedicalFormValues {
  dotMedicalRequired: string;
  dotMedicalStatus: string;
  dotMedicalExpiration: Date | null;
  dotExaminerName: string;
  dotExaminerPhone: string;
  dotNationalRegistryNumber: string;
  dotRestriction: string;
  dotExemption: string;
  cprCertified: string;
  cprType: string;
  cprExpiration: Date | null;
  firstAidCertified: string;
  firstAidType: string;
  firstAidExpiration: Date | null;
  patsCertified: string;
  patsDate: Date | null;
  patsAgency: string;
  mavoCertified: string;
  mavoDate: Date | null;
  mavoAgency: string;
  defensiveDriving: string;
  holdOtherCertification: string;
  otherCertifications: Array<{
    id?: number;
    certificateName: string;
    issuingBody: string;
    expirationDate: Date | null;
    dateIssued: Date | null;
  }>;
}

const NemtMedical: React.FC = () => {
  const { updateStepFromApiResponse, goToPreviousStep, canGoBack } = useNemtDriverCategory();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const [dotMedicalStatusOptions, setDotMedicalStatusOptions] = useState<FormValue[]>([]);
  const [cprTypeOptions, setCprTypeOptions] = useState<FormValue[]>([]);
  const [firstAidTypeOptions, setFirstAidTypeOptions] = useState<FormValue[]>([]);

  const validationSchema = Yup.object({
    dotMedicalRequired: Yup.string().required("DOT Medical requirement is required"),
    dotMedicalStatus: Yup.string().when("dotMedicalRequired", {
      is: (val: string) => val === "yes" || val === "unsure",
      then: (schema) => schema.required("DOT Medical status is required"),
      otherwise: (schema) => schema,
    }),
    dotMedicalExpiration: Yup.date().when("dotMedicalStatus", {
      is: (val: string) => val && val !== "disqualified" && val !== "n/a",
      then: (schema) => schema.required("DOT Medical expiration date is required"),
      otherwise: (schema) => schema.nullable(),
    }),
    cprCertified: Yup.string().required("CPR certification status is required"),
    cprType: Yup.string().when("cprCertified", {
      is: "Yes",
      then: (schema) => schema.required("CPR certification type is required"),
      otherwise: (schema) => schema,
    }),
    cprExpiration: Yup.date().when("cprCertified", {
      is: "Yes",
      then: (schema) => schema.required("CPR expiration date is required"),
      otherwise: (schema) => schema.nullable(),
    }),
    firstAidCertified: Yup.string().required("First Aid certification status is required"),
    firstAidType: Yup.string().when("firstAidCertified", {
      is: "Yes",
      then: (schema) => schema.required("First Aid certification type is required"),
      otherwise: (schema) => schema,
    }),
    firstAidExpiration: Yup.date().when("firstAidCertified", {
      is: "Yes",
      then: (schema) => schema.required("First Aid expiration date is required"),
      otherwise: (schema) => schema.nullable(),
    }),
    patsCertified: Yup.string().required("PATS certification status is required"),
    mavoCertified: Yup.string().required("MAVO certification status is required"),
    defensiveDriving: Yup.string().required("Defensive driving status is required"),
    holdOtherCertification: Yup.string().required("Other certification status is required"),
  });

  const formik = useFormik<MedicalFormValues>({
    initialValues: {
      dotMedicalRequired: "No (Driving standard van/car)",
      dotMedicalStatus: "",
      dotMedicalExpiration: null,
      dotExaminerName: "",
      dotExaminerPhone: "",
      dotNationalRegistryNumber: "",
      dotRestriction: "",
      dotExemption: "",
      cprCertified: "Yes",
      cprType: "",
      cprExpiration: null,
      firstAidCertified: "Yes",
      firstAidType: "",
      firstAidExpiration: null,
      patsCertified: "No / Not Formally Certified",
      patsDate: null,
      patsAgency: "",
      mavoCertified: "No / Not Formally Certified",
      mavoDate: null,
      mavoAgency: "",
      defensiveDriving: "No",
      holdOtherCertification: "no",
      otherCertifications: [{
        certificateName: "",
        issuingBody: "",
        expirationDate: null,
        dateIssued: null,
      }],
    },
    validationSchema,
    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);



        // Load form field options using mixed approach
        try {
          // Use the proven fetchDotMedicalCard for DOT Medical options
          const dotMedicalOptions = await fetchDotMedicalCard();
          console.log("DOT Medical options:", dotMedicalOptions);
          setDotMedicalStatusOptions(dotMedicalOptions);

          // Use School Bus Aide approach for CPR and First Aid options
          const formFieldData = await fetchSchoolBusAideFormFields();
          console.log("Form field data:", formFieldData);

          // Set CPR options
          if (formFieldData["cpr-certification-type-driver-bus-aide-assistant"]) {
            console.log("CPR options:", formFieldData["cpr-certification-type-driver-bus-aide-assistant"]);
            setCprTypeOptions(formFieldData["cpr-certification-type-driver-bus-aide-assistant"]);
          }

          // Set First Aid options
          if (formFieldData["first-aid-certification-type-driver-bus-aide-assistant"]) {
            console.log("First Aid options:", formFieldData["first-aid-certification-type-driver-bus-aide-assistant"]);
            setFirstAidTypeOptions(formFieldData["first-aid-certification-type-driver-bus-aide-assistant"]);
          }
        } catch (error) {
          console.error("Failed to fetch form field options:", error);
        }

        // Load existing driver data
        const response = await fetchDriverDetails();
        if (response?.status && response?.data?.driver) {
          const driver = response.data.driver;

          // Helper function to safely access properties with runtime type checking
          const getString = (obj: object, key: string): string => {
            const value = (obj as Record<string, string>)[key];
            return typeof value === 'string' ? value : '';
          };

          const getBoolean = (obj: object, key: string): boolean => {
            const value = (obj as Record<string, boolean>)[key];
            return typeof value === 'boolean' ? value : false;
          };

          const getDate = (obj: object, key: string): Date | null => {
            const value = (obj as Record<string, string>)[key];
            return typeof value === 'string' && value ? new Date(value) : null;
          };

          const getCertifications = (obj: object, key: string): MedicalCertification[] => {
            const value = (obj as Record<string, MedicalCertification[]>)[key];
            return Array.isArray(value) ? value : [];
          };

          console.log('🔍 Raw driver data for medical form:', {
            dotMedicalCardStatus: driver.dotMedicalCardStatus,
            dotExpirationDate: driver.dotExpirationDate,
            dotExaminerName: driver.dotExaminerName,
            dotExaminerPhone: driver.dotExaminerPhone,
            dotNationalRegistryNumber: driver.dotNationalRegistryNumber,
            dotRestriction: driver.dotRestriction,
            dotExemption: driver.dotExemption,
            cprCertification: driver.cprCertification,
            cprCertificationExpirationDate: driver.cprCertificationExpirationDate,
            firstAidCertification: driver.firstAidCertification,
            firstAidCertificationExpirationDate: driver.firstAidCertificationExpirationDate,
            patsCertification: driver.patsCertification,
            patsCertificateIssuingAgency: driver.patsCertificateIssuingAgency,
            defensiveDrivingCourseCompleted: getBoolean(driver, "defensiveDrivingCourseCompleted"),
            holdOtherCertification: driver.holdOtherCertification,
            driverOtherCertifications: driver.driverOtherCertifications
          });

          // Map existing driver data to form values using available fields
          const mappedValues = {
            dotMedicalRequired: getString(driver, "dotRequired") === "Yes" ? "Yes (Driving larger vehicle / Employer policy)" : getString(driver, "dotRequired") === "No" ? "No (Driving standard van/car)" : "Unsure", // Map API values back to form labels
            dotMedicalStatus: driver.dotMedicalCardStatus ? "Current / Valid" : "",
            dotMedicalExpiration: getDate(driver, "dotExpirationDate"),
            dotExaminerName: getString(driver, "dotExaminerName"),
            dotExaminerPhone: getString(driver, "dotExaminerPhone"),
            dotNationalRegistryNumber: getString(driver, "dotNationalRegistryNumber"),
            dotRestriction: getString(driver, "dotRestriction"),
            dotExemption: getString(driver, "dotExemption"),
            cprCertified: driver.cprCertification === true ? "Yes" : driver.cprCertification === false ? "No / Expired" : "Yes", // Default to "Yes" if no data
            cprType: getString(driver, "cprCertificationType"),
            cprExpiration: getDate(driver, "cprCertificationExpirationDate"),
            firstAidCertified: driver.firstAidCertification === true ? "Yes" : driver.firstAidCertification === false ? "No / Expired" : "Yes", // Default to "Yes" if no data
            firstAidType: getString(driver, "firstAidCertificationType"),
            firstAidExpiration: getDate(driver, "firstAidCertificationExpirationDate"),
            patsCertified: driver.patsCertification === true ? "Yes, Current" : driver.patsCertification === false ? "Yes, Expired / Need Refresher" : "No / Not Formally Certified",
            patsDate: getDate(driver, "patsCertificationDate"),
            patsAgency: getString(driver, "patsCertificateIssuingAgency"),
            mavoCertified: getBoolean(driver, "mavoCertification") === true ? "Yes, Current" : getBoolean(driver, "mavoCertification") === false ? "Yes, Expired / Need Refresher" : "No / Not Formally Certified",
            mavoDate: getDate(driver, "mavoCertificationDate"),
            mavoAgency: getString(driver, "mavoCertificateIssuingAgency"),
            defensiveDriving: getBoolean(driver, "defensiveDrivingCourseCompleted") ? "Yes" : "No",
            holdOtherCertification: driver.holdOtherCertification === true ? "yes" : "no",
            otherCertifications: getCertifications(driver, "driverOtherCertifications").map((cert: MedicalCertification) => ({
              id: cert.driverOtherCertificationId,
              certificateName: cert.certificateName,
              issuingBody: cert.issuingBody,
              expirationDate: cert.expirationDate ? new Date(cert.expirationDate) : null,
              dateIssued: cert.dateIssued ? new Date(cert.dateIssued) : null,
            })),
          };

          console.log('🎯 Mapped form values for medical:', mappedValues);
          formik.setValues(mappedValues);
        }
      } catch (error) {
        console.error("Error loading data:", error);
        toast.error("Failed to load data. Please refresh.");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const handleSubmit = async (values: MedicalFormValues, shouldContinue: boolean) => {
    try {
      setIsLoading(true);

      // Map DOT medical status to number if needed
      const getDotMedicalStatusId = (status: string): number | null => {
        if (!status) return null;
        // This would need to be mapped based on the actual form values
        // For now, returning 1 as example
        return 1;
      };

      const payload = {
        currentStage: 3,
        currentStep: 3,
        driver: {
          dotRequired: values.dotMedicalRequired === "Yes (Driving larger vehicle / Employer policy)" ? "Yes" : values.dotMedicalRequired === "No (Driving standard van/car)" ? "No" : null,
          dotMedicalCardStatus: getDotMedicalStatusId(values.dotMedicalStatus),
          dotExpirationDate: values.dotMedicalExpiration?.toISOString() || null,
          dotExaminerName: values.dotExaminerName || null,
          dotExaminerPhone: values.dotExaminerPhone || null,
          dotNationalRegistryNumber: values.dotNationalRegistryNumber || null,
          dotRestriction: values.dotRestriction || null,
          dotExemption: values.dotExemption || null,
          cprCertification: values.cprCertified === "Yes",
          cprCertificationType: values.cprType ? parseInt(values.cprType) : null,
          cprCertificationExpirationDate: values.cprExpiration?.toISOString() || null,
          firstAidCertification: values.firstAidCertified === "Yes",
          firstAidCertificationType: values.firstAidType ? parseInt(values.firstAidType) : null,
          firstAidCertificationExpirationDate: values.firstAidExpiration?.toISOString() || null,
          patsCertification: values.patsCertified === "Yes, Current" ? true : values.patsCertified === "Yes, Expired / Need Refresher" ? false : null,
          patsCertificationDate: values.patsDate?.toISOString() || null,
          patsCertificateIssuingAgency: values.patsAgency || null,
          mavoCertification: values.mavoCertified === "Yes, Current" ? true : values.mavoCertified === "Yes, Expired / Need Refresher" ? false : null,
          mavoCertificationDate: values.mavoDate?.toISOString() || null,
          mavoCertificateIssuingAgency: values.mavoAgency || null,
          defensiveDrivingCourseCompleted: values.defensiveDriving.startsWith("Yes"),
          holdOtherCertification: values.holdOtherCertification === "yes",
          driverOtherCertifications: values.otherCertifications.map((cert, index) => ({
            ...(cert.id && { driverOtherCertificationId: cert.id }),
            certificateName: cert.certificateName,
            issuingBody: cert.issuingBody,
            expirationDate: cert.expirationDate?.toISOString() || null,
            dateIssued: cert.dateIssued?.toISOString() || null,
            rank: index + 1
          }))
        }
      };

      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Medical information saved successfully!");
          window.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to save medical information. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error submitting medical information:", error);
      toast.error("Failed to save medical information. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return <div className={css.loading}>Loading...</div>;
  }

  return (
    <div className={css.medical}>
      <h3>Step 3: Medical & Certifications (NEMT Driver)</h3>
      <p>Provide details about relevant certifications like First Aid, CPR, PATS, MAVT/MAVO, and any state-specific NEMT requirements. DOT Medical Card is typically not required unless driving larger vehicles.</p>
      <h6 className={css.required}>Required fields are marked with <sup>*</sup></h6>
      
      <form onSubmit={formik.handleSubmit}>
        {/* DOT Medical Information */}
        <div className={css.section}>
          <h4>DOT Medical Information</h4>
          <p className={css.info}>Generally NOT required for standard NEMT vehicles unless over 10,001 lbs GVWR or designed for 16+ passengers.</p>
          
          <div className={css.formRow}>
            <label>Do your NEMT roles require you to hold a valid DOT Medical Card? *</label>
            <RadioGroup
              name="dotMedicalRequired"
              options={["Yes (Driving larger vehicle / Employer policy)", "No (Driving standard van/car)", "Unsure"]}
              selectedValue={formik.values.dotMedicalRequired}
              onChange={(value) => formik.setFieldValue("dotMedicalRequired", value)}
            />
            {formik.touched.dotMedicalRequired && formik.errors.dotMedicalRequired && (
              <p className={css.error}>{formik.errors.dotMedicalRequired}</p>
            )}
          </div>

          {(formik.values.dotMedicalRequired === "Yes (Driving larger vehicle / Employer policy)" || formik.values.dotMedicalRequired === "Unsure") && (
            <>
              <div className={css.formRow}>
                <label>DOT Medical Card Status: *</label>
                <Dropdown
                  options={dotMedicalStatusOptions.map(option => ({ value: option.label.en, label: option.label.en }))}
                  value={formik.values.dotMedicalStatus}
                  placeholder="Select Status"
                  onChange={(value) => formik.setFieldValue("dotMedicalStatus", value)}
                  error={formik.touched.dotMedicalStatus && formik.errors.dotMedicalStatus ? formik.errors.dotMedicalStatus : undefined}
                  name="dotMedicalStatus"
                />
                {dotMedicalStatusOptions.length === 0 && <p style={{color: 'red', fontSize: '12px'}}>No DOT Medical options loaded</p>}
                {formik.touched.dotMedicalStatus && formik.errors.dotMedicalStatus && (
                  <p className={css.error}>{formik.errors.dotMedicalStatus}</p>
                )}
              </div>

              {formik.values.dotMedicalStatus && formik.values.dotMedicalStatus !== "disqualified" && formik.values.dotMedicalStatus !== "n/a" && (
                <>
                  <div className={css.formRow}>
                    <label>DOT Medical Card Expiration Date: *</label>
                    <DateInput
                      selected={formik.values.dotMedicalExpiration}
                      onChange={(date) => formik.setFieldValue("dotMedicalExpiration", date)}
                    />
                    {formik.touched.dotMedicalExpiration && formik.errors.dotMedicalExpiration && (
                      <p className={css.error}>{formik.errors.dotMedicalExpiration}</p>
                    )}
                  </div>

                  <div className={css.formRow}>
                    <label>DOT Examiner Name:</label>
                    <input
                      type="text"
                      name="dotExaminerName"
                      value={formik.values.dotExaminerName}
                      onChange={formik.handleChange}
                      className={css.input}
                    />
                  </div>

                  <div className={css.formRow}>
                    <label>DOT Examiner Phone:</label>
                    <input
                      type="tel"
                      name="dotExaminerPhone"
                      value={formik.values.dotExaminerPhone}
                      onChange={formik.handleChange}
                      className={css.input}
                    />
                  </div>

                  <div className={css.formRow}>
                    <label>DOT National Registry Number:</label>
                    <input
                      type="text"
                      name="dotNationalRegistryNumber"
                      value={formik.values.dotNationalRegistryNumber}
                      onChange={formik.handleChange}
                      className={css.input}
                    />
                  </div>

                  <div className={css.formRow}>
                    <label>DOT Restriction:</label>
                    <input
                      type="text"
                      name="dotRestriction"
                      value={formik.values.dotRestriction}
                      onChange={formik.handleChange}
                      className={css.input}
                    />
                  </div>

                  <div className={css.formRow}>
                    <label>DOT Exemption:</label>
                    <input
                      type="text"
                      name="dotExemption"
                      value={formik.values.dotExemption}
                      onChange={formik.handleChange}
                      className={css.input}
                    />
                  </div>
                </>
              )}
            </>
          )}
        </div>

        {/* NEMT & Passenger Assistance Certifications */}
        <div className={css.section}>
          <h4>NEMT & Passenger Assistance Certifications</h4>
          
          <div className={css.formRow}>
            <label>Current CPR Certification? * <span className={css.info}>Often required for NEMT. BLS or Heartsaver acceptable.</span></label>
            <RadioGroup
              name="cprCertified"
              options={["Yes", "No / Expired"]}
              selectedValue={formik.values.cprCertified}
              onChange={(value) => formik.setFieldValue("cprCertified", value)}
            />
            {formik.touched.cprCertified && formik.errors.cprCertified && (
              <p className={css.error}>{formik.errors.cprCertified}</p>
            )}
          </div>

          {formik.values.cprCertified === "Yes" && (
            <>
              <div className={css.formRow}>
                <label>CPR Certification Type: *</label>
                <Dropdown
                  options={cprTypeOptions.map(option => ({ value: option.formValueId, label: option.label.en }))}
                  value={formik.values.cprType}
                  placeholder="Select Type"
                  onChange={(value) => formik.setFieldValue("cprType", value)}
                  error={formik.touched.cprType && formik.errors.cprType ? formik.errors.cprType : undefined}
                  name="cprType"
                />
                {cprTypeOptions.length === 0 && <p style={{color: 'red', fontSize: '12px'}}>No CPR options loaded</p>}
              </div>

              <div className={css.formRow}>
                <label>CPR Expiration Date: *</label>
                <DateInput
                  selected={formik.values.cprExpiration}
                  onChange={(date) => formik.setFieldValue("cprExpiration", date)}
                />
                {formik.touched.cprExpiration && formik.errors.cprExpiration && (
                  <p className={css.error}>{formik.errors.cprExpiration}</p>
                )}
              </div>
            </>
          )}

          <div className={css.formRow}>
            <label>Current First Aid Certification? *</label>
            <RadioGroup
              name="firstAidCertified"
              options={["Yes", "No / Expired"]}
              selectedValue={formik.values.firstAidCertified}
              onChange={(value) => formik.setFieldValue("firstAidCertified", value)}
            />
            {formik.touched.firstAidCertified && formik.errors.firstAidCertified && (
              <p className={css.error}>{formik.errors.firstAidCertified}</p>
            )}
          </div>

          {formik.values.firstAidCertified === "Yes" && (
            <>
              <div className={css.formRow}>
                <label>First Aid Certification Type: *</label>
                <Dropdown
                  options={firstAidTypeOptions.map(option => ({ value: option.formValueId, label: option.label.en }))}
                  value={formik.values.firstAidType}
                  placeholder="Select Type"
                  onChange={(value) => formik.setFieldValue("firstAidType", value)}
                  error={formik.touched.firstAidType && formik.errors.firstAidType ? formik.errors.firstAidType : undefined}
                  name="firstAidType"
                />
                {firstAidTypeOptions.length === 0 && <p style={{color: 'red', fontSize: '12px'}}>No First Aid options loaded</p>}
              </div>

              <div className={css.formRow}>
                <label>First Aid Expiration Date: *</label>
                <DateInput
                  selected={formik.values.firstAidExpiration}
                  onChange={(date) => formik.setFieldValue("firstAidExpiration", date)}
                />
                {formik.touched.firstAidExpiration && formik.errors.firstAidExpiration && (
                  <p className={css.error}>{formik.errors.firstAidExpiration}</p>
                )}
              </div>
            </>
          )}

          {/* PATS Certification */}
          <div className={css.formRow}>
            <label>Passenger Assistance Training (PATS / CTAA PASS) Certified? <span className={css.info}>Certifications covering passenger sensitivity, assistance techniques, and wheelchair securement.</span></label>
            <RadioGroup
              name="patsCertified"
              options={["Yes, Current", "Yes, Expired / Need Refresher", "No / Not Formally Certified"]}
              selectedValue={formik.values.patsCertified}
              onChange={(value) => formik.setFieldValue("patsCertified", value)}
            />
            {formik.touched.patsCertified && formik.errors.patsCertified && (
              <p className={css.error}>{formik.errors.patsCertified}</p>
            )}
          </div>

          {(formik.values.patsCertified === "Yes, Current" || formik.values.patsCertified === "Yes, Expired / Need Refresher") && (
            <>
              <div className={css.formRow}>
                <label>PATS/PASS Certification Date (Approx): (Optional)</label>
                <DateInput
                  selected={formik.values.patsDate}
                  onChange={(date) => formik.setFieldValue("patsDate", date)}
                />
              </div>

              <div className={css.formRow}>
                <label>Issuing Agency/Trainer: (Optional)</label>
                <input
                  type="text"
                  name="patsAgency"
                  value={formik.values.patsAgency}
                  onChange={formik.handleChange}
                  className={css.input}
                />
              </div>
            </>
          )}

          {/* MAVT/MAVO Certification */}
          <div className={css.formRow}>
            <label>MAVT / MAVO (Mobility Assistance Vehicle Tech/Operator) Certified? <span className={css.info}>Certification often required for operating wheelchair lift vehicles and proper securement.</span></label>
            <RadioGroup
              name="mavoCertified"
              options={["Yes, Current", "Yes, Expired / Need Refresher", "No / Not Formally Certified"]}
              selectedValue={formik.values.mavoCertified}
              onChange={(value) => formik.setFieldValue("mavoCertified", value)}
            />
            {formik.touched.mavoCertified && formik.errors.mavoCertified && (
              <p className={css.error}>{formik.errors.mavoCertified}</p>
            )}
          </div>

          {(formik.values.mavoCertified === "Yes, Current" || formik.values.mavoCertified === "Yes, Expired / Need Refresher") && (
            <>
              <div className={css.formRow}>
                <label>MAVT/MAVO Certification Date (Approx): (Optional)</label>
                <DateInput
                  selected={formik.values.mavoDate}
                  onChange={(date) => formik.setFieldValue("mavoDate", date)}
                />
              </div>

              <div className={css.formRow}>
                <label>Issuing Agency/Trainer: (Optional)</label>
                <input
                  type="text"
                  name="mavoAgency"
                  value={formik.values.mavoAgency}
                  onChange={formik.handleChange}
                  className={css.input}
                />
              </div>
            </>
          )}

          {/* Defensive Driving */}
          <div className={css.formRow}>
            <label>Defensive Driving Course Completed? (Often required by insurance/employers)</label>
            <RadioGroup
              name="defensiveDriving"
              options={["Yes, within last 3 years", "Yes, longer than 3 years ago", "No"]}
              selectedValue={formik.values.defensiveDriving}
              onChange={(value) => formik.setFieldValue("defensiveDriving", value)}
            />
            {formik.touched.defensiveDriving && formik.errors.defensiveDriving && (
              <p className={css.error}>{formik.errors.defensiveDriving}</p>
            )}
          </div>

          {/* Other Certifications */}
          <div className={css.formRow}>
            <label>Do you hold any other relevant certifications?</label>
            <RadioGroup
              name="holdOtherCertification"
              options={["yes", "no"]}
              selectedValue={formik.values.holdOtherCertification}
              onChange={(value) => formik.setFieldValue("holdOtherCertification", value)}
            />
            {formik.touched.holdOtherCertification && formik.errors.holdOtherCertification && (
              <p className={css.error}>{formik.errors.holdOtherCertification}</p>
            )}
          </div>

          {formik.values.holdOtherCertification === "yes" && (
            <div className={css.formRow}>
              <label>Other Certifications:</label>
              <p className={css.info}>Add any additional relevant certifications (HAZMAT, etc.)</p>
              {formik.values.otherCertifications.map((cert, index) => (
                <div key={index} className={css.certificationBlock}>
                  <div className={css.formRow}>
                    <label>Certificate Name:</label>
                    <input
                      type="text"
                      value={cert.certificateName}
                      onChange={(e) => {
                        const updatedCerts = [...formik.values.otherCertifications];
                        updatedCerts[index].certificateName = e.target.value;
                        formik.setFieldValue("otherCertifications", updatedCerts);
                      }}
                      className={css.input}
                    />
                  </div>
                  <div className={css.formRow}>
                    <label>Issuing Body:</label>
                    <input
                      type="text"
                      value={cert.issuingBody}
                      onChange={(e) => {
                        const updatedCerts = [...formik.values.otherCertifications];
                        updatedCerts[index].issuingBody = e.target.value;
                        formik.setFieldValue("otherCertifications", updatedCerts);
                      }}
                      className={css.input}
                    />
                  </div>
                  <div className={css.formRow}>
                    <label>Date Issued:</label>
                    <DateInput
                      selected={cert.dateIssued}
                      onChange={(date) => {
                        const updatedCerts = [...formik.values.otherCertifications];
                        updatedCerts[index].dateIssued = date;
                        formik.setFieldValue("otherCertifications", updatedCerts);
                      }}
                    />
                  </div>
                  <div className={css.formRow}>
                    <label>Expiration Date:</label>
                    <DateInput
                      selected={cert.expirationDate}
                      onChange={(date) => {
                        const updatedCerts = [...formik.values.otherCertifications];
                        updatedCerts[index].expirationDate = date;
                        formik.setFieldValue("otherCertifications", updatedCerts);
                      }}
                    />
                  </div>
                  {formik.values.otherCertifications.length > 1 && (
                    <button
                      type="button"
                      onClick={() => {
                        const updatedCerts = formik.values.otherCertifications.filter((_, i) => i !== index);
                        formik.setFieldValue("otherCertifications", updatedCerts);
                      }}
                      className={css.removeBtn}
                    >
                      Remove Certification
                    </button>
                  )}
                </div>
              ))}
              <button
                type="button"
                onClick={() => {
                  const newCert = {
                    certificateName: "",
                    issuingBody: "",
                    expirationDate: null,
                    dateIssued: null,
                  };
                  formik.setFieldValue("otherCertifications", [...formik.values.otherCertifications, newCert]);
                }}
                className={css.addBtn}
              >
                Add Another Certification
              </button>
            </div>
          )}
        </div>

        <div className={css.navigationButtons}>
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              className={css.backBtn}
            >
              ← Back (To Step 2: History)
            </button>
          )}
          <div className={css.rightButtons}>
            <button
              type="button"
              onClick={() => handleSubmit(formik.values, false)}
              className={css.saveExitBtn}
              disabled={isLoading}
            >
              Save & Exit (Complete Later)
            </button>
            <button
              type="submit"
              className={css.continueBtn}
              disabled={isLoading}
            >
              Save & Continue (To Step 4: Docs) →
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default NemtMedical;
