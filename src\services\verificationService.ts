
export interface VerifyOtpPayload {
  userId: number;
  otp: number;
}
export const verifyOtp = async (payload: VerifyOtpPayload) => {
  const apiUrl = `${process.env.NEXT_PUBLIC_API_V1}auth/verify-otp`;

  try {
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    });
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Failed to verify OTP:", error);
    throw error;
  }
};


// otp send API 
export const resendOtp = async (userId: number) => {
  const apiUrl = `${process.env.NEXT_PUBLIC_API_V1}auth/resend-otp`;

  try {
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ userId }),
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Failed to resend OTP:", error);
    throw error;
  }
};
