"use client";
import DateInput from "@/components/Common/DateInput/DateInput";
import RadioGroup from "@/components/Common/RadioGroup";
import Dropdown from "@/components/Common/Dropdown";
import {
  fetchDotMedicalCard,
  fetchDriverDetails,
  FormValue,
  submitDriverDetails,
} from "@/services/driverFormService";
import { getStates, State } from "@/services/locationService";
import { useFormik } from "formik";
import React, { useEffect, useState } from "react";
import * as Yup from "yup";
import { useDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";
import css from "./medicalCertifications.module.scss";

interface Certification {
  certificateName: string;
  issuingBody: string;
  expirationDate: Date | null;
  dateIssued: Date | null;
}

interface FormValues {
  dotMedicalCardStatus: string;
  dotExpirationDate: Date | null;
  dotExaminerName: string;
  dotExaminerPhone: string;
  dotNationalRegistryNumber: string;
  dotRestriction: string;
  dotExemption: string;
  dotExaminerState: string;
  holdOtherCertification: string;
  driverOtherCertifications: Certification[];
  hasMedicalVariance: string;
  medicalVarianceDetails: string;
}
 
const MedicalCertification = () => {
  const { updateStepFromApiResponse ,canGoBack,goToPreviousStep } = useDriverCategory();
  const router = useRouter();
  const [dotMedicalOptions, setDotMedicalOptions] = useState<FormValue[]>([]);
  const [states, setStates] = useState<State[]>([]);
 

  useEffect(() => {
    const loadData = async () => {
      const options = await fetchDotMedicalCard();
      const state = await getStates();
      setDotMedicalOptions(options);
      setStates(state);
    };
    loadData();
  }, []);

  //  useEffect(() => {
  //     function handleClickOutside(event: MouseEvent) {
  //       if (
  //         dropdownRef.current &&
  //         !dropdownRef.current.contains(event.target as Node)
  //       ) {
  //         setShowDropdown(false);
  //       }
  //     }
  //     document.addEventListener("mousedown", handleClickOutside);
  //     return () => {
  //       document.removeEventListener("mousedown", handleClickOutside);
  //     };
  //   }, []);
  
  const handleSubmit = async (
    values: FormValues,
    shouldContinue: boolean = true
  ) => {
    const selectedStatus = dotMedicalOptions.find(
      (item) => item.label.en === values.dotMedicalCardStatus
    );
    const selectedState = dotMedicalOptions.find(
      (item) => item.label.en === values.dotExaminerState
    );

    const payload = {
      currentStage: 3,
      currentStep: 3,
      driver: {
        dotMedicalCardStatus: selectedStatus?.formValueId ?? null,
        dotExpirationDate: values.dotExpirationDate?.toISOString(),
        dotExaminerName: values.dotExaminerName,
        dotExaminerPhone: values.dotExaminerPhone,
        dotNationalRegistryNumber: values.dotNationalRegistryNumber,
        dotRestriction: values.dotRestriction,
        dotExemption: values.dotExemption,
        dotExaminerState: selectedState?.formValueId,
        holdOtherCertification: values.holdOtherCertification === "Yes",
        hasMedicalVariance: values.hasMedicalVariance === "Yes",
        ...(values.holdOtherCertification === "Yes" && {
      driverOtherCertifications: values.driverOtherCertifications.map(
        (cert, index) => ({
          certificateName: cert.certificateName,
          issuingBody: cert.issuingBody,
          expirationDate: cert.expirationDate?.toISOString(),
          dateIssued: cert.dateIssued?.toISOString(),
          rank: index + 1,
        })
      ),
    }),
      },
    };

    const response = await submitDriverDetails(payload);
    if (response && response.status) {
      if (shouldContinue) {
        updateStepFromApiResponse(response);
        toast.success("Saved successfully");
        window.scrollTo({ top: 0, behavior: "smooth" });
      } else {
        toast.success("Draft saved successfully! Redirecting to home...");
        setTimeout(() => {
          router.push("/");
        }, 1500);
      }
    } else {
      const errorMessage =
        response?.message ||
        response?.error?.message ||
        "Failed to save medical certifications. Please try again.";
      toast.error(errorMessage);
    }
  };

  const formik = useFormik<FormValues>({
    initialValues: {
      dotMedicalCardStatus: "",
      dotExpirationDate: null,
      dotExaminerName: "",
      dotExaminerPhone: "",
      dotNationalRegistryNumber: "",
      dotRestriction: "",
      dotExemption: "",
      dotExaminerState: "",
      holdOtherCertification: "No",
      driverOtherCertifications: [],
      hasMedicalVariance: "yes",
      medicalVarianceDetails: "",
    },
    validationSchema: Yup.object({
      dotExpirationDate: Yup.date()
        .nullable()
        .required("Expiration date is required"),
      dotExemption: Yup.string().when("hasMedicalVariance", {
        is: "Yes",
        then: (schema) => schema.required("Exemption details required"),
        otherwise: (schema) => schema.notRequired(),
      }),
      dotExaminerState: Yup.string().required("State is required"),
      dotExaminerPhone: Yup.string()
        .matches(/^[0-9]{10,15}$/, "Invalid phone number")
        .notRequired(),
      // dotMedicalCardStatus:Yup.string().required('Medical card status is required')
      hasMedicalVariance: Yup.string().required("Please select Yes or No"),
    }),
    onSubmit: async (values) => {
      const selectedStatus = dotMedicalOptions.find(
        (item) => item.label.en === values.dotMedicalCardStatus
      );
      const selectedState = states.find(
        (state) => state.name.en === values.dotExaminerState
      );

      const payload = {
        currentStage: 3,
        currentStep: 3,
        driver: {
          dotMedicalCardStatus: selectedStatus?.formValueId ?? null,
          dotExpirationDate: values.dotExpirationDate?.toISOString(),
          dotExaminerName: values.dotExaminerName,
          dotExaminerPhone: values.dotExaminerPhone,
          dotNationalRegistryNumber: values.dotNationalRegistryNumber,
          dotRestriction: values.dotRestriction,
          dotExemption: values.dotExemption,
          // dotExaminerState: values.dotExaminerState,
          dotExaminerState: selectedState?.stateId,
          holdOtherCertification: values.holdOtherCertification === "Yes",
          hasMedicalVariance: values.hasMedicalVariance === "Yes",
          driverOtherCertifications: values.driverOtherCertifications.map(
            (cert, index) => ({
              certificateName: cert.certificateName,
              issuingBody: cert.issuingBody,
              expirationDate: cert.expirationDate?.toISOString(),
              dateIssued: cert.dateIssued?.toISOString(),
              rank: index + 1,
            })
          ),
        },
      };

      console.log("Sending payload:", payload);

      try {
        const response = await submitDriverDetails(payload);
        console.log("API Success Response:", response);
        if (response) {
          await handleSubmit(values, true);
        }
      } catch (error: unknown) {
        console.error("Error submitting medical certifications:", error);
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Failed to save medical certifications. Please try again.";
        toast.error(errorMessage);
      }
    },
  });

  // prefill options
  useEffect(() => {
    if (dotMedicalOptions.length > 0 && states.length > 0) {
      populateFormWithExistingData();
    }
  }, [states, dotMedicalOptions]);

  // const populateFormWithExistingData = async () => {
  //   try {
  //     const res = await fetchDriverDetails();
  //     const driver = res?.data?.driver;

  //     if (driver) {
  //       const selectedStatus = dotMedicalOptions.find(
  //         (opt) => opt.formValueId === driver.dotMedicalCardStatus
  //       );

  //       const certifications =
  //         driver.driverOtherCertifications &&
  //         driver.driverOtherCertifications.length > 0
  //           ? driver.driverOtherCertifications.map((cert) => ({
  //               certificateName: cert.certificateName,
  //               issuingBody: cert.issuingBody,
  //               expirationDate: cert.expirationDate
  //                 ? new Date(cert.expirationDate)
  //                 : null,
  //               dateIssued: cert.dateIssued ? new Date(cert.dateIssued) : null,
  //             }))
  //           : [
  //               {
  //                 certificateName: "",
  //                 issuingBody: "",
  //                 expirationDate: null,
  //                 dateIssued: null,
  //               },
  //             ];
  //       const selectedState = states.find(
  //         (state) => state.stateId === driver.dotExaminerState
  //       );
  //       formik.setValues({
  //         dotMedicalCardStatus: selectedStatus?.label.en || "",
  //         dotExpirationDate: driver.dotExpirationDate
  //           ? new Date(driver.dotExpirationDate)
  //           : null,
  //         dotExaminerName: driver.dotExaminerName || "",
  //         dotExaminerPhone: driver.dotExaminerPhone || "",
  //         dotNationalRegistryNumber: driver.dotNationalRegistryNumber || "",
  //         dotRestriction: driver.dotRestriction || "",
  //         dotExemption: driver.dotExemption || "",
  //         dotExaminerState: selectedState?.name.en || "",
  //         holdOtherCertification: driver.holdOtherCertification ? "Yes" : "No",
  //         hasMedicalVariance: driver.holdBusCertification ? "Yes" : "No",
  //         medicalVarianceDetails: driver.medicalVarianceDetails || "",
  //         driverOtherCertifications: certifications,
  //       });
  //     }
  //   } catch (err) {
  //     console.error("Error fetching existing data:", err);
  //   }
  // };
const populateFormWithExistingData = async () => {
  try {
    const res = await fetchDriverDetails();
    const driver = res?.data?.driver;

    if (driver) {
      const selectedStatus = dotMedicalOptions.find(
        (opt) => opt.formValueId === driver.dotMedicalCardStatus
      );

      const selectedState = states.find(
        (state) => state.stateId === driver.dotExaminerState
      );

      
      const certifications =
        driver.holdOtherCertification && 
        Array.isArray(driver.driverOtherCertifications) &&
        driver.driverOtherCertifications.length > 0
          ? driver.driverOtherCertifications.map((cert) => ({
              certificateName: cert.certificateName,
              issuingBody: cert.issuingBody,
              expirationDate: cert.expirationDate
                ? new Date(cert.expirationDate)
                : null,
              dateIssued: cert.dateIssued ? new Date(cert.dateIssued) : null,
            }))
          : [
              {
                certificateName: "",
                issuingBody: "",
                expirationDate: null,
                dateIssued: null,
              },
            ];

      formik.setValues({
        dotMedicalCardStatus: selectedStatus?.label.en || "",
        dotExpirationDate: driver.dotExpirationDate
          ? new Date(driver.dotExpirationDate)
          : null,
        dotExaminerName: driver.dotExaminerName || "",
        dotExaminerPhone: driver.dotExaminerPhone || "",
        dotNationalRegistryNumber: driver.dotNationalRegistryNumber || "",
        dotRestriction: driver.dotRestriction || "",
        dotExemption: driver.dotExemption || "",
        dotExaminerState: selectedState?.name.en || "",
        holdOtherCertification: driver.holdOtherCertification ? "Yes" : "No",
        hasMedicalVariance: driver.holdBusCertification ? "Yes" : "No",
        medicalVarianceDetails: driver.medicalVarianceDetails || "",

        // ✅ only set certifications if holdOtherCertification is true
        driverOtherCertifications: driver.holdOtherCertification
          ? certifications
          : [
              {
                certificateName: "",
                issuingBody: "",
                expirationDate: null,
                dateIssued: null,
              },
            ],
      });
    }
  } catch (err) {
    console.error("Error fetching existing data:", err);
  }
};

  const addCertification = () => {
    formik.setFieldValue("driverOtherCertifications", [
      ...formik.values.driverOtherCertifications,
      {
        certificateName: "",
        issuingBody: "",
        expirationDate: null,
        dateIssued: null,
      },
    ]);
  };

  const removeCertification = (index: number) => {
    const certs = [...formik.values.driverOtherCertifications];
    certs.splice(index, 1);
    formik.setFieldValue("driverOtherCertifications", certs);
  };

  return (
    <form onSubmit={formik.handleSubmit} className={css.medicalInfo}>
      <div>
        <h2>DOT Medical Information</h2>

        <div className={`${css.formRow} ${css.dBlaco}`}>
          <label>
            DOT Medical Card Status
            <span className={css.tooltipIcon}>
              <img src="/images/icons/icon-info.svg" alt="" />
              <span className={css.tooltip}>
                Document filed with your state showing business
                formation/registration.
              </span>
            </span>
          </label>
          {dotMedicalOptions.length > 0 ? (
            <RadioGroup
              name="dotMedicalCardStatus"
              options={dotMedicalOptions.map((item) => item.label.en)}
              selectedValue={formik.values.dotMedicalCardStatus}
              onChange={(val) =>
                formik.setFieldValue("dotMedicalCardStatus", val)
              }
            />
          ) : (
            <p>Loading options...</p>
          )}
          {formik.touched.dotMedicalCardStatus &&
            formik.errors.dotMedicalCardStatus && (
              <span className={css.error}>
                {formik.errors.dotMedicalCardStatus}
              </span>
            )}
        </div>

        <div className={css.formRow}>
          <div className={css.col03}>
            <label htmlFor="dotExpirationDate">
              DOT Medical Card Expiration Date<sup>*</sup>
            </label>
            <DateInput
              name="dotExpirationDate"
              selected={formik.values.dotExpirationDate}
              onChange={(val) => formik.setFieldValue("dotExpirationDate", val)}
            />
            {formik.touched.dotExpirationDate &&
              formik.errors.dotExpirationDate && (
                <span className={css.error}>
                  {formik.errors.dotExpirationDate}
                </span>
              )}
          </div>
          <div className={css.col03}>
            <label htmlFor="dotExaminerName">
              Medical Examiner&apos;s Name{" "}
              <span>- Helpful for verification</span>
            </label>
            <input
              type="text"
              name="dotExaminerName"
              value={formik.values.dotExaminerName}
              onChange={formik.handleChange}
              placeholder="Examiner Name"
            />
          </div>
          <div className={css.col03}>
            <label htmlFor="dotExaminerPhone">
              Medical Examiner&apos;s Phone Number
            </label>
            <input
              type="text"
              name="dotExaminerPhone"
              value={formik.values.dotExaminerPhone}
              onChange={formik.handleChange}
              placeholder="Phone"
              maxLength={10}
            />
            {formik.touched.dotExaminerPhone &&
              formik.errors.dotExaminerPhone && (
                <span className={css.error}>
                  {formik.errors.dotExaminerPhone}
                </span>
              )}
          </div>
          <div className={css.col03}>
            <label htmlFor="dotNationalRegistryNumber">
              National Registry Number of Medical Examiner
            </label>
            <input
              type="text"
              name="dotNationalRegistryNumber"
              value={formik.values.dotNationalRegistryNumber}
              onChange={formik.handleChange}
              placeholder="Registry Number"
            />
          </div>
          <div className={css.col03}>
            <label htmlFor="dotExaminerState">State <sup>*</sup></label>
            <Dropdown
              options={states.map(state => ({ value: state.name.en, label: state.name.en }))}
              value={formik.values.dotExaminerState}
              placeholder="Select State"
              onChange={(value) => formik.setFieldValue("dotExaminerState", value)}
              error={formik.touched.dotExaminerState && formik.errors.dotExaminerState ? formik.errors.dotExaminerState : undefined}
              name="dotExaminerState"
            />
          </div>
        </div>

        <div className={`${css.formRow} ${css.dBlaco}`}>
          <label>
            Do you have a Medical Variance / Exemption Document <sup>*</sup>
          </label>
          <RadioGroup
            name="hasMedicalVariance"
            options={["Yes", "No"]}
            selectedValue={formik.values.hasMedicalVariance}
            onChange={(val) => {
              formik.setFieldValue("hasMedicalVariance", val);
              if (val === "No") {
                formik.setFieldValue("dotExemption", "");
              }
            }}
          />

          {formik.values.hasMedicalVariance === "Yes" && (
            <div className={css.col03}>
              <input
                type="text"
                name="dotExemption"
                placeholder="Enter here"
                value={formik.values.dotExemption}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
              />
              {formik.touched.dotExemption && formik.errors.dotExemption && (
                <span className={css.error}>{formik.errors.dotExemption}</span>
              )}
            </div>
          )}
        </div>
      </div>

      <div className={css.moreInfo}>
        <h2>
          Other Relevant Certifications - Add any job relevant training /
          certificate
        </h2>
        <div className={`${css.formRow} ${css.dBlaco}`}>
          <label>Do you hold any other relevant certifications </label>
          <RadioGroup
            name="holdOtherCertification"
            options={["Yes", "No"]}
            selectedValue={formik.values.holdOtherCertification}
            onChange={(val) =>
              formik.setFieldValue("holdOtherCertification", val)
            }
          />
        </div>

        {formik.values.holdOtherCertification === "Yes" && (
          <div>
            <label> List any additional certifications below</label>
            <button
              type="button"
              onClick={addCertification}
              className={css.addPeriodBtn}
            >
              + Add Certificate
            </button>

            {formik.values.driverOtherCertifications.map((cert, index) => (
              <div className={css.mt28} key={index}>
                <h2>Certification - {index + 1}</h2>
                <div className={css.formRow}>
                  <div className={css.col03}>
                    <label>Certification Name / Type</label>
                    <input
                      type="text"
                      name={`driverOtherCertifications[${index}].certificateName`}
                      placeholder="Certificate Name"
                      value={cert.certificateName}
                      onChange={(e) => {
                        const updated = [
                          ...formik.values.driverOtherCertifications,
                        ];
                        updated[index].certificateName = e.target.value;
                        formik.setFieldValue(
                          "driverOtherCertifications",
                          updated
                        );
                      }}
                    />
                  </div>
                  <div className={css.col03}>
                    <label>Issuing Body / Provider</label>
                    <input
                      type="text"
                      name={`driverOtherCertifications[${index}].issuingBody`}
                      placeholder="Issuing Body"
                      value={cert.issuingBody}
                      onChange={(e) => {
                        const updated = [
                          ...formik.values.driverOtherCertifications,
                        ];
                        updated[index].issuingBody = e.target.value;
                        formik.setFieldValue(
                          "driverOtherCertifications",
                          updated
                        );
                      }}
                    />
                  </div>
                  <div className={css.col03}>
                    <label>Date Issued</label>
                    <DateInput
                      name={`driverOtherCertifications[${index}].dateIssued`}
                      selected={cert.dateIssued}
                      onChange={(val) => {
                        const updated = [
                          ...formik.values.driverOtherCertifications,
                        ];
                        updated[index].dateIssued = val;
                        formik.setFieldValue(
                          "driverOtherCertifications",
                          updated
                        );
                      }}
                    />
                  </div>
                  <div className={css.col03}>
                    <label>Expiration Date</label>
                    <DateInput
                      name={`driverOtherCertifications[${index}].expirationDate`}
                      selected={cert.expirationDate}
                      onChange={(val) => {
                        const updated = [
                          ...formik.values.driverOtherCertifications,
                        ];
                        updated[index].expirationDate = val;
                        formik.setFieldValue(
                          "driverOtherCertifications",
                          updated
                        );
                      }}
                    />
                  </div>
                </div>
                <button
                  type="button"
                  onClick={() => removeCertification(index)}
                  className={css.removeHistory}
                >
                  <img src="/images/icons/icon-close.svg" alt="" />
                  Remove Certification
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
      <div className={css.btnGroup}>
        {canGoBack&&(   
           <button onClick={goToPreviousStep} type="button" className={css.back}>
          <img src="/images/icons/arrow_back.svg" />
          Back
        </button>)}
    
        <button
          type="button"
          onClick={() => handleSubmit(formik.values, false)}
          className={css.exit}
        >
          Save Draft and Exit
        </button>
        <button type="submit" className={css.continue}>
          Save and Continue
        </button>
      </div>
    </form>
  );
};

export default MedicalCertification;
