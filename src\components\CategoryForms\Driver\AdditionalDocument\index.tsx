"use client";
import React, { useState, useEffect } from "react";
import BrowseFiles, { FileItem } from "@/components/Browse/BrowseFiles";
import { useFormik } from "formik";
import * as Yup from "yup";
import { submitDriverDetails, fetchDriverDetails } from "@/services/driverFormService";
import { useDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import css from './additionalDocument.module.scss';

interface DocumentFormValues {
  dotMedicalCardFiles: FileItem[];
  twicCardFiles: FileItem[];
  hazmatAndOthersFiles: FileItem[];
}

const AdditionalDocument = () => {
  const { updateStepFromApiResponse,canGoBack,goToPreviousStep } = useDriverCategory();
  const router = useRouter();
  const [dotMedicalCardFiles, setDotMedicalCardFiles] = useState<FileItem[]>([]);
  const [twicCardFiles, setTwicCardFiles] = useState<FileItem[]>([]);
  const [hazmatAndOthersFiles, setHazmatAndOthersFiles] = useState<FileItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);


  useEffect(() => {
    const loadDriverDetails = async () => {
      try {
        const response = await fetchDriverDetails();
        if (response?.status && response?.data?.driver) {
          const driver = response.data.driver;
   

          if (driver.documents) {
            setDotMedicalCardFiles(driver.documents.dot_medical_card || []);
            setTwicCardFiles(driver.documents.twic_card || []);
            setHazmatAndOthersFiles(driver.documents.hazmat_and_others || []);
          }
        }
      } catch (error) {
        console.error("Error fetching driver details:", error);
        toast.error("Failed to load existing documents");
      } finally {
        setIsLoading(false);
      }
    };

    loadDriverDetails();
  }, []);

  const validationSchema = Yup.object({
    dotMedicalCardFiles: Yup.array()
      .min(1, "DOT Medical Card document is required")
      .required("DOT Medical Card document is required"),
    twicCardFiles: Yup.array()
      .min(1, "TWIC Card document is required")
      .required("TWIC Card document is required"),
    hazmatAndOthersFiles: Yup.array()
      .min(1, "Hazmat endorsement proof is required")
      .required("Hazmat endorsement proof is required"),
  });

  const handleSubmit = async (shouldContinue: boolean = true) => {
    try {
      const payload = {
        currentStage: 3,
        currentStep: 4,
        driver: {
          documents: {
            dot_medical_card: dotMedicalCardFiles,
            twic_card: twicCardFiles,
            hazmat_and_others: hazmatAndOthersFiles,
          },
        },
      };

      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Documents uploaded successfully!");
          window.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to upload documents. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error:", error);
      toast.error("An error occurred while processing documents.");
    }
  };

  const formik = useFormik<DocumentFormValues>({
    initialValues: {
      dotMedicalCardFiles: [],
      twicCardFiles: [],
      hazmatAndOthersFiles: [],
    },
    validationSchema,
    onSubmit: async () => {
      try {
        await handleSubmit(true); 
      } catch (error) {
        console.error("Error submitting documents:", error);
        toast.error("An error occurred while uploading documents.");
      }
    },
  });

  useEffect(() => {
    formik.setFieldValue("dotMedicalCardFiles", dotMedicalCardFiles);
  }, [dotMedicalCardFiles]);

  useEffect(() => {
    formik.setFieldValue("twicCardFiles", twicCardFiles);
  }, [twicCardFiles]);

  useEffect(() => {
    formik.setFieldValue("hazmatAndOthersFiles", hazmatAndOthersFiles);
  }, [hazmatAndOthersFiles]);

  if (isLoading) {
    return <div>Loading existing documents...</div>;
  }

  return (
    <>
      <h2>Medical & Endorsement Documents</h2>
      <form onSubmit={formik.handleSubmit} className={css.additionalDocuments}> 
        <div className={css.note}>
          <h6>Note</h6>
          <p>
            Upload clear images or scans of additional required or relevant documents.
            Your basic ID/License was uploaded in Stage 2. These documents will only be
            shared with employers upon your explicit approval via a &quote;Hiring Packet Request&quote;.
          </p>
        </div>
        <div className={css.formRow}>
          <div className={css.col02}>
            <div className={css.labelDiv}>
              <label>Upload DOT Medical Card and Medical Variance/Exemption Document<sup>*</sup></label>
            </div>
            <BrowseFiles
              label=""
              maxFiles={2}
              onUploadComplete={setDotMedicalCardFiles}
              initialFiles={dotMedicalCardFiles}
            />
            {formik.touched.dotMedicalCardFiles && formik.errors.dotMedicalCardFiles && (
              <p style={{ color: "red", fontSize: "12px", marginTop: "0.5rem" }}>
                {typeof formik.errors.dotMedicalCardFiles === 'string'
                  ? formik.errors.dotMedicalCardFiles
                  : 'DOT Medical Card document is required'}
              </p>
            )}
          </div>
          <div className={css.col02}>
            <div className={css.labelDiv}>
              <label>Upload Scan / Photo of TWIC Card<sup>*</sup></label>
            </div>
            <BrowseFiles
              label=""
              maxFiles={2}
              onUploadComplete={setTwicCardFiles}
              initialFiles={twicCardFiles}
            />
            {formik.touched.twicCardFiles && formik.errors.twicCardFiles && (
              <p style={{ color: "red", fontSize: "12px", marginTop: "0.5rem" }}>
                {typeof formik.errors.twicCardFiles === 'string'
                  ? formik.errors.twicCardFiles
                  : 'TWIC Card document is required'}
              </p>
            )}
          </div>
          <div className={css.col02}>
            <div className={css.labelDiv}>
              <label>Upload Hazmat Endorsement Proof and Any Other Relevant Certifications<sup>*</sup></label>
            </div>
            <BrowseFiles
              label=""
              maxFiles={2}
              onUploadComplete={setHazmatAndOthersFiles}
              initialFiles={hazmatAndOthersFiles}
            />
            {formik.touched.hazmatAndOthersFiles && formik.errors.hazmatAndOthersFiles && (
              <p style={{ color: "red", fontSize: "12px", marginTop: "0.5rem" }}>
                {typeof formik.errors.hazmatAndOthersFiles === 'string'
                  ? formik.errors.hazmatAndOthersFiles
                  : 'Hazmat endorsement proof is required'}
              </p>
            )}
          </div>
        </div>
        <div className={css.btnGroup}>
           {canGoBack&&(   
           <button onClick={goToPreviousStep} type="button" className={css.back}>
          <img src="/images/icons/arrow_back.svg" />
          Back
        </button>)}
          <button
            type="button"
            onClick={() => handleSubmit(false)}
            className={css.exit}
          >
            Save Draft and Exit
          </button>
          <button type="submit" className={css.continue}>Save and Continue</button>
        </div>
      </form>
    </>
  );
};

export default AdditionalDocument;
