"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useNemtDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import {
  fetchNemtDriverFormFields,
  FormValue,
  submitDriverDetails,
  fetchDriverDetails,
} from "@/services/driverFormService";
import CheckboxGroup from "@/components/Common/CheckboxGroup";
import DateInput from "@/components/Common/DateInput/DateInput";
import Dropdown from "@/components/Common/Dropdown";
import css from './nemtConsents.module.scss';

interface ConsentsFormValues {
  availability: number;
  availabilitySpecificDate: Date | null;
  employmentType: number[];
  preferredSchedule: number[];
  employmentArrangement: number;
  areaWillingToCover: string;
  consentShareProfile: boolean;
  consentBackgroundCheck: boolean;
  consentClearinghouse: boolean;
  consentCertifyInfo: boolean;
  consentAcceptTerms: boolean;
}

const NemtConsents: React.FC = () => {
  const { goToPreviousStep, canGoBack } = useNemtDriverCategory();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [employmentTypeOptions, setEmploymentTypeOptions] = useState<FormValue[]>([]);
  const [shiftOptions, setShiftOptions] = useState<FormValue[]>([]);
  const [driverData, setDriverData] = useState<any>(null);
  const [availabilityOptions] = useState([
    { value: 0, label: "Immediately Available" },
    { value: 1, label: "Available within 1 Week" },
    { value: 2, label: "Available within 2 Weeks" },
    { value: 3, label: "Available within 1 Month" },
    { value: 4, label: "Specific Date" },
    { value: 5, label: "Currently Employed/Contracted - Seeking Opportunities" },
  ]);

  const validationSchema = Yup.object({
    availability: Yup.number().required("Availability is required"),
    availabilitySpecificDate: Yup.date().when("availability", {
      is: 2, // Assuming 2 is the value for specific date
      then: (schema) => schema.required("Specific date is required"),
      otherwise: (schema) => schema.nullable(),
    }),
    employmentType: Yup.array()
      .of(Yup.number())
      .min(1, "At least one employment type is required"),
    preferredSchedule: Yup.array().of(Yup.number()),
    employmentArrangement: Yup.number().required("Employment arrangement is required"),
    areaWillingToCover: Yup.string().required("Area willing to cover is required"),
    consentShareProfile: Yup.boolean().oneOf([true], "Profile sharing consent is required"),
    consentBackgroundCheck: Yup.boolean().oneOf([true], "Background check consent is required"),
    consentClearinghouse: Yup.boolean().oneOf([true], "Clearinghouse consent is required"),
    consentCertifyInfo: Yup.boolean().oneOf([true], "Information certification is required"),
    consentAcceptTerms: Yup.boolean().oneOf([true], "Terms acceptance is required"),
  });

  const formik = useFormik<ConsentsFormValues>({
    initialValues: {
      availability: 0,
      availabilitySpecificDate: null,
      employmentType: [],
      preferredSchedule: [],
      employmentArrangement: 0,
      areaWillingToCover: "",
      consentShareProfile: false,
      consentBackgroundCheck: false,
      consentClearinghouse: false,
      consentCertifyInfo: false,
      consentAcceptTerms: false,
    },
    validationSchema,
    onSubmit: async (values) => {
      await handleSubmit(values);
    },
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Load form field options
        const formFields = await fetchNemtDriverFormFields([
          "preferred-employment-types-driver-cdl",
          "preferred-shifts-driver-non-emergency-medical-transportation",
        ]);

        const employmentTypeKey = "preferred-employment-types-driver-cdl";
        if (formFields[employmentTypeKey]) {
          setEmploymentTypeOptions(formFields[employmentTypeKey]);
        }

        const shiftsKey = "preferred-shifts-driver-non-emergency-medical-transportation";
        if (formFields[shiftsKey]) {
          setShiftOptions(formFields[shiftsKey]);
        }

        // Load existing driver data
        const response = await fetchDriverDetails();
        if (response?.status && response?.data?.driver) {
          const driver = response.data.driver;
          setDriverData(driver); // Store driver data for review section

          // Map existing driver data to form values
          const driverData = driver as any;
          formik.setValues({
            availability: driverData.availability || 0,
            availabilitySpecificDate: driverData.availabilitySpecificDate ? new Date(driverData.availabilitySpecificDate) : null,
            employmentType: driverData.employmentType || [],
            preferredSchedule: driverData.preferredSchedule || [],
            employmentArrangement: driverData.employmentArrangement || 0,
            areaWillingToCover: driverData.areaWillingToCover || "",
            consentShareProfile: driverData.consentShareProfile || false,
            consentBackgroundCheck: driverData.consentBackgroundCheck || false,
            consentClearinghouse: driverData.consentClearinghouse || false,
            consentCertifyInfo: driverData.consentCertifyInfo || false,
            consentAcceptTerms: driverData.consentAcceptTerms || false,
          });
        }
      } catch (error) {
        console.error("Error loading data:", error);
        toast.error("Failed to load data. Please refresh.");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const handleSubmit = async (values: ConsentsFormValues) => {
    try {
      setIsLoading(true);

      const payload = {
        currentStage: 3,
        currentStep: 4,
        driver: {
          availability: values.availability,
          availabilitySpecificDate: values.availabilitySpecificDate?.toISOString() || null,
          employmentType: values.employmentType,
          preferredSchedule: values.preferredSchedule,
          employmentArrangement: values.employmentArrangement,
          areaWillingToCover: values.areaWillingToCover,
          consentShareProfile: values.consentShareProfile,
          consentBackgroundCheck: values.consentBackgroundCheck,
          consentClearinghouse: values.consentClearinghouse,
          consentCertifyInfo: values.consentCertifyInfo,
          consentAcceptTerms: values.consentAcceptTerms,
        }
      };

      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        toast.success("NEMT Driver profile completed successfully!");
        // Redirect to home or success page
        setTimeout(() => {
          router.push("/");
        }, 2000);
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to complete profile. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error completing profile:", error);
      toast.error("Failed to complete profile. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Helper functions to format review data
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Not provided";
    return new Date(dateString).toLocaleDateString();
  };

  const getExperienceData = () => {
    const experience = driverData?.nemtExperience;
    if (!experience) {
      return {
        nemtExp: "Data not available",
        drivingExp: "Data not available",
        vehicles: "Data not available",
        passengers: "Data not available",
        skills: "Data not available",
      };
    }

    return {
      nemtExp: experience.nemtExperience || "Not specified",
      drivingExp: experience.totalDrivingExperience || "Not specified",
      vehicles: experience.vehicleTypes?.length ? "Vehicle types selected" : "Not specified",
      passengers: experience.passengerTypes?.length ? "Passenger types selected" : "Not specified",
      skills: experience.assistanceSkills?.length ? "Skills selected" : "Not specified",
    };
  };

  const getMedicalData = () => {
    const medical = driverData?.nemtMedical;
    if (!medical) {
      return {
        dotRequired: "Data not available",
        cpr: "Data not available",
        firstAid: "Data not available",
        pats: "Data not available",
        mavt: "Data not available",
      };
    }

    return {
      dotRequired: medical.dotMedicalRequired || "Not specified",
      cpr: medical.cprCertified === "Yes" ? `Yes, Exp: ${formatDate(medical.cprExpiration)}` : "No",
      firstAid: medical.firstAidCertified === "Yes" ? `Yes, Exp: ${formatDate(medical.firstAidExpiration)}` : "No",
      pats: medical.patsCertified || "Not specified",
      mavt: medical.mavtCertified || "Not specified",
    };
  };

  const getDocumentData = () => {
    const documents = driverData?.nemtDocuments;
    if (!documents) {
      return {
        cprCard: "Data not available",
        firstAidCard: "Data not available",
        patsCert: "Data not available",
        mavtCert: "Data not available",
      };
    }

    return {
      cprCard: documents.cprCertificationFiles?.length ? "✓" : "Not uploaded",
      firstAidCard: documents.firstAidCertificationFiles?.length ? "✓" : "Not uploaded",
      patsCert: documents.patsCertificationFiles?.length ? "✓" : "Not uploaded",
      mavtCert: documents.mavtCertificationFiles?.length ? "✓" : "Not uploaded",
    };
  };

  if (isLoading) {
    return <div className={css.loading}>Loading...</div>;
  }

  return (
    <div className={css.consents}>
      <h3>Step 5: Consents, Availability & Final Review (NEMT Driver)</h3>
      <p>Final step! Review your NEMT Driver profile, agree to consents, and specify your availability.</p>

      <form onSubmit={formik.handleSubmit}>
        {/* Required Consents & Agreements */}
        <div className={css.section}>
          <h4>Required Consents & Agreements</h4>
          <p className={css.requiredNote}>(All checkboxes below are required to complete your profile *)</p>

          <div className={css.consentItem}>
            <CheckboxGroup
              name="consentShareProfile"
              options={[{
                id: 1,
                label: "Profile Sharing Consent: I consent to allow DriverJobz to share my profile information (excluding full sensitive numbers like SSN/License# until Hiring Packet approval) and indicate document upload status with registered NEMT providers/brokers on this platform for employment/contracting consideration. I understand I control full document/detail release via Hiring Packet requests."
              }]}
              selectedValues={formik.values.consentShareProfile ? [1] : []}
              onChange={(selected) => formik.setFieldValue("consentShareProfile", selected.includes(1))}
            />
            {formik.touched.consentShareProfile && formik.errors.consentShareProfile && (
              <p className={css.error}>{formik.errors.consentShareProfile}</p>
            )}
          </div>

          <div className={css.consentItem}>
            <CheckboxGroup
              name="consentBackgroundCheck"
              options={[{
                id: 1,
                label: "Background Check Consent: I understand that potential employers/brokers will conduct background checks as a condition of engagement. This typically includes Motor Vehicle Record (MVR) checks, criminal background checks (often required for transporting vulnerable populations), and potentially drug screening per company/contract policy. I consent to these checks being performed by companies/brokers I connect with or apply to via DriverJobz."
              }]}
              selectedValues={formik.values.consentBackgroundCheck ? [1] : []}
              onChange={(selected) => formik.setFieldValue("consentBackgroundCheck", selected.includes(1))}
            />
            {formik.touched.consentBackgroundCheck && formik.errors.consentBackgroundCheck && (
              <p className={css.error}>{formik.errors.consentBackgroundCheck}</p>
            )}
          </div>

          <div className={css.consentItem}>
            <CheckboxGroup
              name="consentClearinghouse"
              options={[{
                id: 1,
                label: "DOT Clearinghouse Consent: I consent to DOT Drug and Alcohol Clearinghouse queries as required by potential employers for CDL-related positions."
              }]}
              selectedValues={formik.values.consentClearinghouse ? [1] : []}
              onChange={(selected) => formik.setFieldValue("consentClearinghouse", selected.includes(1))}
            />
            {formik.touched.consentClearinghouse && formik.errors.consentClearinghouse && (
              <p className={css.error}>{formik.errors.consentClearinghouse}</p>
            )}
          </div>

          <div className={css.consentItem}>
            <CheckboxGroup
              name="consentCertifyInfo"
              options={[{
                id: 1,
                label: "Certification of Information: I certify that all information provided in this application profile is true, accurate, and complete to the best of my knowledge. I understand that any misrepresentation, falsification, or omission may result in disqualification from opportunities or termination/contract cancellation."
              }]}
              selectedValues={formik.values.consentCertifyInfo ? [1] : []}
              onChange={(selected) => formik.setFieldValue("consentCertifyInfo", selected.includes(1))}
            />
            {formik.touched.consentCertifyInfo && formik.errors.consentCertifyInfo && (
              <p className={css.error}>{formik.errors.consentCertifyInfo}</p>
            )}
          </div>

          <div className={css.consentItem}>
            <CheckboxGroup
              name="consentAcceptTerms"
              options={[{
                id: 1,
                label: "Terms & Privacy Agreement: I acknowledge that I have read and agree to the DriverJobz Terms of Service and Privacy Policy."
              }]}
              selectedValues={formik.values.consentAcceptTerms ? [1] : []}
              onChange={(selected) => formik.setFieldValue("consentAcceptTerms", selected.includes(1))}
            />
            <div style={{ marginTop: '0.5rem', fontSize: '14px' }}>
              <a href="/terms" target="_blank" style={{ color: '#0075F2', marginRight: '1rem' }}>Terms of Service</a>
              <a href="/privacy" target="_blank" style={{ color: '#0075F2' }}>Privacy Policy</a>
            </div>
            {formik.touched.consentAcceptTerms && formik.errors.consentAcceptTerms && (
              <p className={css.error}>{formik.errors.consentAcceptTerms}</p>
            )}
          </div>
        </div>

        {/* Availability Information */}
        <div className={css.section}>
          <h4>Availability Information</h4>

          <div className={css.formRow}>
            <label>Date Available for Work: *</label>
            <Dropdown
              options={availabilityOptions.map(option => ({ value: option.value.toString(), label: option.label }))}
              value={formik.values.availability.toString()}
              placeholder="Select Availability"
              onChange={(value) => formik.setFieldValue("availability", parseInt(value as string))}
              error={formik.touched.availability && formik.errors.availability ? formik.errors.availability : undefined}
              name="availability"
            />
          </div>

          {formik.values.availability === 4 && (
            <div className={css.formRow}>
              <label>Specific Date: *</label>
              <DateInput
                selected={formik.values.availabilitySpecificDate}
                onChange={(date) => formik.setFieldValue("availabilitySpecificDate", date)}
              />
              {formik.touched.availabilitySpecificDate && formik.errors.availabilitySpecificDate && (
                <p className={css.error}>{formik.errors.availabilitySpecificDate}</p>
              )}
            </div>
          )}

          <div className={css.formRow}>
            <label>Preferred Employment Type(s): * (Check all that apply)</label>
            {employmentTypeOptions.length > 0 ? (
              <CheckboxGroup
                name="employmentType"
                options={employmentTypeOptions.map(option => ({
                  id: option.formValueId,
                  label: option.label.en
                }))}
                selectedValues={formik.values.employmentType}
                onChange={(selected) => {
                  formik.setFieldValue('employmentType', selected);
                }}
              />
            ) : (
              <div>Loading employment type options...</div>
            )}
            {formik.touched.employmentType && formik.errors.employmentType && (
              <p className={css.error}>{formik.errors.employmentType}</p>
            )}
          </div>

          <div className={css.formRow}>
            <label>Preferred Schedule(s): (Optional - Check all that apply)</label>
            {shiftOptions.length > 0 ? (
              <CheckboxGroup
                name="preferredSchedule"
                options={shiftOptions.map(option => ({
                  id: option.formValueId,
                  label: option.label.en
                }))}
                selectedValues={formik.values.preferredSchedule}
                onChange={(selected) => {
                  formik.setFieldValue('preferredSchedule', selected);
                }}
              />
            ) : (
              <div>Loading schedule options...</div>
            )}
          </div>

          <div className={css.formRow}>
            <label>Employment Arrangement: *</label>
            <Dropdown
              options={[
                { value: "0", label: "Employee" },
                { value: "1", label: "Independent Contractor" },
                { value: "2", label: "Both" }
              ]}
              value={formik.values.employmentArrangement.toString()}
              placeholder="Select Employment Arrangement"
              onChange={(value) => formik.setFieldValue("employmentArrangement", parseInt(value as string))}
              error={formik.touched.employmentArrangement && formik.errors.employmentArrangement ? formik.errors.employmentArrangement : undefined}
              name="employmentArrangement"
            />
          </div>

          <div className={css.formRow}>
            <label>Area Willing to Cover: *</label>
            <input
              type="text"
              name="areaWillingToCover"
              value={formik.values.areaWillingToCover}
              onChange={formik.handleChange}
              placeholder="e.g., Within 30 miles of Anytown, All of County X"
              className={css.input}
            />
            {formik.touched.areaWillingToCover && formik.errors.areaWillingToCover && (
              <p className={css.error}>{formik.errors.areaWillingToCover}</p>
            )}
          </div>
        </div>

        {/* Final Review */}
        <div className={css.section}>
          <h4>Final Review</h4>
          <p>Please carefully review all the information you've provided below. Use the "Edit Section" links to make corrections.</p>

          <div className={css.reviewSection}>
            <div className={css.reviewItem}>
              <div className={css.reviewLabel}>
                <strong>Stage 2: Essentials</strong>
                <a href="/category/driver" className={css.editLink}>[Edit Stage 2 Info]</a>
              </div>
              <div className={css.reviewValue}>
                <ul>
                  <li>Category: [NEMT Driver]</li>
                  <li>License: [Standard Class C, NY, Exp: MM/DD/YYYY]</li>
                  <li>Endorsements: [N/A] | Restrictions: [None]</li>
                  <li>Safety Summary: Accidents [0], Violations [0], Suspension [No]</li>
                  <li>ID/License Upload: [✓]</li>
                </ul>
              </div>
            </div>

            <div className={css.reviewItem}>
              <div className={css.reviewLabel}>
                <strong>Stage 3 - Step 1: Experience</strong>
                <button
                  type="button"
                  onClick={() => router.push('/category/nemt-driver?step=1')}
                  className={css.editLink}
                >
                  [Edit Experience]
                </button>
              </div>
              <div className={css.reviewValue}>
                {driverData ? (
                  <ul>
                    <li>NEMT Exp: [{getExperienceData().nemtExp}] | Driving Exp: [{getExperienceData().drivingExp}]</li>
                    <li>Vehicles: [{getExperienceData().vehicles}]</li>
                    <li>Passengers: [{getExperienceData().passengers}] | Skills: [{getExperienceData().skills}]</li>
                    <li>Brokers: [Data from experience form]</li>
                  </ul>
                ) : (
                  <p>Loading experience data...</p>
                )}
              </div>
            </div>

            <div className={css.reviewItem}>
              <div className={css.reviewLabel}>
                <strong>Stage 3 - Step 2: History</strong>
                <button
                  type="button"
                  onClick={() => router.push('/category/nemt-driver?step=2')}
                  className={css.editLink}
                >
                  [Edit History]
                </button>
              </div>
              <div className={css.reviewValue}>
                {driverData ? (
                  <ul>
                    <li>Periods Reported: [{driverData.driverEmploymentHistory?.length || 0}] (Covering employment history)</li>
                    <li><em>Employment history data available</em></li>
                  </ul>
                ) : (
                  <p>Loading history data...</p>
                )}
              </div>
            </div>

            <div className={css.reviewItem}>
              <div className={css.reviewLabel}>
                <strong>Stage 3 - Step 3: Medical</strong>
                <button
                  type="button"
                  onClick={() => router.push('/category/nemt-driver?step=3')}
                  className={css.editLink}
                >
                  [Edit Medical]
                </button>
              </div>
              <div className={css.reviewValue}>
                {driverData ? (
                  <ul>
                    <li>DOT Med Required?: [{getMedicalData().dotRequired}]</li>
                    <li>CPR: [{getMedicalData().cpr}] | First Aid: [{getMedicalData().firstAid}]</li>
                    <li>PATS: [{getMedicalData().pats}] | MAVT/MAVO: [{getMedicalData().mavt}]</li>
                  </ul>
                ) : (
                  <p>Loading medical data...</p>
                )}
              </div>
            </div>

            <div className={css.reviewItem}>
              <div className={css.reviewLabel}>
                <strong>Stage 3 - Step 4: Documents</strong>
                <button
                  type="button"
                  onClick={() => router.push('/category/nemt-driver?step=4')}
                  className={css.editLink}
                >
                  [Edit Documents]
                </button>
              </div>
              <div className={css.reviewValue}>
                {driverData ? (
                  <ul>
                    <li>CPR Card: [{getDocumentData().cprCard}] | First Aid Card: [{getDocumentData().firstAidCard}]</li>
                    <li>PATS Cert: [{getDocumentData().patsCert}] | MAVT/MAVO Cert: [{getDocumentData().mavtCert}]</li>
                  </ul>
                ) : (
                  <p>Loading document data...</p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Submit Profile Section */}
        <div className={css.section}>
          <h4>Submit Your Full NEMT Driver Profile</h4>
          <p>By clicking "Complete Profile", you confirm your review, accuracy, and agreement to the consents. This enables 1-Click Apply and allows connected providers/brokers to request your full hiring docket.</p>
        </div>

        <div className={css.navigationButtons}>
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              className={css.backBtn}
            >
              ← Back (To Step 4: Documents)
            </button>
          )}
          <div className={css.rightButtons}>
            <button
              type="submit"
              className={css.completeBtn}
              disabled={isLoading}
            >
              Complete Full Profile & Activate Features
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default NemtConsents;