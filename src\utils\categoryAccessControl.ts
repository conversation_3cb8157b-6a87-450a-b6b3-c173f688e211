import { redirect } from "next/navigation";
import { FetchDriverDetailsResponse } from "@/services/driverFormService";

export const checkCategoryAccess = (
  currentStage: number,
  currentStep: number,
  driverCategory: number,
  requiredCategory: number,
  lang: string,
  categoryName?: string
): void => {
  const hasStageAccess = (currentStage === 2 && currentStep === 7) || (currentStage === 3);
  const hasCategoryAccess = driverCategory === requiredCategory;

  if (!hasStageAccess) {
    const category = categoryName || 'category';
    console.log(`Stage access denied to ${category}. currentStage: ${currentStage}, currentStep: ${currentStep}`);
    redirect(`/${lang}`);
  }

  if (!hasCategoryAccess) {
    redirect(`/${lang}`);
  }
};

export const fetchDriverDetailsServerSide = async (
  token: string
): Promise<FetchDriverDetailsResponse | null> => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_V1}driver/driver-details`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: token,
        },
        cache: 'no-store',
      }
    );

    if (!response.ok) {
      console.error(`HTTP error! status: ${response.status}`);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Server-side API Error:", error);
    return null;
  }
};

export const fetchDriverDetailsAndCheckAccess = async (
  token: string,
  requiredCategory: number,
  lang: string,
  categoryName?: string
) => {
  try {
    const driverData = await fetchDriverDetailsServerSide(token);
    if (!driverData?.status || !driverData?.data) {
      console.error("Invalid driver data response");
      redirect(`/${lang}`);
    }
    const currentStage = driverData.data.currentStage || 1;
    const currentStep = driverData.data.currentStep || 0;
    const driverCategory = driverData.data.driver?.driverCategory || 0;
    checkCategoryAccess(currentStage, currentStep, driverCategory, requiredCategory, lang, categoryName);
    return driverData;
  } catch (error) {
    console.error("Error fetching driver details:", error);
    redirect(`/${lang}`);
  }
};
