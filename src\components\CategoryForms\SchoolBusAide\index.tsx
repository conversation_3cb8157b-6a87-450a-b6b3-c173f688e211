"use client";
import React from "react";
import AideExperience from "./Experience";
import SkillsCertificate from "./skills";
import WorkHistory from "./WorkHistory";
import Documents from "./Documents";
import ConsentReviews from "./Consents";
import { useSchoolBusAideCategory } from "@/contexts/CommonDriverCategoryContext";
import css from "../Driver/driver.module.scss";
import StepIndicator from "@/components/Common/StepIndicator";
 
const SchoolBusAideCategoryForm: React.FC = () => {
      const { currentStep } = useSchoolBusAideCategory();
  const categorySteps = [
    {id: 1, title: "AideExperience"},
    {id: 2, title: "WorkHistory"},
    {id: 3, title: "SkillsCertificate"},
    {id: 4, title: "Documents"},
    {id: 5, title: "ConsentReviews"},
  ];

  const completedSteps: number[] = [];
  for (let i = 1; i < currentStep; i++) {
    completedSteps.push(i);
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <AideExperience />;
      case 2:
        return <WorkHistory />;
      case 3:
        return <SkillsCertificate />;
      case 4:
        return <Documents />;
      case 5:
        return <ConsentReviews />;
      default:
        return <AideExperience />;
    }
  };

  const currentStepData = categorySteps.find(step => step.id === currentStep);

  return (
    <div className={css.driverCategoryForm}>
      <div className={css.container}>
        <StepIndicator categorySteps={categorySteps}
        currentStep={currentStep}
        completedSteps={completedSteps}
        />
        <h6 className={css.required}>
          Required fields are marked with <span>*</span>
        </h6>
        <div className={css.stepContent}>
          <h2>{currentStepData?.title}</h2>
          {renderStepContent()}
        </div>
      </div>
    </div>
  );
};

export default SchoolBusAideCategoryForm;
