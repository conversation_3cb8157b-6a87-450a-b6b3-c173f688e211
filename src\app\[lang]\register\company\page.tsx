

// import { getDictionary } from "../../dictionaries";
import css from "../registerpage.module.scss";
import CompanyRegistration2 from "@/components/Registration/Company2"; 
export default async function Page({
  params,
}: {
  params: Promise<{ lang: "en" | "es" }>;
}) {
  const { lang } = await params;
  // const dict = await getDictionary(lang);

  return (
    <div className={css.pageContainer}>
      <CompanyRegistration2 lang={lang} />
    </div>
  );
}
