import React from 'react';
import css from './button.module.scss';

interface ButtonProps {
  label: string;  
  onClick?: () => void; 
  className?: string;  
  type?: "button" | "submit" | "reset";  
}

const Button: React.FC<ButtonProps> = ({ label, onClick, className, type = "button" }) => {  
  return (
    <button 
      className={`${css.button} ${className}`}  
      onClick={onClick} 
      type={type}  
    >
      {label}
    </button>
  );
};

export default Button;

