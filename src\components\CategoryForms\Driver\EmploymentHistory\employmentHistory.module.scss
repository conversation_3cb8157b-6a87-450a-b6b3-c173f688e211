@import '../../../../styles/global.scss';

.employmentHistory {
    h2 {
        color: $black;
        font-weight: 700;
        font-size: 24px;
        line-height: 32px;
        margin-bottom: 24px;
    }

    .note {
        border-radius: 8px;
        border: 1px solid #0075F2;
        background-color: #EDF6FF;
        padding: 20px;

        h6 {
            color: #0075F2;
            font-weight: 700;
            font-size: 16px;
            line-height: 24px;
            text-transform: uppercase;
        }

        p {
            color: #0075F2;
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            margin-top: 8px;
        }
    }

    .employmentHistoryRow {
        border-bottom: 6px solid #F4F4F4;
        padding-bottom: 40px;
        margin-bottom: 40px;
        position: relative;

        &:last-child {
            border-bottom: none;
            padding-bottom: 0px;
            margin-bottom: 0px;
        }

        .removeHistory {
            background-color: transparent;
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            position: absolute;
            right: 0px;
            top: 0px;
            color: #555555;
            font-weight: 600;
            font-size: 13px;
            line-height: 20px;
            cursor: pointer;
        }
    }

    .formGroup {
        margin-bottom: 28px;
    }

    label {
        display: block;
        color: $black;
        font-weight: 700;
        font-size: 14px;
        line-height: 22px;
        margin-bottom: 16px;

        span {
            font-weight: 400;
        }

        sup {
            color: $red;
            line-height: 0px;
        }

        .tooltipIcon {
            width: 22px;
            height: 22px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin-left: 2px;
            cursor: pointer;

            &:hover {
            .tooltip {
                display: block;
            }
            }
        }

        .tooltip {
            background-color: #1E1E1E;
            border-radius: 4px;
            color: $white;
            font-size: 14px;
            line-height: 24px;
            text-align: center;
            max-width: 330px;
            padding: 8px;
            width: 84vw;
            position: absolute;
            left: 50%;
            bottom: calc(100% + 10px);
            transform: translateX(-50%);
            display: none;

            &:after {
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid #1E1E1E;
            content: "";
            position: absolute;
            left: 50%;
            bottom: -9px;
            transform: translateX(-50%);
            }
        }
    }

    .btnGroup {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-top: 50px;

        button {
            border-radius: 8px;
            border: 1px solid;
            cursor: pointer;
            font-weight: 500;
            font-size: 18px;
            line-height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            height: 50px;

            &.back {
                background-color: $white;
                border-color: #555555;
                color: $black;
                width: 120px;
            }

            &.exit {
                background-color: #555555;
                border-color: #555555;
                color: $white;
                width: 220px;
                margin-left: auto;
            }

            &.continue {
                background-color: $secondary;
                border-color: $secondary;
                color: $black;
                width: 220px;
            }
        }
    }

    .mt28 {
        margin-top: 28px;
    }

    .mb28 {
        margin-bottom: 28px;
    }

    .mt36 {
        margin-top: 36px;
    }

    .addPeriodBtn {
        border: none;
        border-radius: 8px;
        background-color: #555555;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        height: 44px;
        padding: 0px 20px;
        font-size: 14px;
        line-height: 20px;
        color: $white;
        cursor: pointer;
    }

    .formRow {
        display: flex;
        flex-wrap: wrap;
        column-gap: 24px;
        row-gap: 28px;
        margin-bottom: 28px;
        position: relative;

        &:last-child {
            margin-bottom: 0px;
        }

        &.dBlaco {
            display: block;
        }

        .error {
            color: #F91313;
            font-size: 12px;
            line-height: 18px;
            font-weight: 400;
            margin-top: 4px;
        }
    }

    .col03 {
        width: calc((100% - 48px) / 3);
        position: relative;

        &:has( .error) {
            input {
            border-color: #F91313;
            }
        }

        label {
            margin-bottom: 8px;
        }

        @include for-size(tablet-phone) {
            width: 100%;
        }

        div {
            max-width: 100%;
        }
    }

    input[type='text'],
    input[type='email'],
    input[type='password'],
    input[type='tel'],
    input[type='number'],
    input[type='date'],
    .dropdownToggle,
    textarea {
        background-color: #FFFFFF;
        border: 1px solid #707070;
        border-radius: 4px;
        width: 100%;
        height: 44px;
        color: #515B6F;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        text-align: left;
        padding: 10px 8px;
        outline: none !important;

        &:disabled {
            background-color: #F7F7F7;
            font-style: italic;
        }

        &::placeholder {
            color: #707070;
            font-weight: 400;
            opacity: 1;
        }
    }

    textarea {
        height: 88px;
        resize: none;
        max-width: 776px;
    }

    .characterLimit {
        color: $black;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
        letter-spacing: 0%;
        display: block;
        margin-top: 4px;
    }

    .dropdown {
        position: relative;

        .dropdownToggle {
            display: inline-flex;
            align-items: center;
            justify-content: flex-start;
            background-image: url(/images/icons/icon-down-arrow.svg);
            background-repeat: no-repeat;
            background-position: right 12px center;
        }

        .dropdownMenu {
            background-color: #FFFFFF;
            border-radius: 4px;
            -webkit-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            -moz-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            -ms-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            -o-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
            max-height: 180px;
            overflow-x: hidden;
            overflow-y: auto;
            position: absolute;
            width: 100%;
            left: 0px;
            top: calc(100% + 8px);
            z-index: 1;
        }

        &::-webkit-scrollbar {
            width: 6px;
        }

        &::-webkit-scrollbar-track {
            background: #F5F5F5; 
        }

        &::-webkit-scrollbar-thumb {
            background: #929292; 
            border-radius: 24px;
        }
    }

    .dropdownItem {
        background-color: #FFFFFF;
        border: none;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: flex-start;
        font-weight: 500;
        font-size: 15px;
        line-height: 22px;
        height: 44px;
        padding: 4px 12px;
        width: 100%;
    }

    .checkBox {
        position: relative;
        cursor: pointer;
        user-select: none;

        input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 24px;
            width: 24px;
            z-index: 3;

            &:checked {
                ~.checkmark {
                    background-color: #555555;
                    border-color: #555555;

                    &:after {
                        display: block;
                    }
                }
            }
        }

        .checkmark {
            height: 20px;
            width: 20px;
            background-color: #FFFFFF;
            border-radius: 2px;
            border: 2px solid #555555;
            position: absolute;
            left: 0px;
            top: 2px;

            &:after {
                content: "";
                position: absolute;
                display: none;
                left: 5px;
                top: 0px;
                width: 5px;
                height: 10px;
                border: solid $white;
                border-width: 0 1px 1px 0;
                -webkit-transform: rotate(45deg);
                -ms-transform: rotate(45deg);
                transform: rotate(45deg);
            }
        }

        p {
            color: #555555;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            padding-left: 28px;
            margin: 0px;
            text-align: left;

            a {
                color: #0075F2;
            }
        }
    }
}