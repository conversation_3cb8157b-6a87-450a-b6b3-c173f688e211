import DriverFlow from "@/components/DriverFlow";
import css from "../registerpage.module.scss";
import { cookies } from "next/headers";

export const dynamic = 'force-dynamic';
export const revalidate = 0;

export default async function Page({
  params,
}: {
  params: Promise<{ lang: "en" | "es" }>;
}) {
  const { lang } = await params;

  const cookieStore = await cookies();
  const token = cookieStore.get("authToken");

  let userData: {
    userId: string;
    phone: string;
    isPhoneNumberVerified: boolean;
  } | null = null;

  if (token?.value) {
    try {
      const timestamp = new Date().getTime();
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_V1}auth/me?_t=${timestamp}`, {
        method: "GET",
        headers: {
          "Authorization": token.value,
          "Content-Type": "application/json",
        },
      });

      if (res.ok) {
        const data = await res.json();
        if (data?.status && data?.data?.user) {
          const user = data.data.user;
          const isVerified = Boolean(user.isPhoneNumberVerified);
          userData = {
            userId: String(user.userId),
            phone: user.contactNumber,
            isPhoneNumberVerified: isVerified,
          };
        }
      }
    } catch (err) {
      console.error("auth/me fetch failed:", err);
    }
  }

  return (
    <div className={css.pageContainer}>
      <DriverFlow userData={userData} lang={lang} />
    </div>
  );
}
