@import '../../../styles/global.scss';

.radioList {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .radioGroup {
        position: relative;
        padding-left: 32px;
        cursor: pointer;
        user-select: none;
        width: calc((100% - 64px) / 5);

        input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            width: 100%;
            height: 100%;
            left: 0px;

            &:checked {
                ~ .checkmark {
                    &:after {
                        display: block;
                    }
                }
            }
        }

        .checkmark {
            border: 2px solid #555555;
            background-color: $white;
            border-radius: 50%;
            position: absolute;
            top: 0;
            left: 0;
            height: 20px;
            width: 20px;

            &:after {
                background-color: #555555;
                content: "";
                position: absolute;
                display: none;
                top: 3px;
                left: 3px;
                width: 10px;
                height: 10px;
                border-radius: 50%;
            }
        }

        p {
            color: #555555;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
        }
    }
}