"use client";
import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import css from './header.module.scss';
import HeaderSecondaryNav from './HeaderSecondaryNav';
import { usePathname } from 'next/navigation';

const Header: React.FC = () => {
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const dropdownRef = useRef<HTMLLIElement>(null);
    const pathname = usePathname();

    const toggleDropdown = () => {
        setIsDropdownOpen(prev => !prev);
    };

    const handleClickOutside = (event: MouseEvent) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
            setIsDropdownOpen(false);
        }
    };

    useEffect(() => {
        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const getLanguageLink = (lang: 'en' | 'es') => {
        const pathParts = pathname.split('/').filter(Boolean);
        if (pathParts[0] === 'en' || pathParts[0] === 'es') {
            pathParts.shift(); // remove existing lang
        }
        return `/${lang}/${pathParts.join('/')}`;
    };

    return (
        <>
            <header className={css.header}>
                <div className={css.container}>
                    <Link href="/" className={css.logo}>
                        <img src="/images/logo.svg" alt="logo.svg" />
                    </Link>
                    <nav>
                        <ul>
                            <li>
                                <Link href="/">
                                    Find Jobs
                                    <img src='/images/icons/icon-down-arrow.svg' alt='icon-down-arrow.svg'/>
                                </Link>
                            </li>
                            <li>
                                <Link href="/">
                                    Browse Drivers
                                    <img src='/images/icons/icon-down-arrow.svg' alt='icon-down-arrow.svg'/>
                                </Link>
                            </li>
                            <li>
                                <Link href="/">
                                    Resources
                                    <img src='/images/icons/icon-down-arrow.svg' alt='icon-down-arrow.svg'/>
                                </Link>
                            </li>
                            <li 
                                className={`${css.dropdown} ${isDropdownOpen ? css.active : ''}`} 
                                onClick={toggleDropdown} 
                                ref={dropdownRef}
                            >
                                <span className={css.dropdownToggle}>
                                    Language
                                    <img src='/images/icons/icon-down-arrow.svg' alt='icon-down-arrow.svg'/>
                                </span>
                                {isDropdownOpen && (
                                    <ul className={`${css.dropdownMenu} ${css.dropdownMenu01} ${css.language}`}>
                                        <li>
                                            <Link href={getLanguageLink('en')}>
                                                <img src='/images/icons/icon-language.svg' alt='icon-language.svg'/>
                                                English
                                            </Link>
                                        </li>
                                        <li>
                                            <Link href={getLanguageLink('es')}>
                                                <img src='/images/icons/icon-language.svg' alt='icon-language.svg'/>
                                                Spanish
                                            </Link>
                                        </li>
                                    </ul>
                                )}
                            </li>
                        </ul>
                    </nav>
                    <HeaderSecondaryNav />
                </div>
            </header>
            <header className={css.phoneHeader}>
                <button type='button' className={css.menuIcon}>
                    <img src='/images/icons/icon-toggle.svg' alt=''/>
                </button>
                <Link href="/" className={css.logo}>
                    <img src="/images/logo.svg" alt="logo.svg" />
                </Link>
                <button type='button' className={css.menuIcon}>
                    <img src='/images/icons/icon-user.svg' alt='icon-user.svg'/>
                </button>
            </header>
        </>
    );
};

export default Header;
