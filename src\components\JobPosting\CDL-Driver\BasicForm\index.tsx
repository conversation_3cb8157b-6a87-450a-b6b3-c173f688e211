"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import { booleanFlags, otherValue } from "@/utils/constant";
import styles from "../../jobPosting.module.scss";
import TextField from "../../../Common/Form/TextField";
import CheckboxField from "../../../Common/Form/CheckboxField";
import RadioField from "../../../Common/Form/RadioField";
import Textarea from "@/components/Common/Form/Textarea";
import DropdownField from "@/components/Common/Form/DropdownField";
import { BasicFormValues, BasicProps } from "../../../../types/jobpostingform";
import ButtonComponent from "@/components/Common/Form/ButtonComponent";
import { deepCleanValues, getCommonKeys, handleArrayFields, numericInput, safeFetch, scrollToFirstError } from "@/utils/utils";
import DatePickerComponent from "@/components/Common/Form/DatePickerComponent";
import { getJobPosting, jobPostingUpdate } from "@/services/jobPostingService";
import { useRouter } from "next/navigation";
import { getCookie } from "cookies-next";
import OverlayScreen from "@/components/Common/Form/OverlayScreen";
import { basicFormCDL } from "@/initialValues/basicFormValues";
import { getBasicFormSchema } from "@/schemas/basicFormSchema";
import { basicFormPayload } from "@/submitHandler/basicFormSubmit";

const BasicForm = ({ states, formFields, setCurrentStep, companyDetails }: BasicProps) => {
  const [isDraft, setIsDraft] = useState(false);
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const jobIdStr = getCookie("jobId");
  const jobId = jobIdStr ? Number(jobIdStr) : null;

  const formik = useFormik<BasicFormValues>({
    initialValues: basicFormCDL,
    validationSchema: getBasicFormSchema(isDraft),
    onSubmit: async(values: BasicFormValues) => {
      setLoading(true);
      const payload = basicFormPayload(values, isDraft, companyDetails);

      try {
        await jobPostingUpdate(payload, jobId, setCurrentStep, 2, isDraft, router);
      } catch (error) {
        console.error("Failed to fetch", error);
      } finally {
        setLoading(false);
      }
    },
  });

  useEffect(() => {
    if (submitAttempted && Object.keys(formik.errors).length > 0) {
      scrollToFirstError(formik.errors);
      setSubmitAttempted(false);
    }
  }, [formik.errors, submitAttempted]);

  useEffect(() => {
    const fetchData = async () => {
      const result = await safeFetch(() => getJobPosting("", jobId), {});
      
      if(result?.jobPost) {
        const initialKeys = getCommonKeys(basicFormCDL, result.jobPost);
        const cleanedJobPost = deepCleanValues(initialKeys);

        const arrayFields = handleArrayFields(cleanedJobPost, {
          routeType: { outputKey: "specificRoute", addField: "other" },
          serviceArea: { outputKey: "specificState", addField: "other" },
        });

        const newObject = {} as Record<string, unknown>;

        if (typeof cleanedJobPost.jobTitle === 'string' && !isNaN(Number(cleanedJobPost.jobTitle))) {
          newObject.jobTitle = Number(cleanedJobPost.jobTitle);
        } else {
          newObject.otherJob = cleanedJobPost.jobTitle;
          newObject.jobTitle = "other";
        }

        if(cleanedJobPost.customHomeTimeFrequency) {
          newObject.homeTimeFrequency = "other";
        }

        formik.setValues({
          ...formik.values,
          ...cleanedJobPost,
          ...arrayFields,
          ...newObject
        });
      }
    };

    fetchData();
  }, [])

  return (
    <div className={styles.basicFormInfo}>
      {loading && <OverlayScreen />}
      <form onSubmit={formik.handleSubmit}>
        <div>
          <h2 className={styles.heading}>Personal Details</h2>
          <div className={styles.rowField}>
            <DropdownField 
              className = "columnWidth_3"
              label = "Job Title"
              fieldName="jobTitle"
              defaultLabel="Select Job Title"
              formik={formik}
              dropdownArray={[ 
                ...(formFields?.["job-title-job-posting-cdl"] || []), 
                otherValue
              ]}
            />
            {(formik.values.jobTitle === "other") && 
              <TextField
                className="columnWidth_3"
                label="Specify Other Job Title"
                fieldName="otherJob"
                placeholder="Enter custom job title"
                formik={formik}
              />
            }
          </div> 
          <CheckboxField 
            label="Employment Type"
            desc=" - Check all that apply"
            fieldName="employmentTypes"
            formik={formik}
            checkboxArray={formFields?.["employment-type-check-all-that-apply-job-posting-cdl"]}
          />
          <div className={`${styles.rowField} ${styles.marginAdjust}`}>
            <TextField
              className="columnWidth_3"
              styleClass={styles.tooltipLabel}
              label="Number of Identical Openings for This Position Type"
              fieldName="numberOfOpenings"
              formik={formik}
              handleChange={numericInput}
              tooltipMsg="Enter the number of drivers you need for this specific role (e.g., '5' if hiring 5 identical OTR positions). This post represents one job *type*."
            />
          </div>
          <hr className={styles.horizontalLine} />
          <h2 className={styles.heading}>Location, Route & Schedule</h2>
          <div className={styles.rowField}>
            <TextField
              className="columnWidth_3"
              label="Job Location / Terminal / Base"
              fieldName="locationCity"
              placeholder="Enter City"
              formik={formik}
              tooltipMsg="City and State where the driver is based or reports to."
            />
            <DropdownField 
              className = "columnWidth_3"
              label = "State"
              fieldName="stateId"
              defaultLabel="Select State"
              formik={formik}
              dropdownArray={states}
            />
            <TextField
              className="columnWidth_3"
              label="Zip Code"
              fieldName="locationZipCode"
              placeholder="Enter Primary Zip Code"
              formik={formik}
              hide={true}
              maxLength={5}
              handleChange={numericInput}
            />
          </div>
          <CheckboxField 
            label="Route Type"
            fieldName="routeType"
            formik={formik}
            checkboxArray={[ 
              ...(formFields?.["route-type-job-posting-cdl"] || []), 
              { label: "Other (Describe Below)", value: "other" }
            ]}
          />
          {formik.values.routeType.includes("other") &&
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                fieldName="specificRoute"
                placeholder="eg.,"
                formik={formik}
                hide={true}
              />
            </div>
          }
          <CheckboxField 
            label="Primary Lanes / Operating Area"
            fieldName="serviceArea"
            formik={formik}
            checkboxArray={[ 
              ...(formFields?.["primary-lanes-operating-area-job-posting-cdl"] || []), 
              { label: "Specific States (List Below)", value: "other" }
            ]}
          />
          {formik.values.serviceArea.includes("other") &&
            <div className={styles.rowField}>
              <TextField
                className="columnWidth_3"
                fieldName="specificState"
                placeholder="eg.,"
                formik={formik}
                hide={true}
              />
            </div>
          }
          <Textarea
            label = "Describe Specific Lanes / States / Area"
            desc=" (If applicable)"
            fieldName = "specificServiceArea"
            placeholder = "e.g., Runs between Dallas, TX and Atlanta, GA; Primarily states east of I-35; Within 150 miles of Chicago"
            formik={formik}
            hide={true}
          />
          <RadioField
            label="Does drive needs to be close to Location / Pickup / Route?"
            fieldName="hasPickUpLocation"
            formik={formik}
            radioArray={booleanFlags}
            hide={true}
          />
          {formik.values.hasPickUpLocation &&
            <>
              <TextField
                className="columnWidth_3"
                label="Enter Location"
                fieldName="firstPickUpLocation"
                placeholder="Zipcode"
                formik={formik}
                hide={true}
              />
              <RadioField
                styleClass={styles.radioMargin}
                fieldName="pickUpLocationDistance"
                formik={formik}
                radioArray={formFields?.["distance-range-job-posting-cdl"]}
                hide={true}
              />
              <div className={styles.rowField}>
                <TextField
                  className="columnWidth_3"
                  label="Nationwide"
                  fieldName="nationwide"
                  placeholder="NYC Metro Area"
                  formik={formik}
                  hide={true}
                />
                <TextField
                  className="columnWidth_3"
                  label="Statewide"
                  fieldName="statewide"
                  placeholder="Los Angles Metro Area"
                  formik={formik}
                  hide={true}
                />
              </div>
            </>
          }
          <RadioField
            label="Home Time Frequency"
            fieldName="homeTimeFrequency"
            formik={formik}
            radioArray={[ 
              ...(formFields?.["home-time-frequency-job-posting-cdl"] || []), 
              { label: "Custom (Describe Below)", value: "other" }
            ]}
          />
          {formik.values.homeTimeFrequency === "other" &&
            <Textarea
              label = "Describe Custom Home Time"
              fieldName = "customHomeTimeFrequency"
              placeholder = "Enter custom home time details"
              formik={formik}
            />
          }
          <CheckboxField 
            label="Typical Work Schedule"
            fieldName="workScheduleDays"
            formik={formik}
            checkboxArray={formFields?.["typical-work-schedule-job-posting-cdl"]}
          />
          <RadioField
            label="How soon you can join?"
            fieldName="joinPeriod"
            formik={formik}
            radioArray={formFields?.["target-start-date-job-posting-cdl"]}
          />
          {formik.values.joinPeriod === 76 &&
            <DatePickerComponent
              className="columnWidth_3"
              label="Target Joining Date"
              desc=" (MM/DD/YYYY)"
              fieldName="targetStartDate"
              formik={formik}
              hide={true}
            />
          }
          <ButtonComponent
            className="rightAlign"
            backText=""
            draftBtnHandler={() => {
              setIsDraft(true);
              formik.submitForm();
            }}
            saveBtnHandler={() => {
              setIsDraft(false);
              setSubmitAttempted(true);
            }}
          />
        </div>
      </form>
    </div>
  );
};

export default BasicForm;
