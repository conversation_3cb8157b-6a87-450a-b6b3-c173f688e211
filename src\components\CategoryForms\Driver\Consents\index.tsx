"use client";
import CheckboxGroup from "@/components/Common/CheckboxGroup";
import DateInput from "@/components/Common/DateInput/DateInput";
import RadioGroup from "@/components/Common/RadioGroup";
import {
  fetchDriverDetails,
  fetchFormfieldOption,
  FormValue,
  submitDriverDetails,
} from "@/services/driverFormService";
import { useFormik } from "formik";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import * as Yup from "yup";
import { useDriverCategory } from "@/contexts/CommonDriverCategoryContext";// for rendering
import { useRouter, useParams } from "next/navigation";
import css from './consents.module.scss';
import Link from "next/link";

interface formValues {
  availability: string;
  availabilitySpecificDate: Date | null;
  employmentType: number[];
  preferredRouteType: number[];
  willingToRelocate: string;
  employmentArrangement:  string;
  consentShareProfile: boolean;
  consentBackgroundCheck: boolean;
  consentClearinghouse: boolean;
  consentCertifyInfo: boolean;
  consentAcceptTerms: boolean;
}

const Consent = () => {

  const { setCurrentStep, setCompletedSteps, updateStepFromApiResponse, driverData ,canGoBack,goToPreviousStep} = useDriverCategory();
  const router = useRouter();
  const params = useParams();
  const lang = params?.lang as string;

  const handleEditStep = (step: number) => {
    setCurrentStep(step);
    const currentStage = driverData?.currentStage || 2;
    if (currentStage >= 3) {
      const completed: number[] = [];
      for (let i = 1; i < step; i++) {
        completed.push(i);
      }
      setCompletedSteps(completed);
    }
  };
  const [employmentType, setEmploymentType] = useState<FormValue[]>([]);
  const [routeType, setRouteType] = useState<FormValue[]>([]);
  const [availablity, setAvailablity] = useState<FormValue[]>([]);
  const [employmentAgreement, setEmploymentAgreement] = useState<FormValue[]>(
    []
  );

  useEffect(() => {
    const loadData = async () => {
      const fetchEmploymentTypes = await fetchFormfieldOption(
        "preferred-employment-types-driver-cdl"
      );
      const fetchRouteTypes = await fetchFormfieldOption(
        "preferred-route-types-driver-cdl"
      );
      const fetchAvailability = await fetchFormfieldOption(
        "when-are-you-available-to-start-driver-cdl"
      );
      const fetchEmploymentArrangements = await fetchFormfieldOption(
        "preferred-employment-type-driver-cdl"
      );

      setEmploymentType(fetchEmploymentTypes);
      setRouteType(fetchRouteTypes);
      setAvailablity(fetchAvailability);
      setEmploymentAgreement(fetchEmploymentArrangements);
    };
    loadData();
  }, []);

  const relocateOptions = [
    { label: "Yes", value: "yes" },
    { label: "No", value: "no" },
    { label: "Maybe / Negotiable", value: "maybe" },
  ];

  const formik = useFormik<formValues>({
    initialValues: {
      availability: "",
      availabilitySpecificDate: null,
      employmentType: [],
      preferredRouteType: [],
      willingToRelocate: "",
      // employmentArrangement: [],
      employmentArrangement: "",
      consentShareProfile: false,
      consentBackgroundCheck: false,
      consentClearinghouse: false,
      consentCertifyInfo: false,
      consentAcceptTerms: false,
    },
    validationSchema: Yup.object({
      // availability: Yup.string().required("Availability is required"),
      availabilitySpecificDate: Yup.date().when("availability", {
        is: (val: string) => val === "Specific Date",
        then: (schema) =>
          schema.required("Please enter the specific availability date"),
        otherwise: (schema) => schema.nullable(),
      }),
      employmentType: Yup.array()
        .of(Yup.number())
        .min(1, "Select at least one employment type"),
       
      employmentArrangement: Yup.string().required("This field is required"),

      willingToRelocate: Yup.string().required(
        "Relocation selection is required"
      ),
      consentShareProfile: Yup.boolean().oneOf(
        [true],
        "Profile Sharing Consent is required"
      ),
      consentBackgroundCheck: Yup.boolean().oneOf(
        [true],
        "Background Check Consent is required"
      ),
      consentClearinghouse: Yup.boolean().oneOf(
        [true],
        "FMCSA Clearinghouse Consent is required"
      ),
      consentCertifyInfo: Yup.boolean().oneOf(
        [true],
        "Certification of Truthfulness is required"
      ),
      consentAcceptTerms: Yup.boolean().oneOf(
        [true],
        "Terms & Privacy Agreement is required"
      ),
    }),
    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  const handleSubmit = async (
    values: formValues,
    shouldContinue: boolean = true
  ) => {
    const availability =
      availablity.find((item) => item.label.en === values.availability)
        ?.formValueId ?? null;
      const arrangement=
        employmentAgreement.find((item)=> item.label.en===values.employmentArrangement)?.formValueId?? null;

    const payload = {
      currentStage: 3,
      currentStep: 5,
      driver: {
        availability: availability,
        availabilitySpecificDate:
          values.availabilitySpecificDate?.toISOString(),
        employmentType: values.employmentType,
        preferredRouteType: values.preferredRouteType,
        willingToRelocate:
          values.willingToRelocate === "yes"
            ? true
            : values.willingToRelocate === "no"
            ? false
            : null,
        employmentArrangement:arrangement,
        consentShareProfile: values.consentShareProfile,
        consentBackgroundCheck: values.consentBackgroundCheck,
        consentClearinghouse: values.consentClearinghouse,
        consentCertifyInfo: values.consentCertifyInfo,
        consentAcceptTerms: values.consentAcceptTerms,
      },
    };

    const response = await submitDriverDetails(payload);
    if (response && response.status) {
      if (shouldContinue) {
        updateStepFromApiResponse(response);
        toast.success("Profile completed successfully! Redirecting to home...");
        window.scrollTo({ top: 0, behavior: "smooth" });
        setTimeout(() => {
          router.push("/");
        }, 2000);
      } else {
        toast.success("Draft saved successfully! Redirecting to home...");
        setTimeout(() => {
          router.push("/");
        }, 1500);
      }
    } else {
      const errorMessage = response?.message || response?.error?.message || "Failed to complete profile. Please try again.";
      toast.error(errorMessage);
    }
  };

  //prefill
  useEffect(() => {
    const populateFormWithExistingData = async () => {
      try {
        const res = await fetchDriverDetails();
        const driver = res?.data?.driver;

        if (driver) {
          const availabilityOption = availablity.find(
            (opt) => opt.formValueId === driver.availability
          );
 const agreementOptions= employmentAgreement.find(
  (opt)=> opt.formValueId===driver.employmentArrangement
 );
          formik.setValues({
            availability: availabilityOption?.label.en || "",
            availabilitySpecificDate: driver.availabilitySpecificDate
              ? new Date(driver.availabilitySpecificDate)
              : null,
            employmentType: driver.employmentType || [],
            preferredRouteType: driver.preferredRouteType || [],
            willingToRelocate:
              driver.willingToRelocate === true
                ? "yes"
                : driver.willingToRelocate === false
                ? "no"
                : "maybe",
            employmentArrangement: agreementOptions?.label.en ||"",
            consentShareProfile: driver.consentShareProfile || false,
            consentBackgroundCheck: driver.consentBackgroundCheck || false,
            consentClearinghouse: driver.consentClearinghouse || false,
            consentCertifyInfo: driver.consentCertifyInfo || false,
            consentAcceptTerms: driver.consentAcceptTerms || false,
          });
        }
      } catch (err) {
        console.error("Error fetching existing data:", err);
      }
    };

    if (
      availablity.length > 0 &&
      employmentType.length > 0 &&
      routeType.length > 0 &&
      employmentAgreement.length > 0
    ) {
      populateFormWithExistingData();
    }
  }, [availablity, employmentType, routeType, employmentAgreement]);

  return (
    <>
      <form onSubmit={formik.handleSubmit} className={css.consents}>
        <h2>Availabiltiy information</h2>
        <div className={`${css.formRow} ${css.dBlaco}`}>
          <label htmlFor="" className={css.mb16}>Date available for work</label>
          <RadioGroup
            name="availability"
            options={availablity.map((opt) => opt.label.en)}
            selectedValue={formik.values.availability}
            onChange={(val) => formik.setFieldValue("availability", val)}
          />
          {formik.touched.availability && formik.errors.availability && (
            <span className={css.error}>
              {formik.errors.availability}
            </span>
          )}
        </div>
        <div className={css.formRow}>
          <div className={css.col03}>
            <div className={css.labelDiv}>
              <label htmlFor="">Specific Date (MM/DD/YYYY)</label>
            </div>
            <DateInput
              name="availabilitySpecificDate"
              selected={formik.values.availabilitySpecificDate}
              onChange={(val) =>
                formik.setFieldValue("availabilitySpecificDate", val)
              }
            />
            {formik.touched.availabilitySpecificDate &&
              formik.errors.availabilitySpecificDate && (
                <span className={css.error}>
                  {formik.errors.availabilitySpecificDate}
                </span>
              )}
          </div>
        </div>
        <div className={`${css.formRow} ${css.dBlaco}`}>
          <label htmlFor="" className={css.mb16}>Preferred Employment Type - Check all that apply</label>
          <CheckboxGroup
            name="employmentType"
            options={employmentType.map((item) => ({
              label: item.label.en,
              id: item.formValueId,
            }))}
            selectedValues={formik.values.employmentType}
            onChange={(val) => formik.setFieldValue("employmentType", val)}
          />
          {formik.touched.employmentType && formik.errors.employmentType && (
            <span className={css.error}>
              {formik.errors.employmentType}
            </span>
          )}
        </div>

        <div className={`${css.formRow} ${css.dBlaco}`}>
          <label htmlFor="" className={css.mb16}>Preferred Route Type(s) - Check all that apply</label>
          <CheckboxGroup
            name="preferredRouteType"
            options={routeType.map((item) => ({
              label: item.label.en,
              id: item.formValueId,
            }))}
            selectedValues={formik.values.preferredRouteType}
            onChange={(val) => formik.setFieldValue("preferredRouteType", val)}
          />
          {formik.touched.preferredRouteType &&
            formik.errors.preferredRouteType && (
              <span className={css.error}>
                {formik.errors.preferredRouteType}
              </span>
            )}
        </div>

        <div className={`${css.formRow} ${css.dBlaco}`}>
          <label htmlFor="" className={css.mb16}>Willing to relocate</label>
          <ul className={css.radioList}>
            {relocateOptions.map((options) => (
              <li key={options.value} className={css.radioGroup}>
                <input
                  type="radio"
                  name="willingToRelocate"
                  value={options.value}
                  checked={formik.values.willingToRelocate === options.value}
                  onChange={() =>
                    formik.setFieldValue("willingToRelocate", options.value)
                  }
                />
                <span className={css.checkmark}></span>
                <p>{options.label}</p>
              </li>
            ))}
          </ul>
          {formik.touched.willingToRelocate &&
            formik.errors.willingToRelocate && (
              <span className={css.error}>
                {formik.errors.willingToRelocate}
              </span>
            )}
        </div>

        <div className={`${css.formRow} ${css.dBlaco}`}>
          <label htmlFor="" className={css.mb16}>What type of employment arrangement are you seeking?</label>
          <RadioGroup
            name="employmentArrangement"
            options={employmentAgreement.map((opt) => opt.label.en)}
            selectedValue={formik.values.employmentArrangement}
            onChange={(val) => formik.setFieldValue("employmentArrangement", val)}
          />
          {formik.touched.employmentArrangement &&
            formik.errors.employmentArrangement && (
              <span className={css.error}>
                {formik.errors.employmentArrangement}
              </span>
          )}
        </div>


        <div className={`${css.formRow} ${css.dBlaco} ${css.borderTop}`}>
          <h3>Required Consents & Agreements</h3>
          <label htmlFor="" className={css.mb16}>All checkboxes below are required to complete your profile</label>
          <div className={css.checkBox}>
            <input
              type="checkbox"
              name="consentShareProfile"
              checked={formik.values.consentShareProfile}
              onChange={(e) =>
                formik.setFieldValue("consentShareProfile", e.target.checked)
              }
            />
            <span className={css.checkmark}></span>
            <div className={css.text}>
              <h6>Profile Sharing Consent</h6>
              <p>I consent to allow DriverJobz to share my profile information (excluding full sensitive numbers like SSN/License# until Hiring Packet approval) and indicate document upload status with registered employers on this platform for employment consideration. I understand I control full document/detail release via Hiring Packet requests</p>
            </div>
            {formik.touched.consentShareProfile &&
              formik.errors.consentShareProfile && (
                <span className={css.error}>
                  {formik.errors.consentShareProfile}
                </span>
              )}
          </div>

          <div className={css.checkBox}>
              <input
                type="checkbox"
                name="consentBackgroundCheck"
                checked={formik.values.consentBackgroundCheck}
                onChange={(e) =>
                  formik.setFieldValue(
                    "consentBackgroundCheck",
                    e.target.checked
                  )
                }
              />
            <span className={css.checkmark}></span>
            <div className={css.text}>
              <h6>
                Background Check Consent
                <span className={css.tooltipIcon}>
                  <img src="/images/icons/icon-info.svg" alt="" />
                  <span className={css.tooltip}>
                    A verification link will be sent to this email. Please use a valid address you can access.
                  </span>
                </span>
              </h6>
              <p>I understand that potential employers will conduct background checks as a condition of hire. This includes DOT-required checks such as Safety Performance History inquiries (PSP Report, per 49 CFR Part 391), queries of the FMCSA Drug & Alcohol Clearinghouse, and Motor Vehicle Record (MVR) checks. I consent to these checks being performed by employers I connect with or apply to via DriverJobz.</p>
            </div>
            {formik.touched.consentBackgroundCheck &&
              formik.errors.consentBackgroundCheck && (
                <span className={css.error}>
                  {formik.errors.consentBackgroundCheck}
                </span>
              )}
          </div>

          <div className={css.checkBox}>
              <input
                type="checkbox"
                name="consentClearinghouse"
                checked={formik.values.consentClearinghouse}
                onChange={(e) =>
                  formik.setFieldValue("consentClearinghouse", e.target.checked)
                }
              />
            <span className={css.checkmark}></span>
            <div className={css.text}>
              <h6>
                FMCSA Clearinghouse Consent
                <span className={css.tooltipIcon}>
                  <img src="/images/icons/icon-info.svg" alt="" />
                  <span className={css.tooltip}>
                    A verification link will be sent to this email. Please use a valid address you can access.
                  </span>
                </span>
              </h6>
              <p>As a CDL holder subject to FMCSA Drug & Alcohol Testing regulations, I provide specific consent for prospective and current employers registered in the Clearinghouse to conduct limited queries of the FMCSA Drug & Alcohol Clearinghouse regarding my record as part of the hiring process. I understand I will be asked separately by the employer to provide specific consent within the Clearinghouse portal for any required full queries.</p>
            </div>
            {formik.touched.consentClearinghouse &&
              formik.errors.consentClearinghouse && (
                <span className={css.error}>
                  {formik.errors.consentClearinghouse}
                </span>
              )}
          </div>

          <div className={css.checkBox}>
              <input
                type="checkbox"
                name="consentCertifyInfo"
                checked={formik.values.consentCertifyInfo}
                onChange={(e) =>
                  formik.setFieldValue("consentCertifyInfo", e.target.checked)
                }
              />
            <span className={css.checkmark}></span>
            <div className={css.text}>
              <h6>Certification of Truthfulness</h6>
              <p>I certify that all information provided in this application profile is true, accurate, and complete to the best of my knowledge. I understand that any misrepresentation, falsification, or omission of information may result in disqualification from employment opportunities or termination if hired.</p>
            </div>
            {formik.touched.consentCertifyInfo &&
              formik.errors.consentCertifyInfo && (
                <span className={css.error}>
                  {formik.errors.consentCertifyInfo}
                </span>
              )}
          </div>

          <div className={css.checkBox}>
              <input
                type="checkbox"
                name="consentAcceptTerms"
                checked={formik.values.consentAcceptTerms}
                onChange={(e) =>
                  formik.setFieldValue("consentAcceptTerms", e.target.checked)
                }
              />
              <span className={css.checkmark}></span>
              <div className={css.text}>
                <h6>Terms & Privacy Agreement</h6>
                <p>I acknowledge that I have read and agree to the DriverJobz <Link href="/">Terms of Service</Link> and <Link href="/">Privacy Policy</Link>.</p>
              </div>
              
            {formik.touched.consentAcceptTerms &&
              formik.errors.consentAcceptTerms && (
                <span className={css.error}>
                  {formik.errors.consentAcceptTerms}
                </span>
              )}
          </div>
        </div>

        <div className={css.btnGroup}>
             {canGoBack&&(   
           <button onClick={goToPreviousStep} type="button" className={css.back}>
          <img src="/images/icons/arrow_back.svg" />
          Back
        </button>)}
          <button
            type="button"
            onClick={() => handleSubmit(formik.values, false)}
            className={css.exit}
          >
            Save Draft and Exit
          </button>
          <button type="submit" className={css.continue}>Complete Profile & Activate Features</button>
        </div>
      </form>

      <div className={css.reviewDetails}>
          <h2>Final Review</h2>
          <div className={css.note}>
            <h6>Note</h6>
            <p>Please carefully review all the information you&apos;ve provided below. If anything needs correction, use the &quot;Edit Section&quot; links to go back.</p>
          </div>
          <div className={css.flexBox}>
            <div className={css.card}>
              <div className={css.cardHeading}>
                <h3 style={{ margin: 0 }}>Stage 2 - Essentials</h3>
                <button
                  onClick={() => router.push(`/${lang}/profile/driver?step=1`)}
                >
                  <img src="/images/icons/icon-edit.svg" alt="icon-edit.svg" />
                  Edit
                </button>
              </div>

              <ul className={css.content}>
                <li>
                  <strong>Category</strong>
                  <span>CDL-A / CDL-B</span>
                </li>
                <li>
                  <strong>License Issuing State</strong>
                  <span>CA</span>
                </li>
                <li>
                  <strong>License Expiration</strong>
                  <span>12 / 05 / 20235</span>
                </li>
                <li>
                  <strong>CDL Endorsements</strong>
                  <span>N - Tank Vehicle, T - Double/Triple Trailers</span>
                </li>
                <li>
                  <strong>License Restrictions</strong>
                  <span>None / No Restrictions</span>
                </li>
                <li>
                  <strong>Safety Summary</strong>
                  <span>Accidents [0], Violations [1], Suspension [No]</span>
                </li>
                <li>
                  <strong>ID/License Upload</strong>
                  <span>Documents uploaded successfully</span>
                </li>
              </ul>
            </div>

            {/* Stage 3 - Experience */}
            <div className={css.card}>
              <div className={css.cardHeading}>
                <h3>Stage 3 - Experience</h3>
                {/* <button type="button" onClick={() =>{setCurrentStep(1);
                  router.push("/category/driver ")}}>
                  <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
                </button> */}
                <button onClick={() => handleEditStep(1)}>
                  <img src="/images/icons/icon-edit.svg" alt="icon-edit.svg" />
                  Edit
                </button>
              </div>

              <ul className={css.content}>
                <li>
                  <strong>CDL Exp</strong>
                  <span>5 Years</span>
                </li>
                <li>
                  <strong>Miles</strong>
                  <span>500k-750k</span>
                </li>
                <li>
                  <strong>Equipment</strong>
                  <span>Dry Van, Reefer, Tanker(Liquid Non-Haz)</span>
                </li>
                <li>
                  <strong>Transmission</strong>
                  <span>Manual(10), Auto Manual</span>
                </li>
                <li>
                  <strong>Routes</strong>
                  <span>Regional, OTR</span>
                </li>
              </ul>
            </div>

            {/* Stage 3 - History */}
            <div className={css.card}>
              <div className={css.cardHeading}>
                <h3>Stage 3 - History</h3>
                {/* <button type="button" onClick={() =>{setCurrentStep(2);router.push("/category/driver ")}}>
                  <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
                </button> */}
                <button onClick={() => handleEditStep(2)}>
                  <img src="/images/icons/icon-edit.svg" alt="icon-edit.svg" />
                  Edit
                </button>
              </div>

              <ul className={`${css.content} ${css.flexColumn}`}>
                <li>
                  <strong>Periods Reported</strong>
                </li>
                <li>
                  <h6>Period  - 1</h6>
                  <span>RMP Trucking LLC - May 2024 - Present</span>
                  <p>Contrary to popular belief, Lorem Ipsum is not simply random text. It has roots in a piece...</p>
                </li>
                <li>
                  <h6>Period  - 2</h6>
                  <span>RMP Trucking LLC - Jan 2020 - Apr 2024</span>
                  <p>Richard McClintock, a Latin professor at Hampden-Sydney College in Virginia...</p>
                </li>
                <li>
                  <h6>Period  - 3</h6>
                  <span>RMP Trucking LLC - Jan 2017 - Dec 2019</span>
                  <p>The standard chunk of Lorem Ipsum used since the reproduced below interested...</p>
                </li>
              </ul>
            </div>

            {/* Stage 3 - Medical & Documents */}
            <div className={css.card}>
              <div className={css.cardHeading}>
                <h3>Stage 3 - Medical & Documents</h3>
                {/* <button type="button" onClick={() =>{setCurrentStep(3); router.push("/category/driver ")}}>
                  <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
                </button> */}
                <button onClick={() => handleEditStep(3)}>
                  <img src="/images/icons/icon-edit.svg" alt="icon-edit.svg" />
                  Edit
                </button>
              </div>

              <ul className={css.content}>
                <li>
                  <strong>DOT Med Status</strong>
                  <span>Current / Valid</span>
                </li>
                <li>
                  <strong>Miles</strong>
                  <span>1 Jan 2028</span>
                </li>
                <li>
                  <strong>Med Card</strong>
                  <span>Document uploaded successfully</span>
                </li>
                <li>
                  <strong>TWIC</strong>
                  <span>Document uploaded successfully</span>
                </li>
                <li>
                  <strong>Other Certs</strong>
                  <span>None</span>
                </li>
              </ul>
            </div>
          </div>
      </div>

    </>
  );
};

export default Consent;
