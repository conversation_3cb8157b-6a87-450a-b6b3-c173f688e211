.btnContainer {
    display: flex;
    justify-content: space-between;

    .backBtn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 120px;
        height: 50px;
        padding: 12px;
        border-radius: 8px;
        font-size: 18px;
        font-weight: 500;
        margin-top: 50px;
        border: 1px solid #555555;
        background-color: #FFFFFF;
        gap: 12px;
        cursor: pointer;
    }
}

.rightAlign {
    justify-content: right;
}

.btnWrapper {
    display: flex;
    column-gap: 20px;
    margin-top: 50px;

    .draftBtn,
    .saveBtn {
        width: 220px;
        height: 50px;
        padding: 12px;
        border-radius: 8px;
        font-size: 18px;
        font-weight: 500;
        cursor: pointer;
    }

    .draftBtn {
        border: 1px solid #555555;
        background-color: #555555;
        color: #FFFFFF;
    }

    .saveBtn {
        border: 1px solid #FBD758;
        background-color: #FBD758;
    }
}

.btnContainer.jobCategoryClass {
    flex-direction: column;

    .btnWrapper {
        margin-top: 36px;

        .saveBtn {
            width: 100%;
        }
    }

    .backBtnLink {
        width: 100%;
        margin-top: 20px;
        text-align: center;
        font-size: 18px;
        font-weight: 400;

        .linkClass {
            font-weight: 500;
        }
    }
}