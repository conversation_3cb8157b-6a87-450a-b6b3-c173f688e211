import { DrivingHistoryEntry, QualificationForm } from "@/types/jobpostingform";
import { driverRecords } from "@/utils/constant";
import * as Yup from "yup";

export const getQualifyFormSchema = (isDraft: boolean, proficiencyValues: (string)[], languageValues: (string | number)[]) => {
  if (isDraft) return null;

  return Yup.object({
    cdlClass: Yup.string().required("Please select CDL class"),
    dotMedicalCard: Yup.string().required(
      "Please select medical card required or not"
    ),
    twicCardRequired: Yup.string().required(
      "Please select twic required or not"
    ),
    passportRequired: Yup.string().required(
      "Please select password required or not"
    ),
    experienceMonths: Yup.string().required("Please select CDL experience"),
    screeningChecks: Yup.array()
      .of(Yup.mixed<string | number>())
      .min(1, "Please select at least one screening requirement")
      .required("Please select at least one screening requirement"),
    physicalReq: Yup.array()
      .of(Yup.mixed<string | number>())
      .test(
        "custom-physical-requirements-checks",
        "Please enter valid physical requirement values",
        function () {
          const {
            physicalRequirements,
            physicalLiftingLimit,
            otherPhysicalRequirements,
          } = this.parent;

          if (!physicalRequirements || physicalRequirements.length === 0) {
            return this.createError({
              path: "physicalReq",
              message: "Please select at least one physical requirement",
            });
          }

          const hasOther = physicalRequirements.includes("other");
          const hasRadioBtn = physicalRequirements.includes("radioBtn");
          const hasValidLifting =
            physicalLiftingLimit !== "" && !isNaN(physicalLiftingLimit);

          if (
            hasOther &&
            (!otherPhysicalRequirements ||
              otherPhysicalRequirements.trim() === "")
          ) {
            return this.createError({
              path: "physicalReq",
              message: "Please enter value",
            });
          }

          if (
            (hasRadioBtn && !hasValidLifting) ||
            (!hasRadioBtn && hasValidLifting)
          ) {
            return this.createError({
              path: "physicalReq",
              message: "Please select both checkbox and radio option",
            });
          }

          return true;
        }
      ),
    minAge: Yup.string().required("Please select age requirement"),
    drivingReq: Yup.object()
      .test(
        "at-least-one-selected",
        "Please select at least one driving history option.",
        function () {
          const values = this.parent as QualificationForm;

          const hasOneSelected = driverRecords.some(({ key }) => {
            const entry = values[key] as DrivingHistoryEntry | undefined;
            return entry?.allowed === true;
          });

          const hasOtherFilled = !!values?.drivingRecordOther?.trim?.();

          return hasOneSelected || hasOtherFilled;
        }
      )
      .test(
        "count-and-years-required",
        "Please select all count and year fields where 'Yes' is selected.",
        function () {
          const values = this.parent as QualificationForm;

          for (const { key } of driverRecords) {
            const entry = values[key] as DrivingHistoryEntry | undefined;

            if (entry?.allowed === true) {
              const isCountValid =
                entry.count !== undefined && entry.count !== "";
              const isYearsValid =
                entry.years !== undefined && entry.years !== "";

              if (!isCountValid || !isYearsValid) {
                return false;
              }
            }
          }

          return true;
        }
      ),
    driverLanguages: Yup.array()
      .of(
        Yup.object({
          languageId: Yup.mixed<string | number>()
            .oneOf(languageValues)
            .nullable(),
          proficiency: Yup.string().oneOf(proficiencyValues).nullable(),
        })
      )
      .test(
        "language-proficiency-pairing",
        "Please select all required language and proficiency combinations correctly.",
        function (languageArray) {
          if (!languageArray || !Array.isArray(languageArray)) return true;

          const isValid = languageArray.every((entry) => {
            const hasLang = !!entry.languageId;
            const hasProf = !!entry.proficiency;

            return (!hasLang && !hasProf) || (hasLang && hasProf);
          });

          return isValid;
        }
      ),
    otherRequirementsText: Yup.string().when(
      ["isOtherRequirements", "otherRequirements"],
      ([reqs, otherReq], schema) => {
        if (reqs && otherReq.includes("other")) {
          return schema.required("Please enter value");
        }

        return schema.notRequired();
      }
    ),
  });
};
