import CheckBox from "../CheckBox";
import Dropdown from "../Dropdown";
import { FormField as FormFieldType } from "@/types/form";
import InputText from "../InputText";
import { FormikProps } from "formik";

interface FormFieldProps {
  data: FormFieldType;
  formik: FormikProps<Record<string, string | number | boolean>>;
}

const FormField:React.FC<FormFieldProps> = ({ data, formik }) => {
  const { componentType } = data;

  const renderFormItem = (type: FormFieldType["componentType"]) => {
    switch (type) {
      case "text":
        return <InputText formik={formik} data={data} />;
      case "checkbox":
        return <CheckBox formik={formik} data={data} />;
      case "dropdown":
        return <Dropdown formik={formik} data={data} />;
      default:
        return null;
    }
  };

  return <>{renderFormItem(componentType)}</>;
};

export default FormField;
