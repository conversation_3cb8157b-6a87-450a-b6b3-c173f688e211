'use client'
import React,{useState, useEffect} from 'react';
import css from './checkboxGroup.module.scss';
const CheckboxGroup = ({
  options,
  selectedValues,
  onChange,
  otherLabel,
  otherInputLabel,
  customClass,
  otherValue,
}: {
  name: string;
  options: { label: string; id: number }[];
  selectedValues: number[];
  onChange: (selected: number[], otherValue?: string) => void;
  otherLabel?: string;
  otherInputLabel?: string;
  customClass?: string;
  otherValue?: string;
}) => {
  const customClassMapping: Record<string, string> = {
    operatingArea: css.operatingArea,
  };
  const containerClasses = customClass ? (customClassMapping[customClass] || '') : '';


  const [otherChecked, setOtherChecked] = useState(false);
  const [otherText, setOtherText] = useState('');

  // Initialize other checkbox and text from props
  useEffect(() => {
    if (otherValue && otherValue.trim()) {
      setOtherChecked(true);
      setOtherText(otherValue.trim());
    } else {
      setOtherChecked(false);
      setOtherText('');
    }
  }, [otherValue]);

  const handleCheckboxChange = (id: number) => {
    const updated = selectedValues.includes(id)
      ? selectedValues.filter((val) => val !== id)
      : [...selectedValues, id];

    onChange(updated, otherChecked ? otherText : undefined);
  };

  const handleOtherCheckboxChange = () => {
    const checked = !otherChecked;
    setOtherChecked(checked);
    if (!checked) setOtherText('');
    onChange(selectedValues, checked ? otherText : undefined);
  };

  const handleOtherTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const text = e.target.value;
    setOtherText(text);
    onChange(selectedValues, otherChecked ? text : undefined);
  };

  return (
    <div className={`${css.checkBoxGroup} ${containerClasses}`}>
      {options.map((option) => (
        <div key={option.id} className={css.checkBox}>
          <input
            type="checkbox"
            checked={selectedValues.includes(option.id)}
            onChange={() => handleCheckboxChange(option.id)}
          />
          <span className={css.checkmark}></span>
          <p>{option.label}</p>  
        </div>
      ))}

      {/* Only show "Other" if label is passed */}
      {otherLabel && (
        <div>
          <label>
            <input
              type="checkbox"
              checked={otherChecked}
              onChange={handleOtherCheckboxChange}
            />
            {otherLabel}
          </label>

          {otherChecked && (
            <div style={{ marginLeft: '1rem', marginTop: '0.5rem' }}>
              <label>
                {otherInputLabel || 'Other'}
                <input
                  type="text"
                  value={otherText}
                  onChange={handleOtherTextChange}
                  style={{ display: 'block', width: '100%' }}
                />
              </label>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CheckboxGroup;
