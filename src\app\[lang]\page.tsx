import Home from '@/components/Home'
 
export default async function Page() {
  return (
    <div>
      <Home/>
    </div> 
    )
}


// for rotate token test  
// "use client";

// import React, { useEffect, useState } from "react";
// import { fetchWithAuth } from "@/utils/fetchWithAuth";

// interface User {
//   id: string;
//   email: string;
//   name: string;
// }

// export default function Profile() {
//   const [profile, setProfile] = useState<User | null>(null);

//   useEffect(() => {
//     async function getProfile() {
//       try {
//         const res = await fetchWithAuth(`${process.env.NEXT_PUBLIC_API_V1}auth/me`);
//         if (!res.ok) {
//           throw new Error("Failed to fetch profile");
//         }

//         const data = await res.json();
//         setProfile(data?.data?.user);
//       } catch (error) {
//         console.error(error);
//       }
//     }

//     getProfile();
//   }, []);

//   if (!profile) return <div>Loading...</div>;

//   return (
//     <div>
//       Welcome, {profile.email}
//     </div>
//   );
// }
