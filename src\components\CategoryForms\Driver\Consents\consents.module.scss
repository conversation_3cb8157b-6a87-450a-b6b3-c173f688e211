@import '../../../../styles/global.scss';

.consents {
    h3 {
        color: #555555;
        font-weight: 700;
        font-size: 24px;
        line-height: 30px;
        margin-bottom: 16px;
    }

    label {
        color: $black;
        font-weight: 700;
        font-size: 14px;
        line-height: 22px;
        display: inline-flex;
        align-items: center;
        

        &.mb16 {
            margin-bottom: 16px;
        }


        span {
            font-weight: 400;

            @include for-size(tablet-phone) {
                font-size: 12px;
            }
        }

        sup {
            color: $red;
            line-height: 0px;
        }

        .tooltipIcon {
            width: 22px;
            height: 22px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: relative;
            margin-left: 2px;
            cursor: pointer;

            &:hover {
            .tooltip {
                display: block;
            }
            }
        }

        .tooltip {
            background-color: #1E1E1E;
            border-radius: 4px;
            color: $white;
            font-size: 14px;
            line-height: 24px;
            text-align: center;
            max-width: 330px;
            padding: 8px;
            width: 84vw;
            position: absolute;
            left: 50%;
            bottom: calc(100% + 10px);
            transform: translateX(-50%);
            display: none;

            &:after {
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-top: 10px solid #1E1E1E;
            content: "";
            position: absolute;
            left: 50%;
            bottom: -9px;
            transform: translateX(-50%);
            }
        }
    }

    .labelDiv {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        label {
            margin-bottom: 0px;
        }

        &.mb8 {
            margin-bottom: 8px;
        }

        button {
            border: none;
            background-color: transparent;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #555555;
            font-weight: 600;
            font-size: 13px;
            line-height: 21px;
            cursor: pointer;
            margin-left: auto;
        }
    }

    .formRow {
        display: flex;
        flex-wrap: wrap;
        column-gap: 24px;
        row-gap: 28px;
        margin-bottom: 28px;
        position: relative;

        &:last-child {
            margin-bottom: 0px;
        }

        &.dBlaco {
            display: block;
        }
        
        &.borderTop {
            border-top: 6px solid #F4F4F4;
            padding-top: 40px;
            margin-top: 40px;
        }

        .col02 {
            width: calc((100% - 24px) / 2);
        }

        .col03 {
            width: calc((100% - 48px) / 3);

            div {
                max-width: 100%;
            }
        }

        .error {
            color: #F91313;
            font-size: 12px;
            line-height: 18px;
            font-weight: 400;
            margin-top: 4px;
        }
    }

    .radioList {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .radioGroup {
            position: relative;
            padding-left: 32px;
            cursor: pointer;
            user-select: none;
            width: calc((100% - 64px) / 5);

            input {
                position: absolute;
                opacity: 0;
                cursor: pointer;
                width: 100%;
                height: 100%;
                left: 0px;

                &:checked {
                    ~ .checkmark {
                        &:after {
                            display: block;
                        }
                    }
                }
            }

            .checkmark {
                border: 2px solid #555555;
                background-color: $white;
                border-radius: 50%;
                position: absolute;
                top: 0;
                left: 0;
                height: 20px;
                width: 20px;

                &:after {
                    background-color: #555555;
                    content: "";
                    position: absolute;
                    display: none;
                    top: 3px;
                    left: 3px;
                    width: 10px;
                    height: 10px;
                    border-radius: 50%;
                }
            }

            p {
                color: #555555;
                font-weight: 400;
                font-size: 14px;
                line-height: 20px;
            }
        }
    }

    .checkBox {
        position: relative;
        cursor: pointer;
        user-select: none;
        margin-bottom: 28px;

        &:last-child {
            margin-bottom: 0px;
        }

        input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
            height: 24px;
            width: 24px;
            z-index: 3;

            &:checked {
                ~.checkmark {
                    background-color: #555555;
                    border-color: #555555;

                    &:after {
                        display: block;
                    }
                }
            }
        }

        .checkmark {
            height: 20px;
            width: 20px;
            background-color: #FFFFFF;
            border-radius: 2px;
            border: 2px solid #555555;
            position: absolute;
            left: 0px;
            top: 2px;

            &:after {
                content: "";
                position: absolute;
                display: none;
                left: 5px;
                top: 0px;
                width: 5px;
                height: 10px;
                border: solid $white;
                border-width: 0 1px 1px 0;
                -webkit-transform: rotate(45deg);
                -ms-transform: rotate(45deg);
                transform: rotate(45deg);
            }
        }

        .text {
            padding-left: 28px;
            margin: 0px;
            text-align: left;

            h6 {
                color: #555555;
                font-weight: 700;
                font-size: 14px;
                line-height: 24px;

                .tooltipIcon {
                    width: 22px;
                    height: 22px;
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    position: relative;
                    margin-left: 2px;
                    cursor: pointer;

                    &:hover {
                    .tooltip {
                        display: block;
                    }
                    }
                }

                .tooltip {
                    background-color: #1E1E1E;
                    border-radius: 4px;
                    color: $white;
                    font-size: 14px;
                    line-height: 24px;
                    text-align: center;
                    max-width: 330px;
                    padding: 8px;
                    width: 84vw;
                    position: absolute;
                    left: 50%;
                    bottom: calc(100% + 10px);
                    transform: translateX(-50%);
                    display: none;

                    &:after {
                    border-left: 10px solid transparent;
                    border-right: 10px solid transparent;
                    border-top: 10px solid #1E1E1E;
                    content: "";
                    position: absolute;
                    left: 50%;
                    bottom: -9px;
                    transform: translateX(-50%);
                    }
                }
            }

            p {
                color: #555555;
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;

                a {
                    color: #0075F2;
                    text-decoration: underline;
                }
            }
        }
    }

    .btnGroup {
        display: flex;
        align-items: center;
        gap: 20px;
        margin-top: 50px;

        button {
            border-radius: 8px;
            border: 1px solid;
            cursor: pointer;
            font-weight: 500;
            font-size: 18px;
            line-height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 4px;
            height: 50px;

            &.back {
                background-color: $white;
                border-color: #555555;
                color: $black;
                width: 120px;
            }

            &.exit {
                background-color: #555555;
                border-color: #555555;
                color: $white;
                width: 220px;
                margin-left: auto;
            }

            &.continue {
                background-color: $secondary;
                border-color: $secondary;
                color: $black;
                width: 340px;
            }
        }
    }
}

.reviewDetails {
    border-top: 6px solid #F4F4F4;
    padding-top: 40px;
    margin-top: 40px;

    .flexBox {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;
    }

    .card {
        border-radius: 16px;
        border: 1px solid #555555;
        padding: 16px 24px;
        width: calc((100% - 24px) / 2);
    }

    .cardHeading {
        border-bottom: 1px solid #E5E5E5;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 6px 0px;

        h3 {
            color: $black;
            font-weight: 500;
            font-size: 20px;
            line-height: 30px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        button {
            background-color: transparent;
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #555555;
            font-weight: 600;
            font-size: 13px;
            line-height: 20px;
            cursor: pointer;
        }
    }

    .content {
        margin-top: 16px;
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        gap: 12px;

        li {
            display: flex;
            column-gap: 24px;
        }

        &.flexColumn {
            gap: 16px;

            li {
                flex-direction: column;
                gap: 0px;

                span, strong {
                    width: 100%;
                    text-align: left;
                }
            }
        }

        h6 {
            color: #555555;
            font-weight: 700;
            font-size: 14px;
            line-height: 20px;
            width: 156px;
        }

        strong {
            color: $black;
            font-weight: 700;
            font-size: 14px;
            line-height: 20px;
            text-align: right;
            width: 156px;
        }

        p {
            color: #555555;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            margin-top: 8px;
        }

        span {
            color: #555555;
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            width: calc(100% - 180px);
        }
    }
}

.note {
    border-radius: 8px;
    border: 1px solid #0075F2;
    background-color: #EDF6FF;
    padding: 20px;
    margin-top: 16px;
    margin-bottom: 24px;

    h6 {
        color: #0075F2;
        font-weight: 700;
        font-size: 16px;
        line-height: 24px;
        text-transform: uppercase;
    }

    p {
        color: #0075F2;
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;
        margin-top: 8px;
    }
}