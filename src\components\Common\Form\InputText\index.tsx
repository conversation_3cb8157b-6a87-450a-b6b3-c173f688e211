"use client";
import { useParams } from "next/navigation";
import { FormikProps } from "formik";
import { FormField } from "@/types/form";

interface InputTextProps {
  formik: FormikProps<Record<string, string | number | boolean>>;
  data: FormField;
}

const InputText: React.FC<InputTextProps> = ({ formik, data }) => {
  const params = useParams();
  const { lang } = params as { lang: "en" | "es" };

  const {
    columnName,
    label,
    description,
  } = data;

  // Fallback logic if label or description is not provided
  const fieldLabel = label ? label[lang] || label.en : columnName;
  const placeholder = description ? description[lang] || description.en : "";

  const error = formik.touched[columnName] && formik.errors[columnName];

  return (
    <div className="formGroup">
      <div className="col4">
        <label htmlFor={columnName}>{fieldLabel}</label>
      </div>
      <div className="col5">
        <input
          type="text"
          placeholder={placeholder}
          id={columnName}
          name={columnName}
          value={formik.values[columnName] !== undefined ? String(formik.values[columnName]) : ""}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
        />
        {error && <div className="error">{formik.errors[columnName]}</div>}
      </div>
    </div>
  );
};

export default InputText;
