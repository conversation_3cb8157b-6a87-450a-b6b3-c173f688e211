@import '../../../styles/global.scss';

.checkBoxGroup {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    &.operatingArea {
        flex-direction: column;

        .checkBox {
            width: 100%;
        }
    }
}

.checkBox {
    position: relative;
    cursor: pointer;
    user-select: none;
    width: calc((100% - 64px) / 5);

    input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 24px;
        width: 24px;
        z-index: 3;

        &:checked {
            ~.checkmark {
                background-color: #555555;
                border-color: #555555;

                &:after {
                    display: block;
                }
            }
        }
    }

    .checkmark {
        height: 20px;
        width: 20px;
        background-color: #FFFFFF;
        border-radius: 2px;
        border: 2px solid #555555;
        position: absolute;
        left: 0px;
        top: 2px;

        &:after {
            content: "";
            position: absolute;
            display: none;
            left: 5px;
            top: 0px;
            width: 5px;
            height: 10px;
            border: solid $white;
            border-width: 0 1px 1px 0;
            -webkit-transform: rotate(45deg);
            -ms-transform: rotate(45deg);
            transform: rotate(45deg);
        }
    }

    p {
        color: #555555;
        font-weight: 400;
        font-size: 14px;
        line-height: 24px;
        padding-left: 28px;
        margin: 0px;
        text-align: left;

        a {
            color: #0075F2;
        }
    }
}