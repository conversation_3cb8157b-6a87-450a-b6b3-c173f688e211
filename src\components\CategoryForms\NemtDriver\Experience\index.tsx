"use client";
import React, { useEffect, useState, useCallback } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useNemtDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import { fetchNemtDriverFormFields, FormValue, submitDriverDetails, fetchDriverDetails, FetchDriverDetailsResponse } from "@/services/driverFormService";
import CheckboxGroup from "@/components/Common/CheckboxGroup";
import Dropdown from "@/components/Common/Dropdown";
import css from './nemtDriverExperience.module.scss';

interface NemtExperienceFormValues {
  nemtDrivingExperience: string;
  totalDrivingExperience: string;
  vehicleTypes: number[];
  otherVehicleType: string;
  passengerTypes: number[];
  assistanceSkills: number[];
  tripTypes: number[];
  operationalExperience: number[];
  transmissionTypes: number[];
  additionalSkills: number[];
}

type DriverWithNemtData = FetchDriverDetailsResponse["data"]["driver"] & {
  nemtVehicleType?: (string | number)[];
};

const Experience: React.FC = () => {
  const { updateStepFromApiResponse } = useNemtDriverCategory();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isDataLoading, setIsDataLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  // Form options state
  const [vehicleTypeOptions, setVehicleTypeOptions] = useState<FormValue[]>([]);
  const [passengerTypeOptions, setPassengerTypeOptions] = useState<FormValue[]>([]);
  const [assistanceSkillOptions, setAssistanceSkillOptions] = useState<FormValue[]>([]);
  const [tripTypeOptions, setTripTypeOptions] = useState<FormValue[]>([]);
  const [transmissionOptions, setTransmissionOptions] = useState<FormValue[]>([]);
  const [additionalSkillsOptions, setAdditionalSkillsOptions] = useState<FormValue[]>([]);

  const experienceYears = [
    "< 1 Year",
    "1 Year", 
    "2 Years",
    "3 Years",
    "4 Years", 
    "5 Years",
    "6 - 10 Years",
    "10+ Years"
  ];

  // Validation schema with proper error handling
  const validationSchema = Yup.object().shape({
    nemtDrivingExperience: Yup.string().required("NEMT driving experience is required"),
    vehicleTypes: Yup.array().test(
      'at-least-one-vehicle-type',
      'Please select at least one vehicle type',
      function(value) {
        const { otherVehicleType } = this.parent;
        return (value && value.length > 0) || (otherVehicleType && otherVehicleType.trim() !== "");
      }
    ),
    passengerTypes: Yup.array().min(1, "Please select at least one passenger type"),
    assistanceSkills: Yup.array().min(1, "Please select at least one assistance skill"),
    tripTypes: Yup.array().min(1, "Please select at least one trip type"),
    operationalExperience: Yup.array().min(1, "Please select at least one operational experience"),
    transmissionTypes: Yup.array().min(1, "Please select at least one transmission type"),
  });

  const formik = useFormik<NemtExperienceFormValues>({
    initialValues: {
      nemtDrivingExperience: "",
      totalDrivingExperience: "",
      vehicleTypes: [],
      otherVehicleType: "",
      passengerTypes: [],
      assistanceSkills: [],
      tripTypes: [],
      operationalExperience: [],
      transmissionTypes: [],
      additionalSkills: [],
    },
    validationSchema,
    validateOnBlur: false,
    validateOnChange: false, // Only validate on submit to prevent premature errors
    enableReinitialize: true, // Allow form to reinitialize when initial values change
    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  // Helper function to clear field errors on change
  const handleFieldChange = (fieldName: string, value: any) => {
    formik.setFieldValue(fieldName, value);
    // Clear the error for this field when user makes changes
    if (formik.errors[fieldName as keyof NemtExperienceFormValues]) {
      formik.setFieldError(fieldName, undefined);
    }
  };

  // Helper functions
  const experienceReverseMapping: Record<number, string> = {
    0: "< 1 Year",
    1: "1 Year",
    2: "2 Years",
    3: "3 Years",
    4: "4 Years",
    5: "5 Years",
    6: "6 - 10 Years",
    10: "10+ Years"
  };

  const cleanArrayToNumbers = useCallback((arr: (string | number)[]): number[] => {
    if (!Array.isArray(arr)) {
      console.log('cleanArrayToNumbers - not array:', arr);
      return [];
    }
    const cleaned = [...new Set(arr.map(item => {
      const num = Number(item);
      console.log('Converting item:', { item, type: typeof item, num, isNaN: isNaN(num) });
      return num;
    }).filter(num => !isNaN(num)))];
    console.log('cleanArrayToNumbers:', { input: arr, output: cleaned });
    return cleaned;
  }, []);

  // Function to separate numeric IDs and text values from mixed array
  const separateVehicleTypes = useCallback((arr: (string | number)[]): { ids: number[], otherText: string } => {
    if (!Array.isArray(arr)) return { ids: [], otherText: '' };

    const ids: number[] = [];
    let otherText = '';

    console.log('separateVehicleTypes input:', arr);

    arr.forEach(item => {
      const numValue = Number(item);
      if (!isNaN(numValue) && numValue.toString() === item.toString()) {
        // Only treat as number if it's actually a numeric string/number
        ids.push(numValue);
      } else if (typeof item === 'string' && item.trim() && isNaN(Number(item))) {
        // Only treat as text if it's a non-numeric string
        otherText = item.trim(); // Take the last string value as other text
      }
    });

    const result = { ids: [...new Set(ids)], otherText };
    console.log('separateVehicleTypes output:', result);
    return result;
  }, []);



  // Load form data
  const loadFormFields = useCallback(async () => {
    if (isInitialized) return; // Prevent multiple calls
    
    setIsDataLoading(true);
    
    try {
      const [formFieldsResponse, driverDetails] = await Promise.all([
        fetchNemtDriverFormFields([
          "types-of-nemt-vehicles-driven-driver-non-emergency-medical-transportation",
          "types-of-passengers-transported-driver-non-emergency-medical-transportation",
          "assistance-skills-driver-non-emergency-medical-transportation",
          "trip-types-destinations-driver-non-emergency-medical-transportation",
          "transmission-type-experience-driver-school-bus-driver",
          "additional-skills-experience-driver-school-bus-driver"
        ]),
        fetchDriverDetails()
      ]);

      // Set form field options
      if (formFieldsResponse && Object.keys(formFieldsResponse).length > 0) {
        const formFields = formFieldsResponse;
        
        const vehicleFieldKey = "types-of-nemt-vehicles-driven-driver-non-emergency-medical-transportation";
        if (formFields[vehicleFieldKey]) {
          setVehicleTypeOptions(formFields[vehicleFieldKey]);
        }

        const passengerFieldKey = "types-of-passengers-transported-driver-non-emergency-medical-transportation";
        if (formFields[passengerFieldKey]) {
          setPassengerTypeOptions(formFields[passengerFieldKey]);
        }

        const assistanceFieldKey = "assistance-skills-driver-non-emergency-medical-transportation";
        if (formFields[assistanceFieldKey]) {
          setAssistanceSkillOptions(formFields[assistanceFieldKey]);
        }

        const tripFieldKey = "trip-types-destinations-driver-non-emergency-medical-transportation";
        if (formFields[tripFieldKey]) {
          setTripTypeOptions(formFields[tripFieldKey]);
        }

        const transmissionFieldKey = "transmission-type-experience-driver-school-bus-driver";
        if (formFields[transmissionFieldKey]) {
          setTransmissionOptions(formFields[transmissionFieldKey]);
        }

        const additionalFieldKey = "additional-skills-experience-driver-school-bus-driver";
        if (formFields[additionalFieldKey]) {
          setAdditionalSkillsOptions(formFields[additionalFieldKey]);
        }
      }

      // Set form values from driver data
      if (driverDetails?.status && driverDetails?.data?.driver) {
        const driver = driverDetails.data.driver as DriverWithNemtData;

        console.log('🔍 Raw driver data from API:', {
          totalVerifiableRelevantExperience: driver.totalVerifiableRelevantExperience,
          totalExperienceYears: driver.totalExperienceYears,
          nemtVehicleType: driver.nemtVehicleType,
          passengerTransported: driver.passengerTransported,
          assistanceSkills: driver.assistanceSkills,
          tripTypes: driver.tripTypes,
          operationalExperience: driver.operationalExperience,
          transmissionTypes: driver.transmissionTypes,
          additionalSkills: driver.additionalSkills
        });

        const vehicleTypeData = separateVehicleTypes(driver.nemtVehicleType || []);

        const cleanedValues = {
          nemtDrivingExperience: driver.totalVerifiableRelevantExperience !== undefined
            ? experienceReverseMapping[driver.totalVerifiableRelevantExperience] || ""
            : "",
          totalDrivingExperience: driver.totalExperienceYears !== undefined
            ? experienceReverseMapping[driver.totalExperienceYears] || ""
            : "",
          vehicleTypes: vehicleTypeData.ids,
          otherVehicleType: vehicleTypeData.otherText,
          passengerTypes: cleanArrayToNumbers(driver.passengerTransported || []),
          assistanceSkills: cleanArrayToNumbers(driver.assistanceSkills || []),
          tripTypes: cleanArrayToNumbers(driver.tripTypes || []),
          operationalExperience: cleanArrayToNumbers(driver.operationalExperience || []),
          transmissionTypes: cleanArrayToNumbers(driver.transmissionTypes || []),
          additionalSkills: cleanArrayToNumbers(driver.additionalSkills || [])
        };

        console.log('🎯 Cleaned form values:', cleanedValues);

        // Set each field value individually to ensure they're set
        Object.entries(cleanedValues).forEach(([key, value]) => {
          console.log(`Setting ${key}:`, value);
          formik.setFieldValue(key, value);
        });

        // Add a small delay to check if values are actually set
        setTimeout(() => {
          console.log('🔄 Form values after setFieldValue:', formik.values);
        }, 100);
      }

      setIsInitialized(true);
    } catch (error) {
      console.error("Failed to load form fields:", error);
      toast.error("Failed to load form options. Please refresh the page.");
    } finally {
      setIsDataLoading(false);
    }
  }, [isInitialized, cleanArrayToNumbers, separateVehicleTypes, experienceReverseMapping]);

  useEffect(() => {
    loadFormFields();
  }, [loadFormFields]);

  const handleSubmit = async (values: NemtExperienceFormValues, shouldContinue: boolean = true) => {
    setIsLoading(true);
    try {
      const experienceMapping: Record<string, number> = {
        "< 1 Year": 0,
        "1 Year": 1,
        "2 Years": 2,
        "3 Years": 3,
        "4 Years": 4,
        "5 Years": 5,
        "6 - 10 Years": 6,
        "10+ Years": 10
      };

      const ensureNumberArray = (arr: (string | number)[]): number[] => {
        if (!Array.isArray(arr)) return [];
        return [...new Set(arr.map(item => Number(item)).filter(num => !isNaN(num)))];
      };

      const payload = {
        currentStage: 3,
        currentStep: 1,
        driver: {
          totalVerifiableRelevantExperience: values.nemtDrivingExperience ? experienceMapping[values.nemtDrivingExperience] : null,
          totalExperienceYears: values.totalDrivingExperience ? experienceMapping[values.totalDrivingExperience] : null,
          nemtVehicleType: (() => {
            const selectedIds = ensureNumberArray(values.vehicleTypes);
            const otherText = values.otherVehicleType?.trim();

            console.log('NEMT Vehicle Type Debug:', {
              selectedIds,
              otherText,
              vehicleTypesRaw: values.vehicleTypes,
              otherVehicleTypeRaw: values.otherVehicleType
            });

            // If only other text is provided (no checkboxes)
            if (selectedIds.length === 0 && otherText) {
              const result = [otherText];
              console.log('Only other text:', result);
              return result;
            }

            // If checkboxes are selected
            if (selectedIds.length > 0) {
              // If other text is also provided, add it to the array
              if (otherText) {
                const result = [...selectedIds, otherText];
                console.log('Checkboxes + other text:', result);
                return result;
              }
              // Only checkboxes selected
              console.log('Only checkboxes:', selectedIds);
              return selectedIds;
            }

            console.log('No vehicle types selected');
            return null;
          })(),
          passengerTransported: values.passengerTypes.length > 0 ? ensureNumberArray(values.passengerTypes) : null,
          assistanceSkills: values.assistanceSkills.length > 0 ? ensureNumberArray(values.assistanceSkills) : null,
          tripTypes: values.tripTypes.length > 0 ? ensureNumberArray(values.tripTypes) : null,
          operationalExperience: values.operationalExperience.length > 0 ? ensureNumberArray(values.operationalExperience) : null,
          transmissionTypes: values.transmissionTypes.length > 0 ? ensureNumberArray(values.transmissionTypes) : null,
          additionalSkills: values.additionalSkills.length > 0 ? ensureNumberArray(values.additionalSkills) : null
        }
      };

      const response = await submitDriverDetails(payload);

      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Experience saved successfully!");
          window.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to save experience data";
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error("Error submitting experience:", error);
      toast.error("Failed to save experience. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state
  if (isDataLoading || !isInitialized) {
    return (
      <div style={{ padding: "2rem", textAlign: "center" }}>
        <div style={{ fontSize: "18px", color: "#666" }}>Loading form options...</div>
      </div>
    );
  }

  // Debug: Log current form values when rendering
  console.log('🎨 Rendering form with values:', formik.values);

  return (
    <div className={css.nemtDriverExperience}>
      <h3>Step 1: Experience (NEMT Driver)</h3>
      <h5>Detail your experience providing Non-Emergency Medical Transportation, including passenger assistance and vehicle types.</h5>
      <h6 className={css.required}>Required fields are marked with <sup>*</sup></h6>

      <form onSubmit={formik.handleSubmit}>
        {/* NEMT Driving Experience */}
        <div className={css.card}>
          <h3>NEMT Driving Experience</h3>
          <div className={css.formRow}>
            <div className={css.col02}>
              <label htmlFor="nemtDrivingExperience">Total Years of Verifiable NEMT Driving Experience:<sup>*</sup></label>
              <Dropdown
                options={experienceYears.map(year => ({ value: year, label: year }))}
                value={formik.values.nemtDrivingExperience}
                placeholder="Select Years"
                onChange={(value) => handleFieldChange("nemtDrivingExperience", value)}
                error={formik.touched.nemtDrivingExperience ? formik.errors.nemtDrivingExperience : undefined}
                name="nemtDrivingExperience"
              />
            </div>
            <div className={css.col02}>
              <label htmlFor="totalDrivingExperience">Total Years of Driving Experience (Any Type): <span>(If different)</span></label>
              <Dropdown
                options={experienceYears.map(year => ({ value: year, label: year }))}
                value={formik.values.totalDrivingExperience}
                placeholder="Select Years"
                onChange={(value) => handleFieldChange("totalDrivingExperience", value)}
                name="totalDrivingExperience"
              />
            </div>
          </div>
        </div>

        {/* Vehicle Types */}
        <div className={css.card}>
          <h3>Type(s) of NEMT Vehicles Driven <sup>*</sup></h3>
          <label>(Check all that apply)</label>

          {vehicleTypeOptions.length > 0 ? (
            <CheckboxGroup
              name="vehicleTypes"
              options={vehicleTypeOptions.map(option => ({
                id: option.formValueId,
                label: option.label.en
              }))}
              selectedValues={formik.values.vehicleTypes}
              onChange={(selected, otherValue) => {
                handleFieldChange('vehicleTypes', selected);
                handleFieldChange('otherVehicleType', otherValue || '');
              }}
              otherLabel="Other (Specify Below)"
              otherInputLabel="Other NEMT Vehicle Specified:"
              otherValue={formik.values.otherVehicleType}
            />
          ) : (
            <div>Loading vehicle options...</div>
          )}
          
          {formik.touched.vehicleTypes && formik.errors.vehicleTypes && (
            <span className={css.error}>
              {formik.errors.vehicleTypes}
            </span>
          )}
        </div>

        {/* Passenger Assistance & Service Experience */}
        <div className={css.card}>
          <h3>Passenger Assistance & Service Experience <sup>*</sup></h3>
          <label>(Check all that apply)</label>

          <h5>Types of Passengers Transported:</h5>
          {passengerTypeOptions.length > 0 ? (
            <CheckboxGroup
              name="passengerTypes"
              options={passengerTypeOptions.map(option => ({
                id: option.formValueId,
                label: option.label.en
              }))}
              selectedValues={formik.values.passengerTypes}
              onChange={(selected) => {
                handleFieldChange('passengerTypes', selected);
              }}
            />
          ) : (
            <div>Loading passenger type options...</div>
          )}
          {formik.touched.passengerTypes && formik.errors.passengerTypes && (
            <span className={css.error}>
              {formik.errors.passengerTypes}
            </span>
          )}

          <h5>Assistance Skills:</h5>
          {assistanceSkillOptions.length > 0 ? (
            <CheckboxGroup
              name="assistanceSkills"
              options={assistanceSkillOptions.map(option => ({
                id: option.formValueId,
                label: option.label.en
              }))}
              selectedValues={formik.values.assistanceSkills}
              onChange={(selected) => {
                handleFieldChange('assistanceSkills', selected);
              }}
            />
          ) : (
            <div>Loading assistance skill options...</div>
          )}
          {formik.touched.assistanceSkills && formik.errors.assistanceSkills && (
            <span className={css.error}>
              {formik.errors.assistanceSkills}
            </span>
          )}

          <h5>Trip Types / Destinations:</h5>
          {tripTypeOptions.length > 0 ? (
            <CheckboxGroup
              name="tripTypes"
              options={tripTypeOptions.map(option => ({
                id: option.formValueId,
                label: option.label.en
              }))}
              selectedValues={formik.values.tripTypes}
              onChange={(selected) => {
                handleFieldChange('tripTypes', selected);
              }}
            />
          ) : (
            <div>Loading trip type options...</div>
          )}
          {formik.touched.tripTypes && formik.errors.tripTypes && (
            <span className={css.error}>
              {formik.errors.tripTypes}
            </span>
          )}
        </div>

        {/* Operational Experience */}
        <div className={css.card}>
          <h3>Operational Experience <sup>*</sup></h3>
          <label>(Check all that apply)</label>

          <CheckboxGroup
            name="operationalExperience"
            options={[
              { id: 1, label: "Working with NEMT Brokers (e.g., LogistiCare/Modivcare, MTM, Veyo)" },
              { id: 2, label: "Using Dispatch / Scheduling Software / Apps" },
              { id: 3, label: "Following specific broker/facility procedures" },
              { id: 4, label: "Maintaining trip logs / documentation (Paper / Electronic)" },
              { id: 5, label: "Pre/Post Trip Vehicle Inspections" },
              { id: 6, label: "Keeping vehicle clean and safe" }
            ]}
            selectedValues={formik.values.operationalExperience}
            onChange={(selected) => {
              handleFieldChange('operationalExperience', selected);
            }}
          />
          {formik.touched.operationalExperience && formik.errors.operationalExperience && (
            <span className={css.error}>
              {formik.errors.operationalExperience}
            </span>
          )}
        </div>

        {/* Transmission Type Experience */}
        <div className={css.card}>
          <h3>Transmission Type Experience <sup>*</sup></h3>
          <label>(In NEMT Vehicles Driven) (Check all that apply)</label>

          {transmissionOptions.length > 0 ? (
            <CheckboxGroup
              name="transmissionTypes"
              options={transmissionOptions.map(option => ({
                id: option.formValueId,
                label: option.label.en
              }))}
              selectedValues={formik.values.transmissionTypes}
              onChange={(selected) => {
                handleFieldChange('transmissionTypes', selected);
              }}
            />
          ) : (
            <div>Loading transmission options...</div>
          )}
          {formik.touched.transmissionTypes && formik.errors.transmissionTypes && (
            <span className={css.error}>
              {formik.errors.transmissionTypes}
            </span>
          )}
        </div>

        {/* Additional Skills & Experience */}
        <div className={css.card}>
          <h3>Additional Skills & Experience</h3>
          <label>(Optional)</label>

          {additionalSkillsOptions.length > 0 ? (
            <CheckboxGroup
              name="additionalSkills"
              options={additionalSkillsOptions.map(option => ({
                id: option.formValueId,
                label: option.label.en
              }))}
              selectedValues={formik.values.additionalSkills}
              onChange={(selected) => {
                handleFieldChange('additionalSkills', selected);
              }}
            />
          ) : (
            <div>Loading additional skills options...</div>
          )}
        </div>

        {/* Navigation Buttons */}
        <div className={css.btnGroup}>
          <button
            type="button"
            onClick={() => router.back()}
            disabled={isLoading}
            className={css.back}
          >
            <img src="/images/icons/arrow_back.svg" alt="Back" /> Back
          </button>

          <button
            type="submit"
            disabled={isLoading}
            className={css.continue}
          >
            {isLoading ? "Saving..." : "Save & Continue"}
          </button>

          <button
            type="button"
            onClick={() => handleSubmit(formik.values, false)}
            disabled={isLoading}
            className={css.exit}
          >
            Save & Exit
          </button>
        </div>
      </form>
    </div>
  );
};

export default Experience;