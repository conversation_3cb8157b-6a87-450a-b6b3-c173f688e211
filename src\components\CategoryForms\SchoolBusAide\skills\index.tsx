"use client";
import DateInput from "@/components/Common/DateInput/DateInput";
import RadioGroup from "@/components/Common/RadioGroup";
import Dropdown from "@/components/Common/Dropdown";
import { useSchoolBusAideCategory } from "@/contexts/CommonDriverCategoryContext";
import {
  fetchSchoolBusAideFormFields,
  FormValue,
  submitDriverDetails,
  fetchDriverDetails,
  FetchDriverDetailsResponse,
} from "@/services/driverFormService";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import * as Yup from "yup";

interface SkillsCertificationsFormValues {
  hasCPRCertification: string;
  cprCertificationType: string | number;
  cprExpirationDate: Date | null;
  hasFirstAidCertification: string;
  firstAidCertificationType: number | string;
  firstAidExpirationDate: Date | null;
  hasPATS: string;
  patsCertificationDate: Date | null;
  issuingAgency: string;
  hasBehaviorTraining: string;
  behaviorTrainingType: string;
  behaviorTrainingDate: Date | null;
  stateClearancesStatus: string;
  stateClearancesDetails: string;
}

type DriverDetails = FetchDriverDetailsResponse["data"]["driver"];

interface DriverPayload {
  cprCertification?: boolean;
  cprCertificationType?: number;
  cprCertificationExpirationDate?: string | null;
  firstAidCertification?: boolean;
  firstAidCertificationType?: number;
  firstAidCertificationExpirationDate?: string | null;
  patsCertification?: boolean;
  patsCertificationDate?: string | null;
  patsCertificateIssuingAgency?: string;
  behaviorTraining?: boolean;
  behaviorTrainingName?: string;
  behaviorTrainingCompletedDate?: string | null;
  childAbuseClearance?: number;
  childAbuseClearanceHeld?: string;
}

const SkillsCertificate: React.FC = () => {
  const { updateStepFromApiResponse, goToPreviousStep, canGoBack } =
    useSchoolBusAideCategory();
  const router = useRouter();

  const [certificationType, setCertificationType] = useState<FormValue[]>([]);
  const [aidCertification, setAidCertification] = useState<FormValue[]>([]);
  const [assistanceTrainingOptions, setAssistanceTrainingOptions] = useState<
    FormValue[]
  >([]);
  const [stateClearances, setStateClearances] = useState<FormValue[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  useEffect(() => {
    const loadFormFields = async () => {
      try {
        const fields = await fetchSchoolBusAideFormFields();

        const cprTypes = (fields["cpr-certification-type-driver-bus-aide-assistant"] || [])
          .filter(option => option.label?.en && option.label.en.trim() !== "");
        const firstAidTypes = (fields["first-aid-certification-type-driver-bus-aide-assistant"] || [])
          .filter(option => option.label?.en && option.label.en.trim() !== "");
        const patsTrainingTypes = (fields["passenger-assistance-training-pats-ctaa-pass-driver-bus-aide-assistant"] || [])
          .filter(option => option.label?.en && option.label.en.trim() !== "");
        const stateClearanceOptions = (fields["state-required-clearances-training-for-school-personnel-driver-bus-aide-assistant"] || [])
          .filter(option => option.label?.en && option.label.en.trim() !== "");

        setCertificationType(cprTypes);
        setAidCertification(firstAidTypes);
        setAssistanceTrainingOptions(patsTrainingTypes);
        setStateClearances(stateClearanceOptions);

        console.log("🔍 SchoolBusAide Skills form fields loaded:", {
          cprTypes: cprTypes.length,
          firstAidTypes: firstAidTypes.length,
          patsTrainingTypes: patsTrainingTypes.length,
          stateClearanceOptions: stateClearanceOptions.length
        });
      } catch (error) {
        console.error("Failed to load form fields:", error);
        toast.error("Failed to load form fields. Please refresh.");
      }
    };

    loadFormFields();
  }, []);

  const formik = useFormik<SkillsCertificationsFormValues>({
    initialValues: {
      hasCPRCertification: "yes",
      cprCertificationType: "",
      cprExpirationDate: null,
      hasFirstAidCertification: "yes",
      firstAidCertificationType: "",
      firstAidExpirationDate: null,
      hasPATS: "no",
      patsCertificationDate: null,
      issuingAgency: "",
      hasBehaviorTraining: "no",
      behaviorTrainingType: "",
      behaviorTrainingDate: null,
      stateClearancesStatus: "yes",
      stateClearancesDetails: "",
    },
    enableReinitialize: true,
    validationSchema: Yup.object({
      // ✅ CPR Certification
      hasCPRCertification: Yup.string().required("Required"),
      cprCertificationType: Yup.string().when(
        "hasCPRCertification",
        ([value], schema) => {
          const isYes = value && (value.toLowerCase().includes("yes") || value.toLowerCase().includes("certified"));
          return isYes
            ? schema.required("CPR Certification Type is required")
            : schema;
        }
      ),
      cprExpirationDate: Yup.date()
        .nullable()
        .when("hasCPRCertification", ([value], schema) => {
          const isYes = value && (value.toLowerCase().includes("yes") || value.toLowerCase().includes("certified"));
          return isYes
            ? schema
                .required("CPR Expiration Date is required")
                .typeError("Enter a valid date")
            : schema;
        }),

      // ✅ First Aid Certification
      hasFirstAidCertification: Yup.string().required("Required"),
      firstAidCertificationType: Yup.string().when(
        "hasFirstAidCertification",
        ([value], schema) => {
          const isYes = value && (value.toLowerCase().includes("yes") || value.toLowerCase().includes("certified"));
          return isYes
            ? schema.required("First Aid Certification Type is required")
            : schema;
        }
      ),
      firstAidExpirationDate: Yup.date()
        .nullable()
        .when("hasFirstAidCertification", ([value], schema) => {
          const isYes = value && (value.toLowerCase().includes("yes") || value.toLowerCase().includes("certified"));
          return isYes
            ? schema
                .required("First Aid Expiration Date is required")
                .typeError("Enter a valid date")
            : schema;
        }),

      // ✅ PATS Certification (optional date + agency, but only shown if "Yes")
      hasPATS: Yup.string().required("Required"),
      patsCertificationDate: Yup.date()
        .nullable()
        .when("hasPATS", ([value], schema) => {
          const isYes = value && (value.toLowerCase().includes("yes") || value.toLowerCase().includes("certified"));
          return isYes
            ? schema.typeError("Enter a valid date (optional)")
            : schema;
        }),
      issuingAgency: Yup.string(),

      // ✅ Behavior Training
      hasBehaviorTraining: Yup.string().required("Required"),
      behaviorTrainingType: Yup.string().when(
        "hasBehaviorTraining",
        ([value], schema) => {
          const isYes = value && (value.toLowerCase().includes("yes") || value.toLowerCase().includes("certified"));
          return isYes
            ? schema.required("Training Type is required")
            : schema;
        }
      ),
      behaviorTrainingDate: Yup.date()
        .nullable()
        .when("hasBehaviorTraining", ([value], schema) => {
          const isYes = value && (value.toLowerCase().includes("yes") || value.toLowerCase().includes("certified"));
          return isYes
            ? schema.typeError("Enter a valid date (optional)")
            : schema;
        }),

      // ✅ State Clearance
      stateClearancesStatus: Yup.string().required("Required"),
      stateClearancesDetails: Yup.string().when(
        "stateClearancesStatus",
        ([value], schema) =>
          value === "Yes, All Current" ||
          value === "Some Completed / Need Updates"
            ? schema.required("Please specify held clearances")
            : schema
      ),
    }),

    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  const handleSubmit = async (
    values: SkillsCertificationsFormValues,
    shouldContinue = true
  ) => {
    setIsLoading(true);
    try {
      const toBool = (val: string): boolean => {
        if (!val) return false;
        const lowerVal = val.toLowerCase();
        return lowerVal.includes("yes") || lowerVal.includes("certified");
      };

      const getClearanceId = (): number | undefined =>
        stateClearances.find(
          (opt) => opt.label.en === values.stateClearancesStatus
        )?.formValueId;

      const safeParseInt = (value: string | number): number => {
        if (typeof value === 'number') return value;
        const parsed = parseInt(value.toString(), 10);
        return isNaN(parsed) ? 0 : parsed;
      };

      const driver: DriverPayload = {
        cprCertification: toBool(values.hasCPRCertification),
        firstAidCertification: toBool(values.hasFirstAidCertification),
        patsCertification: toBool(values.hasPATS),
        behaviorTraining: toBool(values.hasBehaviorTraining),
        childAbuseClearance: getClearanceId(),
      };

      if (driver.cprCertification) {
        driver.cprCertificationType = safeParseInt(values.cprCertificationType);
        driver.cprCertificationExpirationDate = values.cprExpirationDate
          ? values.cprExpirationDate.toISOString()
          : null;
      }

      if (driver.firstAidCertification) {
        driver.firstAidCertificationType = safeParseInt(values.firstAidCertificationType);
        driver.firstAidCertificationExpirationDate =
          values.firstAidExpirationDate
            ? values.firstAidExpirationDate.toISOString()
            : null;
      }

      if (driver.patsCertification) {
        // Use ISO string format like other components
        driver.patsCertificationDate = values.patsCertificationDate
          ? values.patsCertificationDate.toISOString()
          : null;
        driver.patsCertificateIssuingAgency = values.issuingAgency || "";
      } else {
        // Don't send PATS fields if certification is false
        driver.patsCertificationDate = null;
        driver.patsCertificateIssuingAgency = "";
      }

      if (driver.behaviorTraining) {
        driver.behaviorTrainingName = values.behaviorTrainingType;
        driver.behaviorTrainingCompletedDate = values.behaviorTrainingDate
          ? values.behaviorTrainingDate.toISOString()
          : null;
      }

      // Only send held date if clearance is not "No / Unsure"
      // const clearanceId = driver.childAbuseClearance;
      // driver.childAbuseClearanceHeld =
      //   clearanceId !== 616 ? values.stateClearancesDetails : "";
      driver.childAbuseClearanceHeld =
        values.stateClearancesStatus === "Yes, All Current" ||
        values.stateClearancesStatus === "Some Completed / Need Updates"
          ? values.stateClearancesDetails
          : "";

      const payload = {
        currentStage: 3,
        currentStep: 3,
        driver,
      };

      console.log("🔍 SchoolBusAide Skills - Submitting payload:", JSON.stringify(payload, null, 2));
      console.log("🔍 PATS fields specifically:", {
        patsCertification: driver.patsCertification,
        patsCertificationDate: driver.patsCertificationDate,
        patsCertificationDateType: typeof driver.patsCertificationDate,
        patsCertificationDateValid: typeof driver.patsCertificationDate === 'string',
        patsCertificateIssuingAgency: driver.patsCertificateIssuingAgency,
        formValues: {
          hasPATS: values.hasPATS,
          patsCertificationDate: values.patsCertificationDate,
          patsCertificationDateType: typeof values.patsCertificationDate,
          patsCertificationDateValid: values.patsCertificationDate instanceof Date,
          issuingAgency: values.issuingAgency
        }
      });

      const res = await submitDriverDetails(payload);
      if (res?.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(res);
          toast.success("Skills information saved successfully!");
        } else {
          toast.success("Draft saved successfully! Redirecting...");
          setTimeout(() => router.push("/"), 1500);
        }
      } else {
        throw new Error(
          res?.message || res?.error?.message || "Failed to save."
        );
      }
    } catch (err) {
      console.error("Skills form submission error:", err);
      const errorMessage = err instanceof Error
        ? err.message
        : "Submission failed. Please try again.";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const loadDriverDetails = async () => {
      if (certificationType.length === 0 || aidCertification.length === 0 ||
          assistanceTrainingOptions.length === 0 || stateClearances.length === 0) {
        console.log("🔍 Form options not loaded yet, skipping driver details load");
        return;
      }

      try {
        const res = await fetchDriverDetails();
        const d = res?.data?.driver as DriverDetails & {
          cprCertificationType?: number;
          cprCertificationExpirationDate?: string;
          firstAidCertificationType?: number;
          firstAidCertificationExpirationDate?: string;
          patsCertificationDate?: string;
          patsCertificateIssuingAgency?: string;
          behaviorTrainingName?: string;
          behaviorTrainingCompletedDate?: string;
          childAbuseClearance?: number;
          childAbuseClearanceHeld?: string;
        };

        if (d) {
          const getBooleanOptionLabel = (value: boolean, optionsList: FormValue[], truePattern: string = "yes", falsePattern: string = "no"): string => {
            if (optionsList.length === 0) {
              return value ? truePattern : falsePattern;
            }

            if (value) {
              // Find option that contains "yes" or "certified"
              const yesOption = optionsList.find(opt =>
                opt.label.en.toLowerCase().includes("yes") ||
                opt.label.en.toLowerCase().includes("certified")
              );
              return yesOption ? yesOption.label.en : truePattern;
            } else {
              // Find option that contains "no" or "not"
              const noOption = optionsList.find(opt =>
                opt.label.en.toLowerCase().includes("no") ||
                opt.label.en.toLowerCase().includes("not")
              );
              return noOption ? noOption.label.en : falsePattern;
            }
          };

          const cleanedValues: SkillsCertificationsFormValues = {
            hasCPRCertification: getBooleanOptionLabel(d.cprCertification, []),
            cprCertificationType: d.cprCertification
              ? d.cprCertificationType?.toString() || ""
              : "",
            cprExpirationDate: d.cprCertification && d.cprCertificationExpirationDate
              ? new Date(d.cprCertificationExpirationDate)
              : null,
            hasFirstAidCertification: getBooleanOptionLabel(d.firstAidCertification, []),
            firstAidCertificationType: d.firstAidCertification
              ? d.firstAidCertificationType?.toString() || ""
              : "",
            firstAidExpirationDate: d.firstAidCertification && d.firstAidCertificationExpirationDate
              ? new Date(d.firstAidCertificationExpirationDate)
              : null,
            hasPATS: getBooleanOptionLabel(d.patsCertification, assistanceTrainingOptions),
            patsCertificationDate: d.patsCertification && d.patsCertificationDate
              ? new Date(d.patsCertificationDate)
              : null,
            issuingAgency: d.patsCertification
              ? d.patsCertificateIssuingAgency || ""
              : "",
            hasBehaviorTraining: getBooleanOptionLabel(d.behaviorTraining, []),
            behaviorTrainingType: d.behaviorTraining
              ? d.behaviorTrainingName || ""
              : "",
            behaviorTrainingDate: d.behaviorTraining && d.behaviorTrainingCompletedDate
              ? new Date(d.behaviorTrainingCompletedDate)
              : null,
            stateClearancesStatus: (() => {
              if (d.childAbuseClearance && stateClearances.length > 0) {
                const matchingOption = stateClearances.find(opt => opt.formValueId === d.childAbuseClearance);
                console.log("🔍 State Clearances mapping:", {
                  apiValue: d.childAbuseClearance,
                  availableOptions: stateClearances.map(opt => ({ id: opt.formValueId, label: opt.label.en })),
                  matchingOption: matchingOption ? { id: matchingOption.formValueId, label: matchingOption.label.en } : null,
                  selectedLabel: matchingOption ? matchingOption.label.en : (stateClearances[0]?.label.en || "")
                });
                return matchingOption ? matchingOption.label.en : (stateClearances[0]?.label.en || "");
              }
              return stateClearances.length > 0 ? stateClearances[0].label.en : "";
            })(),
            stateClearancesDetails: d.childAbuseClearanceHeld || "",
          };

          console.log("🔍 Pre-populating skills form with existing data:", cleanedValues);
          console.log("🔍 Available options:", {
            cprTypes: certificationType.map(opt => ({ id: opt.formValueId, label: opt.label.en })),
            firstAidTypes: aidCertification.map(opt => ({ id: opt.formValueId, label: opt.label.en })),
            patsTypes: assistanceTrainingOptions.map(opt => ({ id: opt.formValueId, label: opt.label.en })),
            stateClearances: stateClearances.map(opt => ({ id: opt.formValueId, label: opt.label.en }))
          });

          formik.setValues(cleanedValues);
          console.log("🔍 After setting formik values:", formik.values);
        }
      } catch (error) {
        console.error("Failed to load driver details:", error);
        const errorMessage = error instanceof Error
          ? `Failed to load driver details: ${error.message}`
          : "Failed to load driver details. Please refresh.";
        toast.error(errorMessage);
      }
    };

    loadDriverDetails();
  }, [certificationType, aidCertification, assistanceTrainingOptions, stateClearances, formik.setValues]);

  if (isLoading) {
    return (
      <div style={{ padding: "2rem", textAlign: "center" }}>
        <div style={{ fontSize: "18px", color: "#666" }}>
          Loading final review...
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: "2rem", maxWidth: "1200px", margin: "0 auto" }}>
      <h3 style={{ fontSize: "1.5rem", marginBottom: "0.5rem" }}>
        Skills & Certifications (Bus Aide / Bus Assistant)
      </h3>
      <p style={{ marginBottom: "1.5rem" }}>
        Required fields are marked with *
      </p>

      <form onSubmit={formik.handleSubmit}>
        {/* CPR Section */}
        <div className="form-section" style={{ marginBottom: "2rem" }}>
          <label style={{ display: "block", marginBottom: "0.5rem" }}>
            Current CPR Certification? *
          </label>
          <RadioGroup
            name="hasCPRCertification"
            selectedValue={formik.values.hasCPRCertification}
            onChange={(value) =>
              formik.setFieldValue("hasCPRCertification", value)
            }
            options={["yes", "no"]}
          />
          {(() => {
            const shouldShowCPRFields = formik.values.hasCPRCertification &&
              (formik.values.hasCPRCertification.toLowerCase().includes("yes") ||
               formik.values.hasCPRCertification.toLowerCase().includes("certified"));
            return shouldShowCPRFields;
          })() && (
            <>
              <label style={{ display: "block", marginTop: "1rem" }}>
                CPR Certification Type *
              </label>
              <Dropdown
                options={certificationType.map(item => ({
                  value: item.formValueId.toString(),
                  label: item.label.en
                }))}
                value={formik.values.cprCertificationType}
                placeholder="Select Type"
                onChange={(value) => {
                  console.log("🔍 CPR Certification Type changed:", value);
                  formik.setFieldValue("cprCertificationType", value);
                }}
                error={formik.touched.cprCertificationType && formik.errors.cprCertificationType ?
                  formik.errors.cprCertificationType : undefined}
                name="cprCertificationType"
              />

              <label style={{ display: "block", marginTop: "1rem" }}>
                CPR Expiration Date *
              </label>
              <DateInput
                selected={formik.values.cprExpirationDate}
                onChange={(date) =>
                  formik.setFieldValue("cprExpirationDate", date)
                }
              />
               {formik.touched.cprExpirationDate &&
                formik.errors.cprExpirationDate && (
                  <div style={{fontSize:"12px",color:'red'}}>{formik.errors.cprExpirationDate}</div>
                )}
            </>
          )}
        </div>

        {/* First Aid Section */}
        <div className="form-section" style={{ marginBottom: "2rem" }}>
          <label style={{ display: "block", marginBottom: "0.5rem" }}>
            Current First Aid Certification? *
          </label>
          <RadioGroup
            name="hasFirstAidCertification"
            selectedValue={formik.values.hasFirstAidCertification}
            onChange={(value) =>
              formik.setFieldValue("hasFirstAidCertification", value)
            }
            options={["yes", "no"]}
          />
          {(() => {
            const shouldShowFirstAidFields = formik.values.hasFirstAidCertification &&
              (formik.values.hasFirstAidCertification.toLowerCase().includes("yes") ||
               formik.values.hasFirstAidCertification.toLowerCase().includes("certified"));
            return shouldShowFirstAidFields;
          })() && (
            <>
              <label style={{ display: "block", marginTop: "1rem" }}>
                First Aid Certification Type *
              </label>
              <Dropdown
                options={aidCertification.map(item => ({
                  value: item.formValueId.toString(),
                  label: item.label.en
                }))}
                value={formik.values.firstAidCertificationType}
                placeholder="Select Type"
                onChange={(value) => {
                  console.log("🔍 First Aid Certification Type changed:", value);
                  formik.setFieldValue("firstAidCertificationType", value);
                }}
                error={formik.touched.firstAidCertificationType && formik.errors.firstAidCertificationType ?
                  formik.errors.firstAidCertificationType : undefined}
                name="firstAidCertificationType"
              />

              <label style={{ display: "block", marginTop: "1rem" }}>
                First Aid Expiration Date *
              </label>
              <DateInput
                selected={formik.values.firstAidExpirationDate}
                onChange={(date) =>
                  formik.setFieldValue("firstAidExpirationDate", date)
                }
              />
               {formik.touched.firstAidExpirationDate &&
                formik.errors.firstAidExpirationDate && (
                  <div style={{fontSize:"12px",color:'red'}}>{formik.errors.firstAidExpirationDate}</div>
                )}
            </>
          )}
        </div>

        {/* PATS Section */}
        <div className="form-section" style={{ marginBottom: "2rem" }}>
          <label style={{ display: "block", marginBottom: "0.5rem" }}>
            PATS / PASS Certification?
          </label>
          <RadioGroup
            name="hasPATS"
            selectedValue={formik.values.hasPATS}
            onChange={(value) => {
              console.log("🔍 PATS radio button changed:", value);
              formik.setFieldValue("hasPATS", value);
            }}
            options={assistanceTrainingOptions.map((option) => option.label.en)}
          />
          {(() => {
            const shouldShowPATSFields = formik.values.hasPATS &&
              (formik.values.hasPATS.toLowerCase().includes("yes") ||
               formik.values.hasPATS.toLowerCase().includes("certified"));
            console.log("🔍 PATS conditional check:", {
              hasPATS: formik.values.hasPATS,
              shouldShowPATSFields,
              availableOptions: assistanceTrainingOptions.map(opt => opt.label.en)
            });
            return shouldShowPATSFields;
          })() && (
            <>
              <label style={{ display: "block", marginTop: "1rem" }}>
                Certification Date (Optional)
              </label>
              <DateInput
                selected={formik.values.patsCertificationDate}
                onChange={(date) =>
                  formik.setFieldValue("patsCertificationDate", date)
                }
              />
              <label style={{ display: "block", marginTop: "1rem" }}>
                Issuing Agency/Trainer (Optional)
              </label>
              <input
                type="text"
                name="issuingAgency"
                value={formik.values.issuingAgency}
                onChange={formik.handleChange}
                style={{
                  width: "100%",
                  padding: "0.5rem",
                  marginTop: "0.5rem",
                }}
              />
            </>
          )}
        </div>

        {/* Behavior Section */}
        <div className="form-section" style={{ marginBottom: "2rem" }}>
          <label style={{ display: "block", marginBottom: "0.5rem" }}>
            Behavior Management Training?
          </label>
          <RadioGroup
            name="hasBehaviorTraining"
            selectedValue={formik.values.hasBehaviorTraining}
            onChange={(value) =>
              formik.setFieldValue("hasBehaviorTraining", value)
            }
            options={["yes", "no"]}
          />

          {(() => {
            const shouldShowBehaviorFields = formik.values.hasBehaviorTraining &&
              (formik.values.hasBehaviorTraining.toLowerCase().includes("yes") ||
               formik.values.hasBehaviorTraining.toLowerCase().includes("certified"));
            return shouldShowBehaviorFields;
          })() && (
            <>
              <label style={{ display: "block", marginTop: "1rem" }}>
                Training Type/Name
              </label>
              <input
                type="text"
                name="behaviorTrainingType"
                value={formik.values.behaviorTrainingType}
                onChange={formik.handleChange}
                style={{
                  width: "100%",
                  padding: "0.5rem",
                  marginTop: "0.5rem",
                }}
              />
                {formik.touched.behaviorTrainingType &&
                formik.errors.behaviorTrainingType && (
                  <div style={{fontSize:"12px",color:'red'}}>{formik.errors.behaviorTrainingType}</div>
                )}
              <label style={{ display: "block", marginTop: "1rem" }}>
                Date Completed (Optional)
              </label>
              <DateInput
                selected={formik.values.behaviorTrainingDate}
                onChange={(date) =>
                  formik.setFieldValue("behaviorTrainingDate", date)
                }
              />
            </>
          )}
        </div>

        {/* State Clearance */}
        <div className="form-section" style={{ marginBottom: "2rem" }}>
          <label style={{ display: "block", marginBottom: "0.5rem" }}>
            State-Required Clearances / Training for School Personnel
          </label>
          <RadioGroup
            name="stateClearancesStatus"
            selectedValue={formik.values.stateClearancesStatus}
            onChange={(value) =>
              formik.setFieldValue("stateClearancesStatus", value)
            }
            options={stateClearances.map((options) => options.label.en)}
          />
          {(formik.values.stateClearancesStatus === "Yes, All Current" ||
            formik.values.stateClearancesStatus ===
              "Some Completed / Need Updates") && (
            <>
              <label style={{ display: "block", marginTop: "1rem" }}>
                Specify Clearances/Training Held
              </label>
              <textarea
                name="stateClearancesDetails"
                value={formik.values.stateClearancesDetails}
                onChange={formik.handleChange}
                style={{
                  width: "100%",
                  padding: "0.5rem",
                  marginTop: "0.5rem",
                  minHeight: "80px",
                }}
              />
                 {formik.touched.stateClearancesDetails &&
                formik.errors.stateClearancesDetails && (
                  <div style={{fontSize:"12px",color:'red'}}>{formik.errors.stateClearancesDetails}</div>
                )}
            </>
          )}
        </div>

        {/* Navigation Buttons */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginTop: "2rem",
            padding: "1.5rem",
            borderTop: "1px solid #e5e5e5",
          }}
        >
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              disabled={isLoading}
              style={{
                padding: "0.75rem 1.5rem",
                backgroundColor: "#6c757d",
                color: "white",
                border: "none",
                borderRadius: "4px",
                // cursor: isLoading ? "not-allowed" : "pointer",
                fontSize: "16px",
              }}
            >
              &lt; Back
            </button>
          )}

          <div style={{ display: "flex", gap: "1rem" }}>
            <button
              type="submit"
              disabled={isLoading}
              style={{
                padding: "0.75rem 1.5rem",
                backgroundColor: "#007bff",
                color: "white",
                border: "none",
                borderRadius: "4px",
                // cursor: isLoading ? "not-allowed" : "pointer",
                fontSize: "16px",
              }}
            >
              {isLoading ? "Saving..." : "Save & Continue (To Step 4: Docs) >"}
            </button>

            <button
              type="button"
              onClick={() => handleSubmit(formik.values, false)}
              disabled={isLoading}
              style={{
                padding: "0.75rem 1.5rem",
                backgroundColor: "#28a745",
                color: "white",
                border: "none",
                borderRadius: "4px",
                // cursor: isLoading ? "not-allowed" : "pointer",
                fontSize: "16px",
              }}
            >
              Save & Exit (Complete Later)
            </button>
          </div>
        </div>
        {/* <button type="submit">Submit Details </button> */}
      </form>
    </div>
  );
};

export default SkillsCertificate;
