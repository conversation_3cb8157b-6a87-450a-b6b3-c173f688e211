"use client";
import { useFormik } from "formik";
import { toast, ToastContainer } from "react-toastify";
import * as Yup from "yup";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { resetPassword } from "@/services/userService";
import css from "./ResetScreen.module.scss";
import "react-toastify/dist/ReactToastify.css";

const ResetScreen = () => {
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const searchParams = useSearchParams();
  const token = searchParams.get("token") || "";
  const email = searchParams.get("email") || "";
  const router = useRouter();
  
  const formik = useFormik({
    initialValues: {
      newPassword: "",
      confirmPassword: "",
    },
    validationSchema: Yup.object({
      newPassword: Yup.string()
        .min(8, "Password must be at least 8 characters")
        .matches(
          /^(?=.*[0-9])(?=.*[!@#$%^&*])/,
          "Password must contain at least one number and one special character (!@#$%^&*)"
        )
        .required("New Password is required"),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref("newPassword")], "Passwords must match")
        .required("Confirm Password is required"),
    }),
    onSubmit: async (values) => {
      console.log("Passwords submitted:", values);
      try {
        const res = await resetPassword({
          token,
          email,
          newPassword: values.newPassword,
        });

        if (res?.status) {
          formik.resetForm()
          toast.success("Password reset successfully!");
          router.push('/login/company')
        } else {
          toast.error(res?.message || "Failed to reset password");
        }
      } catch (error) {
        toast.error(`An error occurred. ${error instanceof Error ? error.message : ""}`);
      }
    },
  });

  return (
    <section className={css.registrationSection}>
      <div className={css.loginWrapper}>
        <h1>Reset Your Password</h1>
        <p>Please enter strong password, min 8 characters, 1 number, 1 symbol (e.g., !@#$).</p>

        <form onSubmit={formik.handleSubmit} className={css.registrationForm}>
          <div className={css.formGroup}>
            <div className={css.col6}>
              <label htmlFor="newPassword">Enter New Password</label>
              <input
                type={showNewPassword ? "text" : "password"}
                id="newPassword"
                name="newPassword"
                placeholder="Create password"
                onChange={formik.handleChange}
                // onBlur={formik.handleBlur}
                value={formik.values.newPassword}
              />
              <span
                className={css.showPassword}
                onClick={() => setShowNewPassword((prev) => !prev)}
              >
                {showNewPassword ? <FaEye /> : <FaEyeSlash />}
              </span>
              {formik.touched.newPassword && formik.errors.newPassword && (
                <div className={css.error}>{formik.errors.newPassword}</div>
              )}
            </div>

            <div className={css.col6}>
              <label htmlFor="confirmPassword">Confirm New Password</label>
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  id="confirmPassword"
                  name="confirmPassword"
                  placeholder="Create password"
                  onChange={formik.handleChange}
                  // onBlur={formik.handleBlur}
                  value={formik.values.confirmPassword}
                />
                <span
                  className={css.showPassword}
                  onClick={() => setShowConfirmPassword((prev) => !prev)}
                >
                  {showConfirmPassword ? <FaEye /> : <FaEyeSlash />}
                </span>
              {formik.touched.confirmPassword &&
                formik.errors.confirmPassword && (
                  <div className={css.error}>
                    {formik.errors.confirmPassword}
                  </div>
                )}
            </div>
          </div>

          <div className={`${css.formGroup} ${css.btnGroup}`}>
            <button type="submit" className={css.submitBtn}>Reset Password</button>
            <button type='button' onClick={()=>{router.push('/login/company')}} className={css.Cancel}>Cancel</button>
          </div>
        </form>
        <ToastContainer
          position="top-right"
          autoClose={3000}
          hideProgressBar={false}
        />
      </div>
    </section>
  );
};

export default ResetScreen;
