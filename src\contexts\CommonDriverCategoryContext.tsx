"use client";
import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { fetchDriverDetails, FetchDriverDetailsResponse } from "@/services/driverFormService";

type DriverDetails = FetchDriverDetailsResponse["data"]["driver"];

interface DriverData extends DriverDetails {
  currentStep: number;
  currentStage: number;
}

export type DriverCategoryType = 'driver' | 'school-bus-driver' | 'school-bus-aide' | 'nemt-driver';

interface CommonDriverCategoryContextType {
  driverData: DriverData | null;
  currentStep: number;
  setCurrentStep: (step: number) => void;
  completedSteps: number[];
  setCompletedSteps: (steps: number[]) => void;
  isLoading: boolean;
  refreshDriverData: () => Promise<void>;
  updateStepFromApiResponse: (apiResponse: FetchDriverDetailsResponse) => void;
  goToPreviousStep: () => void;
  canGoBack: boolean;
  categoryType: DriverCategoryType;
}

const CommonDriverCategoryContext = createContext<CommonDriverCategoryContextType | undefined>(undefined);

interface CommonDriverCategoryProviderProps {
  children: ReactNode;
  categoryType: DriverCategoryType;
}

export const CommonDriverCategoryProvider: React.FC<CommonDriverCategoryProviderProps> = ({ 
  children, 
  categoryType 
}) => {
  const [driverData, setDriverData] = useState<DriverData | null>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const fetchDriverData = async () => {
    try {
      setIsLoading(true);
      const response = await fetchDriverDetails();
      if (response?.status && response?.data) {
        const data = response.data;
        const driver = data.driver;

        const apiCurrentStep = data.currentStep || data?.currentStep || 1;
        const apiCurrentStage = data.currentStage || data?.currentStage || 1;

        const updatedDriverData = {
          ...driver,
          currentStep: apiCurrentStep,
          currentStage: apiCurrentStage
        };

        setDriverData(updatedDriverData);

        let userCurrentStep = 1;
        const completed: number[] = [];

        if (apiCurrentStage === 2) {
          userCurrentStep = 1;
        } else if (apiCurrentStage >= 3) {
          userCurrentStep = Math.min(apiCurrentStep + 1, 5);

          for (let i = 1; i < userCurrentStep; i++) {
            completed.push(i);
          }
        }

        setCurrentStep(userCurrentStep);
        setCompletedSteps(completed);

        console.log(`${categoryType} - Driver data loaded:`, {
          apiCurrentStep,
          apiCurrentStage,
          userCurrentStep,
          completed,
          categoryType
        });
      }
    } catch (error) {
      console.error(`${categoryType} - Error fetching driver data:`, error);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshDriverData = async () => {
    await fetchDriverData();
  };

  const updateStepFromApiResponse = (apiResponse: FetchDriverDetailsResponse) => {
    console.log(`${categoryType} - Updating step from API response:`, apiResponse);

    if (apiResponse?.data) {
      const data = apiResponse.data;
      const driver = data.driver;

      const newApiCurrentStep = data.currentStep || 1;
      const apiCurrentStage = data.currentStage || 1;

      console.log(`${categoryType} - PUT API Response - updating step:`, {
        newApiCurrentStep,
        apiCurrentStage,
        fullData: data
      });

      const nextStep = newApiCurrentStep + 1;

      const updatedDriverData = {
        ...driver,
        currentStep: newApiCurrentStep,
        currentStage: apiCurrentStage
      };

      setDriverData(updatedDriverData);
      
      // For regular driver category, use different logic
      if (categoryType === 'driver') {
        const newUserCurrentStep = nextStep <= 5 ? nextStep : newApiCurrentStep;
        setCurrentStep(newUserCurrentStep);

        const completed: number[] = [];
        if (apiCurrentStage >= 3) {
          for (let i = 1; i < newUserCurrentStep; i++) {
            completed.push(i);
          }
        }
        setCompletedSteps(completed);
      } else {
        // For other categories (school-bus-driver, etc.)
        const newUserCurrentStep = Math.min(nextStep, 5);
        setCurrentStep(newUserCurrentStep);

        const completed: number[] = [];
        for (let i = 1; i < newUserCurrentStep; i++) {
          completed.push(i);
        }
        setCompletedSteps(completed);
      }

      console.log(`${categoryType} - Step updated:`, {
        newStep: categoryType === 'driver' ? (nextStep <= 5 ? nextStep : newApiCurrentStep) : Math.min(nextStep, 5),
        completedSteps: completedSteps
      });
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      const previousStep = currentStep - 1;
      setCurrentStep(previousStep);

      const newCompleted: number[] = [];
      for (let i = 1; i < previousStep; i++) {
        newCompleted.push(i);
      }
      setCompletedSteps(newCompleted);

      console.log(`${categoryType} - Navigated to previous step:`, {
        from: currentStep,
        to: previousStep,
        newCompletedSteps: newCompleted
      });
    }
  };

  const canGoBack = currentStep > 1;

  useEffect(() => {
    fetchDriverData();
  }, [categoryType]);

  const contextValue: CommonDriverCategoryContextType = {
    driverData,
    currentStep,
    setCurrentStep,
    completedSteps,
    setCompletedSteps,
    isLoading,
    refreshDriverData,
    updateStepFromApiResponse,
    goToPreviousStep,
    canGoBack,
    categoryType,
  };

  return (
    <CommonDriverCategoryContext.Provider value={contextValue}>
      {children}
    </CommonDriverCategoryContext.Provider>
  );
};

export const useCommonDriverCategory = (): CommonDriverCategoryContextType => {
  const context = useContext(CommonDriverCategoryContext);
  if (context === undefined) {
    throw new Error('useCommonDriverCategory must be used within a CommonDriverCategoryProvider');
  }
  return context;
};

export const useDriverCategory = (): CommonDriverCategoryContextType => {
  const context = useCommonDriverCategory();
  if (context.categoryType !== 'driver') {
    console.warn('useDriverCategory used with non-driver category type');
  }
  return context;
};

export const useSchoolBusDriverCategory = (): CommonDriverCategoryContextType => {
  const context = useCommonDriverCategory();
  if (context.categoryType !== 'school-bus-driver') {
    console.warn('useSchoolBusDriverCategory used with non-school-bus-driver category type');
  }
  return context;
};

export const useSchoolBusAideCategory = (): CommonDriverCategoryContextType => {
  const context = useCommonDriverCategory();
  if (context.categoryType !== 'school-bus-aide') {
    console.warn('useSchoolBusAideCategory used with non-school-bus-aide category type');
  }
  return context;
};

export const useNemtDriverCategory = (): CommonDriverCategoryContextType => {
  const context = useCommonDriverCategory();
  if (context.categoryType !== 'nemt-driver') {
    console.warn('useNemtDriverCategory used with non-nemt-driver category type');
  }
  return context;
};
