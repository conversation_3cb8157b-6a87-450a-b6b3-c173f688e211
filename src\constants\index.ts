export const DriverCategories = [
  {
    url: 'https://api.dev.driverjobz.com/api/v1/form/form-fields/approximate-total-verifiable-miles-driven-commercial-cdl-driver-cdl',
    setterKey: 'setMilesOptions',
  },
  {
    url: 'https://api.dev.driverjobz.com/api/v1/form/form-fields/tractor-trailer-combinations-driver-cdl',
    setter<PERSON>ey: 'setTrailerOptions',
  },
  {
    url: 'https://api.dev.driverjobz.com/api/v1/form/form-fields/straight-trucks-requiring-cdl-driver-cdl',
    setterKey: 'setStraightTruckOptions',
  },
  {
    url: 'https://api.dev.driverjobz.com/api/v1/form/form-fields/buses-if-applicable-with-cdl-endorsements-driver-cdl',
    setter<PERSON>ey: 'setBusOptions',
  },
  {
    url: 'https://api.dev.driverjobz.com/api/v1/form/form-fields/other-cdl-required-equipment-driver-cdl',
    setter<PERSON><PERSON>: 'setOtherEquipmentOptions',
  },
  {
    url: 'https://api.dev.driverjobz.com/api/v1/form/form-fields/tanker-specifics-hauled-driver-cdl',
    setterKey: 'setTankerOptions',
  },
  {
    url: 'https://api.dev.driverjobz.com/api/v1/form/form-fields/transmission-type-experience-check-all-that-apply-driver-cdl',
    setterKey: 'setTransmissionOptions',
  },
  {
    url: 'https://api.dev.driverjobz.com/api/v1/form/form-fields/route-types-driven-check-all-that-apply-driver-cdl',
    setterKey: 'setRouteTypesOptions',
  },
  {
    url: 'https://api.dev.driverjobz.com/api/v1/form/form-fields/operating-area-condition-experience-check-all-that-apply-optional-driver-cdl',
    setterKey: 'setOperatingAreaOptions',
  },
];
