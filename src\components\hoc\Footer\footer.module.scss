@import '../../../styles/global.scss';

.footer {
    background-color: $dark-700;
    padding: 24px 0px;

    @include for-size(tablet-phone) {
        padding: 24px 0px 38px;
    }

    .container {
        @include container;
        display: flex;
        flex-wrap: wrap;
        flex-direction: column;
        gap: 150px;

        @include for-size(tablet-phone) {
            gap: 0px;
            padding: 0px 24px;
        }
    }

    ul {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;

        li {
            min-width: 20%;

            @include for-size(tablet-phone) {
                width: 100%;
            }

            a {
                color: $white;
                display: inline-flex;
                font-weight: 500;
                font-size: 16px;
                line-height: 22px;
                padding: 24px;
                width: 100%;

                @include for-size(tablet-phone) {
                    border-bottom: 1px solid rgba($color: $white, $alpha: 0.2);
                    padding: 8px 0px;
                }
            }
        }
    }

    p {
        border-top: 1px solid rgba($color: $white, $alpha: 0.2);
        color: $white;
        font-weight: 400;
        font-size: 17px;
        line-height: 28px;
        padding: 16px 0px;

        @include for-size(tablet-phone) {
            border: none;
            font-weight: 500;
            font-size: 16px;
            line-height: 22px;
            padding: 8px 0px;
        }
    }
}