import { FormField } from "@/types/form";
import { useParams } from "next/navigation";
import { FormikProps } from "formik";

interface CheckBoxProps {
  data: FormField;
  formik: FormikProps<Record<string, string | number | boolean>>;
}

const CheckBox: React.FC<CheckBoxProps> = ({ data, formik }) => {
  const params = useParams();
  const { lang } = params as { lang: "en" | "es" };

  const { label, columnName, description, values } = data;

  const fieldLabel = label ? label[lang] || label.en : columnName;
  const fieldDescription = description
    ? description[lang] || description.en
    : "";

    const selectedValues =
    Array.isArray(formik.values[columnName]) &&
    formik.values[columnName].every((v) => typeof v === "string")
      ? (formik.values[columnName] as string[])
      : [];
  

  const handleChange = (valueId: string | number) => {
    const newValue = selectedValues.includes(String(valueId))
      ? selectedValues.filter((val) => val !== String(valueId))
      : [...selectedValues, String(valueId)];

    formik.setFieldValue(columnName, newValue);
  };

  return (
    <div className="formGroup">
      <div className="col4">
        <label htmlFor={columnName}>{fieldLabel}</label>
        <span>{fieldDescription}</span>
      </div>
      <div className="col5">
        <ul className="checboxGroup">
          {values?.map((value, index) => {
            const valueIdStr = String(value.valueId);
            const isChecked = selectedValues.includes(valueIdStr);

            return (
              <li className="checkBox" key={index}>
                <input
                  type="checkbox"
                  name={columnName}
                  value={valueIdStr}
                  checked={isChecked}
                  onChange={() => handleChange(value.valueId)}
                />
                <span className="checkmark"></span>
                {value?.label[lang] || value?.label.en}
              </li>
            );
          })}
        </ul>
      </div>
    </div>
  );
};

export default CheckBox;
