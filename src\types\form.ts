// types/formTypes.ts

export interface FormField {
    columnName: string;
    label?: LocalizedText;
    description?: LocalizedText;
    type?: string;
    required?: boolean;
    placeholder?: string;
    options?: string[];
    componentType: ComponentType;
    values?: FieldOption[];
  }

  export interface FieldOption {
    valueId: string | number;
    label: LocalizedText;
  }
  
export interface LocalizedText {
    en: string;
    es: string;
  }

export interface FormStep {
  stepName: {
    en: string;
    es: string;
  };
  formFields: FormField[];
}

export type ComponentType = "text" | "checkbox" | "dropdown";

export type FormData = FormStep[];

export type FormikFieldValues = Record<string, string | number | boolean>;


