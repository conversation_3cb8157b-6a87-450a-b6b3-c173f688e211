import JobPosting from "@/components/JobPosting";
import { TransformedFields } from "@/types/jobpostingform";
import {
  fetchLanguages,
  fetchOtherLanguages,
} from "@/services/driverFormService";
import {
  getCompanyDetails,
  getJobFormFields,
  getJobPosting,
} from "@/services/jobPostingService";
import { getStates } from "@/services/locationService";
import { safeFetch } from "@/utils/utils";
import { cookies } from "next/headers";

export default async function JobPostingPage({
  params,
}: {
  params: Promise<{ lang: "en" | "es" }>;
}) {
  const { lang } = await params;
  const cookieStore = await cookies();
  const authTokenCookie = cookieStore.get("authToken");
  const token = authTokenCookie?.value ?? "";
  const jobIdCookie = cookieStore.get("jobId");
  const jobId = jobIdCookie?.value ?? "";

  const [stepNo, companyDetails, states, languages, otherLanguages] =
    await Promise.all([
      safeFetch(() => getJobPosting(token, jobId), null),
      safeFetch(() => getCompanyDetails(token), null),
      safeFetch(getStates, []),
      safeFetch(fetchLanguages, []),
      safeFetch(fetchOtherLanguages, []),
    ]);

  const currentStepNo = stepNo?.currentStep ?? 1;

  const stepsToPreload = [1, 2, 3, 4, 5];
  const formFieldsByStep = await Promise.all(
    stepsToPreload.map((step) =>
      safeFetch(() => getJobFormFields(step, lang), {})
    )
  );

  const formFieldsMap = stepsToPreload.reduce((acc, step, index) => {
    acc[step] = formFieldsByStep[index];
    return acc;
  }, {} as Record<number, TransformedFields>);

  const statesArray = states.map((list) => {
    return {
      label: lang === "en" ? list?.name?.en : list?.name?.es,
      value: list.stateId,
    };
  });

  const languagesArray = languages.map((list) => {
    return {
      label: lang === "en" ? list?.label?.en : list?.label?.es,
      value: list.formValueId.toString(),
    };
  });

  const otherLanguagesArray = otherLanguages.map((list) => {
    return {
      label: lang === "en" ? list?.label?.en : list?.label?.es,
      value: list.formValueId.toString(),
    };
  });

  return (
    <JobPosting
      lang={lang}
      formFields={formFieldsMap}
      currentStepNo={currentStepNo}
      languages={languagesArray}
      otherLanguages={otherLanguagesArray}
      companyDetails={companyDetails}
      states={statesArray}
    />
  );
}
