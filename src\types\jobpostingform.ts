import { Dispatch, SetStateAction } from "react";

export interface DropdownItem {
  label: string | number;
  value: string | number | boolean;
}

export type Label = { en: string; es: string };
export type TransformedFields = Record<
  string,
  { label: string; value: number | string }[]
>;

export type FormValueItem = {
  formValueId: number;
  label: Label;
};

export type FormFieldResponse = {
  [key: string]: {
    formValues: FormValueItem[];
  };
};

export type CategoryItem = {
  translations: {
    en?: {
      name?: string | number;
      transportationCategoryId?: string | number | boolean;
    };
    es?: {
      name?: string | number;
    };
  };
};

export interface JobPostingProps {
  lang: "en" | "es";
  formFields: Record<number, TransformedFields>;
  currentStepNo: number;
  languages: DropdownItem[];
  otherLanguages: DropdownItem[];
  companyDetails: CompanyDetails;
  states: DropdownItem[];
}

export interface BasicProps {
  states: DropdownItem[];
  lang?: "en" | "es";
  formFields: TransformedFields;
  setCurrentStep: Dispatch<SetStateAction<number>>;
  companyDetails: CompanyDetails;
}

export interface BasicFormValues extends Record<string, unknown> {
  jobTitle: number | string;
  otherJob: string;
  employmentTypes: (string | number)[];
  numberOfOpenings: number | string;
  locationCity: string;
  stateId: string;
  locationZipCode: string | number;
  routeType: (string | number)[];
  specificRoute: string;
  serviceArea: (string | number)[];
  specificState: string;
  specificServiceArea: string;
  hasPickUpLocation: boolean | string;
  firstPickUpLocation: string;
  pickUpLocationDistance: string;
  nationwide: string;
  statewide: string;
  homeTimeFrequency: string;
  customHomeTimeFrequency: string;
  workScheduleDays: (string | number)[];
  joinPeriod: string | number;
  targetStartDate: Date | string;
}

export interface PayProps {
  lang?: "en" | "es";
  formFields: TransformedFields;
  setCurrentStep: Dispatch<SetStateAction<number>>;
}

export interface PayStructureForm extends Record<string, unknown> {
  type?: string;
  startingCpm: string | number;
  cpmRangeMin: string | number;
  cpmRangeMax: string | number;
  mileageCalculationMethod: string | number;
  startingHourlyRate: string | number;
  hourlyRateRangeMin: string | number;
  hourlyRateRangeMax: string | number;
  overtimeOption: string | number;
  overtimeAfterHours: string | number;
  payPerDay: string | number;
  typicalHoursPerDay: string;
  payPerWeek: string | number;
  typicalHoursDaysWeek: string;
  percentageRate: string | number;
  percentageBasedOn: string;
  payPerLoad: string | number;
  additionalPayFactors: string;
  annualSalary: string | number;
  salaryRangeMin: string | number;
  salaryRangeMax: string | number;
  combinationDescription: string;
  minimumGuaranteedPay: string | number;
  minimumGuaranteedPayPeriod: string | number;
  averageEarningsEstimate: string;
  averageEarningsEstimate1: string;
  estimatePeriod: string;
  paySchedule: string | number;
  signOnBonusOffered: boolean | string;
  signOnBonusAmount: string | number;
  signOnBonusPayoutStructure: string;
  bonusOffered: (string | number)[];
  detentionPayRate: string | number;
  detentionAfterHours: string | number;
  layoverPay: string | number;
  stopPayPerStop: string | number;
  stopPayExcludeFirstLast: (string | boolean)[];
  otherBonusDescription: string;
  benefitsPackage: string | boolean;
  benefits: (string | number)[];
  otherBenefit: string | number;
  driverPerksAndPrograms: (string | number)[];
  orientationRequired: boolean | string;
  orientationDuration: string | number;
  orientationDurationUnit: string | number;
  orientationLocation: string;
  paidOrientation: boolean | string;
  orientationExpense: (string | number)[];
  orientationPayRate: string | number;
  orientationPayUnit: string | number;
};

export interface PayloadData {
  startingCpm?: string | number;
  cpmRangeMin?: string | number;
  cpmRangeMax?: string | number;
  mileageCalculationMethod?: string | number;
  startingHourlyRate?: string | number;
  overtimeOption?: string | number;
  overtimeAfterHours?: string | number;
  hourlyRateRangeMin?: string | number;
  hourlyRateRangeMax?: string | number;
  payPerDay?: string | number;
  typicalHoursPerDay?: string | number;
  payPerWeek?: string | number;
  typicalHoursDaysWeek?: string | number;
  percentageRate?: string | number;
  percentageBasedOn?: string | number;
  payPerLoad?: string | number;
  additionalPayFactors?: string | number;
  annualSalary?: string | number;
  salaryRangeMin?: string | number;
  salaryRangeMax?: string | number;
  combinationDescription?: string | number;
  earningEstimate?: string | number;
  signOnBonusAmount?: string | number;
  signOnBonusPayoutStructure?: string | number;
  benefits?: (string | number)[];
  bonusOffered?: (string | number)[];
  detentionPayRate?: string | number;
  detentionAfterHours?: string | number;
  layoverPay?: string | number;
  stopPayPerStop?: string | number;
  stopPayExcludeFirstLast?: string | boolean;
  otherBonusDescription?: string | number;
  orientationDuration?: string | number;
  orientationDurationUnit?: string | number;
  orientationLocation?: string | number;
  orientationExpense?: (string | number)[];
  orientationPayRate?: string | number;
  orientationPayUnit?: string | number;
  minimumGuaranteedPay?: string | number;
  minimumGuaranteedPayPeriod?: string | number;
  paySchedule?: string | number;
  signOnBonusOffered?: string | boolean;
  orientationRequired?: string | boolean;
  benefitsPackage?: string | boolean;
  paidOrientation?: string | boolean;
}

export interface VehicleProps {
  lang?: "en" | "es";
  formFields: TransformedFields;
  setCurrentStep: Dispatch<SetStateAction<number>>;
}

export interface VehicleFormValues extends Record<string, unknown> {
  vehicleType: (string | number)[],
  otherVehicleText: string | number;
  vehicleTypeText: string | number,
  truckAge: string | number,
  transmission: string | number,
  truckAssignment: string | number,
  governedSpeed: string | number,
  truckAmenities: (string | number)[],
  truckAmenitiesText: string,
  inCabTech: (string | number)[],
  inCabTechText: string,
  eldSystems: (string | number)[],
  eldSystemsText: string | number;
  trailerTypes: (string | number)[],
  trailerLength: string | number,
  tankerContents: string | number,
  otherTrailer: string | number,
  trailerAge: string | number,
  freightTypes: (string | number)[],
  freightTypesText: string,
  loadingReqs: (string | number)[],
  dropHookPercent: string | number,
  avgLoadWeight: string | number
};

export interface QualifyProps {
  lang?: "en" | "es";
  formFields: TransformedFields;
  setCurrentStep: Dispatch<SetStateAction<number>>;
  languages: DropdownItem[];
  otherLanguages: DropdownItem[];
}

export interface DriverLanguage {
  languageId: string | number;
  proficiency: string;
}

export interface DrivingHistoryEntry {
  allowed?: boolean | string;
  count: string | number;
  years: string | number;
}

export interface QualificationForm extends Record<string, unknown> {
  cdlClass: string | number;
  dotMedicalCard: boolean | string;
  endorsements: (string | number)[];
  airbrakeCertRequired: boolean | string;
  twicCardRequired: boolean | string;
  passportRequired: boolean | string;
  experienceMonths: string | number;
  preferredEquipment: (string | number)[];
  preferredRoutes: (string | number)[];
  willingToTrain: boolean | string;
  trainingProgram: string;
  numberOfMovingViolations: DrivingHistoryEntry;
  numberOfAccidents: DrivingHistoryEntry;
  preventableAccidentsYears: DrivingHistoryEntry;
  seriousViolationsYears: DrivingHistoryEntry;
  duiYears: DrivingHistoryEntry;
  suspensionYears: DrivingHistoryEntry;
  drivingRecordOther: string;
  screeningChecks: (string | number)[];
  physicalRequirements: (string | number)[];
  otherPhysicalRequirements: string | number;
  physicalLiftingLimit: string | number;
  driverLanguages: DriverLanguage[];
  minAge: string | number;
  isOtherRequirements: boolean | string;
  otherRequirements: (string | number)[];
  otherRequirementsText: string | number;
}

export interface Company {
  name?: string;
  email?: string;
  phoneNumber?: string;
  companyId?: number;
}

export interface CompanyDetails {
  company?: Company;
}

export interface DescProps {
  lang?: "en" | "es";
  formFields: TransformedFields;
  companyDetails: CompanyDetails;
  setCurrentStep: Dispatch<SetStateAction<number>>;
}

export interface DescriptionFormValues extends Record<string, unknown> {
  jobDescription: string;
  keyResponsibilities: (string | number)[];
  applicationMethod: string;
  applicationDocs: string | boolean;
  additionalNotes: string;
  interviewSteps: (string | number)[];
  hiringProcessTimeline: string | number;
  joiningDate: string;
  contactPersonName: string | undefined;
  contactPersonEmail: string | undefined;
  contactPersonPhone: string | number | undefined;
  visibility: string | boolean;
  postingDurationDays: number;
  eeoConfirmed: (string | number)[];
};

export type CleanConfig = {
  [field: string]: {
    outputKey?: string;
    addField?: string | number;
    addField1?: string | number;
    field1DependsOn?: string;
    string?: boolean;
  };
};

export type Values = Record<string, any>;

export type FieldCheck = {
  fields: string[];
  condition: (values: Values) => boolean;
}
