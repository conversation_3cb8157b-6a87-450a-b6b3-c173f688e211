@import "../../../../styles/global.scss";

.checkboxWrapper {
    .labelCheckbox {
        display: flex;
        font-weight: 700;
        font-size: 14px;
        line-height: 22px;
        margin-bottom: 16px;
        gap: 4px;

        .desc {
            font-weight: 400;
        }
        .important {
            color: $red;
        }
    }

    .checkboxList {
        display: flex;
        flex-wrap: wrap;
        column-gap: 24px;
        list-style: none;
        padding: 0;

        li {
            display: flex;
            column-gap: 11px;
            width: calc((100% - 96px) / 5);
            font-size: 14px;
            color: $gray-secondary;
            margin-bottom: 16px;
            position: relative;
            
            .checkboxContainer {
                user-select: none;
                padding-left: 30px;
            }

            input {
                position: absolute;
                opacity: 0;
                height: 0;
                width: 0;
            }

            .customCheckbox {
                position: absolute;
                left: 0;
                top: 0;
                height: 18px;
                width: 18px;
                border: 2px solid $gray-secondary;
                border-radius: 2px;
                cursor: pointer;
                &::after {
                    content: "";
                    position: absolute;
                    display: none;
                    left: 5px;
                    top: 1px;
                    width: 4px;
                    height: 9px;
                    border: solid white;
                    border-width: 0 2px 2px 0;
                    transform: rotate(45deg);
                }
            }

            input:checked~.customCheckbox {
                background-color: $gray-secondary;
                &::after {
                    display: block;
                }
            }
        }
    }
}

.fullWidthAdjust li {
    width: 100% !important;
    span {
       color: #000000;
    }
}

.checkboxWidthAdjust {
    width: 33%;

    li {
        width: calc((100% - 48px) / 3) !important;
    }
}

.labelRadioWidth {
    width: 100%;
}