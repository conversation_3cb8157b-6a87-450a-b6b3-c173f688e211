@import '../../../styles/global.scss';

.registrationSteps {
    margin-bottom: 24px;
    
    h1 {
        color: $dark;
        font-weight: 500;
        font-size: 23px;
        line-height: 140%;
        display: flex;
        align-items: center;
        gap: 8px;

        button {
            border: none;
            background-color: transparent;
            width: 16px;
            height: 16px;
            display: inline-flex;
            align-items: center;
            justify-content: center;

            img {
                width: 16px;
            }
        }
    }
}

.allSteps {
    display: flex;
    margin-top: 24px;

    li {
        width: calc(100% / 5);
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 2px;
        position: relative;

        &:before,
        &:after {
            background-color: $primary;
            content: '';
            width: 50%;
            height: 1px;
            position: absolute;
            top: 10px;
            z-index: 1;
        }

        &:before {
            left: 0px;
        }

        &:after {
            right: 0px;
        }

        &:first-child {
            &:before {
                display: none;
            }
        }

        &:last-child {
            &:after {
                display: none;
            }
        }

        figure {
            background-color: $white;
            border: 1px solid #C6C6C6;
            border-radius: 50%;
            line-height: 0px;
            width: 20px;
            height: 20px;
            position: relative;
            z-index: 3;

            img {
                width: 100%;
            }
        }

        p {
            color: #6F6F6F;
            font-size: 14px;
            line-height: 20px;
            font-weight: 400;
        }

        &.active {
            figure {
                border-color: #1D831B;
                background-color: #1D831B;
            }

            span {
                color: #414141;
            }

            p {
                color: $dark-900;
            }
        }
    }


    .step {
        width: calc(100% - 72px);
    }

    span {
        color: #A8ADB7;
        font-weight: 400;
        font-size: 19px;
        line-height: 160%;
    }

    p {
        color: #7C8493;
        font-weight: 500;
        font-size: 19px;
        line-height: 140%;
    }
}