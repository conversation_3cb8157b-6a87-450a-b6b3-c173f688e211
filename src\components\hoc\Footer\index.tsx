"use client"
import Link from "next/link";
import css from "./footer.module.scss";

const Footer: React.FC = () => {
//   const handleSubmit = (event: React.FormEvent) => {
//     event.preventDefault();
//     console.log('Submit button clicked!');
// };

    return (
    <footer className={css.footer}>
      <div className={css.container}>
        <ul>
          <li>
            <Link href="/">About us</Link>
          </li>
          <li>
            <Link href="/">Blog</Link>
          </li>
          <li>
            <Link href="/">Contact </Link>
          </li>
          <li>
            <Link href="/">Privacy</Link>
          </li>
          <li>
            <Link href="/">Terms</Link>
          </li>
        </ul>
        <p>&copy;2025 Driverjobz</p>
      </div> 
    </footer>
  );
};

export default Footer;
