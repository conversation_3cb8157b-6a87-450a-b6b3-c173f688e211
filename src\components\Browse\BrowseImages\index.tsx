'use client';

import { ReactNode, useRef, useState } from 'react';
import css from '../../RegistrationForm/Employer02/employerRegistration.module.scss';
import { toast, ToastContainer } from "react-toastify";

type BrowseImagesProps = {
  setImagePath: (path: string) => void;
  label: ReactNode;
  toolTip:string
};

const BrowseImages: React.FC<BrowseImagesProps> = ({ setImagePath, label, toolTip='' }) => {
  // const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState('');
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const MAX_FILE_SIZE = 2 * 1024 * 1024;

  const isValidImageType = (type: string) => type === 'image/jpeg' || type === 'image/png';
  const isValidFileSize = (size: number) => size <= MAX_FILE_SIZE;

  const validateFile = (file: File) => {
    if (!isValidImageType(file.type)) {
      setError('Only JPG and PNG files are allowed');
      return false;
    }
    if (!isValidFileSize(file.size)) {
      setError('File size should not exceed 2MB');
      return false;
    }
    setError('');
    return true;
  };

  const handleUpload = async (selectedFile: File) => {
    setUploading(true);
    try {
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_V1}files/generate-presigned-url`, {
        method: 'GET',
        headers: {
          'Content-Type': selectedFile.type,
        },
      });

      const data = await res.json();
      const { uploadUrl, path } = data?.data;

      const uploadRes = await fetch(uploadUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': selectedFile.type,
        },
        body: selectedFile,
      });

      if (uploadRes.ok) {
        setImagePath(path);
        toast.success('Upload successful!');
      } else {
        throw new Error('Upload failed');
      }
    } catch (err) {
      console.error(err);
      toast.error('Upload failed!');
    } finally {
      setUploading(false);
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile && validateFile(droppedFile)) {
      // setFile(droppedFile);
      await handleUpload(droppedFile);
    } else {
      // setFile(null);
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile && validateFile(selectedFile)) {
      // setFile(selectedFile);
      await handleUpload(selectedFile);
    } else {
      // setFile(null);
    }
    e.target.value = '';
  };

  return (
    <div className={css.formRow}>
      <div className={css.col02}>
        <div className={`${css.labelDiv} ${css.mb8}`}>
          <label>
            {label}
            {toolTip!=='' && <span className={css.tooltipIcon}>
              <img src="/images/icons/icon-info.svg" alt="" />
              <span className={css.tooltip}>
                {toolTip}
              </span>
            </span>}
          </label>
        </div>

        <div
          className={css.BrowseUpload}
          onDrop={handleDrop}
          onDragOver={(e) => e.preventDefault()}
          onClick={() => fileInputRef.current?.click()}
          style={{ cursor: 'pointer' }}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="image/jpeg, image/png"
            onChange={handleFileChange}
            style={{ display: 'none' }}
          />

          <div className={css.dragDrop}>
            <img src="/images/icons/icon-drag-drop.svg" alt="" />
            <span>Drag & drop your file here</span>
          </div>

          <div className={css.or}>or</div>

          <div className={css.browseFile}>
            <button type="button">Browse File</button>
          </div>
        </div>

        <p className={css.supportsType}>
          Supported file types:&nbsp;<strong>JPG, PNG</strong>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;
          Max file size:&nbsp;<strong>2MB</strong>&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;
          Recommended size:&nbsp;<strong>300X300 px</strong>
        </p>

        {/* {file && (
          <div style={{ marginTop: '0.5rem', display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <p style={{ margin: 0 }}>
              <strong>Selected:</strong> {file.name}
            </p>
            <button
              type="button"
              onClick={() => {
                setFile(null);
                setImagePath('');
              }}
              style={{
                background: 'transparent',
                border: 'none',
                color: '#ff0000',
                fontSize: '1.2rem',
                cursor: 'pointer',
              }}
              aria-label="Remove file"
            >
              ×
            </button>
          </div>
        )} */}
        {error && <p style={{ color: 'red', marginTop: '0.5rem', fontSize:12 ,fontWeight:400 }}>{error}</p>}
        {uploading && <p style={{ marginTop: '0.5rem' }}>Uploading...</p>}
      </div>

      <ToastContainer
        position="top-right"
        autoClose={3000}
        hideProgressBar={false}
      />
    </div>
  );
};

export default BrowseImages;
