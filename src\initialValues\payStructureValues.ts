export const payFormCDL = {
  type: "",
  startingCpm: "",
  cpmRangeMin: "",
  cpmRangeMax: "",
  mileageCalculationMethod: "",
  startingHourlyRate: "",
  hourlyRateRangeMin: "",
  hourlyRateRangeMax: "",
  overtimeOption: "",
  overtimeAfterHours: "",
  payPerDay: "",
  typicalHoursPerDay: "",
  payPerWeek: "",
  typicalHoursDaysWeek: "",
  percentageRate: "",
  percentageBasedOn: "",
  payPerLoad: "",
  additionalPayFactors: "",
  annualSalary: "",
  salaryRangeMin: "",
  salaryRangeMax: "",
  combinationDescription: "",
  minimumGuaranteedPay: "",
  minimumGuaranteedPayPeriod: "",
  averageEarningsEstimate: "",
  averageEarningsEstimate1: "",
  estimatePeriod: "",
  paySchedule: "",
  signOnBonusOffered: "",
  signOnBonusAmount: "",
  signOnBonusPayoutStructure: "",
  bonusOffered: [],
  detentionPayRate: "",
  detentionAfterHours: "",
  layoverPay: "",
  stopPayPerStop: "",
  stopPayExcludeFirstLast: [],
  otherBonusDescription: "",
  benefitsPackage: "",
  benefits: [],
  otherBenefit: "",
  driverPerksAndPrograms: [],
  orientationRequired: "",
  orientationDuration: "",
  orientationDurationUnit: "",
  orientationLocation: "",
  paidOrientation: "",
  orientationExpense: [],
  orientationPayRate: "",
  orientationPayUnit: "",
};
