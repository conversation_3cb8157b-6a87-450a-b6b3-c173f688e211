import {
  FormFieldResponse,
  TransformedFields,
} from "@/types/jobpostingform";
import { clearStorage, slugPayload } from "@/utils/utils";
import { getCookie, setCookie } from "cookies-next";
import { AppRouterInstance } from "next/dist/shared/lib/app-router-context.shared-runtime";
import { Dispatch, SetStateAction } from "react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const token = getCookie("authToken");

const headers = {
  "Content-Type": "application/json",
  ...(token ? { Authorization: `${token}` } : {}),
};

// server side call
export const getJobFormFields = async (currentStep: number, lang: string) => {
  const apiUrl = `${process.env.NEXT_PUBLIC_API_V1}form/form-fields/get-all-by-slug`;
  try {
    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        slugs: slugPayload[currentStep],
        categorySlug: "cdl-a-b-commercial-truck-driver",
        type: "job",
      }),
    });
    const data = await response.json();

    const formFields: FormFieldResponse = data?.data;

    const formRes = Object.entries(formFields).reduce((acc, [key, value]) => {
      acc[key] = value.formValues.map((list) => ({
        label: lang === "en" ? list?.label?.en : list?.label?.es,
        value: list?.formValueId,
      }));
      return acc;
    }, {} as TransformedFields);

    return formRes;
  } catch (error) {
    console.error("Failed to fetch form fields:", error);
    return {} as TransformedFields;
  }
};

export const getCompanyDetails = async (authToken?: string) => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_V1}/employer/details`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          ...(authToken ? { Authorization: `${authToken}` } : {}),
        },
      }
    );

    const data = await res.json();
    return data?.data;
  } catch (error) {
    console.error("Failed to fetch company:", error);
  }
};

export const jobPostingUpdate = async <T = unknown>(
  payload: object,
  id?: number | string | undefined | null,
  setCurrentStep?: Dispatch<SetStateAction<number>>,
  stepNo?: number,
  isDraft?: boolean | string,
  router?: AppRouterInstance
): Promise<T | null> => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_V1}jobs${id ? `/${id}` : ""}`,
      {
        method: id ? "PUT" : "POST",
        headers,
        body: JSON.stringify(payload),
      }
    );

    console.log(response, "response")
    if (!response.ok) {
      toast.dismiss();
      toast.error(`Something went wrong, please try again later`);
    }

    const data = await response.json();

    if (data?.status && data?.message == "SUCCESS") {
      if(isDraft || stepNo === 0) {
        toast.dismiss();
        toast.success(isDraft ? `Job posting has been saved successfully` : `Job posting has been submitted successfully`);
        router?.push("/");
        clearStorage();
      } else {
        if (setCurrentStep && stepNo) setCurrentStep(stepNo);
        if(stepNo === 2) {
          setCookie("jobId", data.data.jobPost.jobPostingId);
        }
      }
    }

    return data?.data as T;
  } catch (error) {
    console.error("Failed to fetch:", error);
    return null;
  }
};

// server side call
export const getJobPosting = async (cookieToken?: string, jobId?: string | number | null) => {
  if (jobId) {
    try {
      const res = await fetch(
        `${process.env.NEXT_PUBLIC_API_V1}/jobs?jobPostingId=${jobId}`,
        {
          method: "GET",
          headers: cookieToken ? {
            "Content-Type": "application/json",
            ...(cookieToken ? { Authorization: `${cookieToken}` } : {}),
          } : headers,
        }
      );

      const data = await res.json();
      return data?.data;
    } catch (error) {
      console.error("Failed to fetch:", error);
    }
  }
};
