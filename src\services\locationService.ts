// services/locationService.ts

export interface State {
  stateId: number | string;
  slug: string;
  name: {
    en: string;
    es: string;
  };
}

export interface City {
  cityId: number | string;
  slug: string;
  name: {
    en: string;
    es: string;
  };
}

interface GetStatesApiResponse {
  status: boolean;
  message?: string;
  data: {
    states: State[];
  };
}

interface GetCitiesByStateSlugApiResponse {
  status: boolean;
  message?: string;
  data: {
    states: {
      stateId: number | string;
      slug: string;
      cities: City[];
    };
  };
}

// 🔹 GET all states
export const getStates = async (): Promise<State[]> => {
  const apiUrl = `${process.env.NEXT_PUBLIC_API_V1}locations/states`;

  try {
    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch states: ${response.status}`);
    }

    const result: GetStatesApiResponse = await response.json();

    if (!result.status) {
      throw new Error(`API Error: ${result.message}`);
    }

    return result.data.states;
  } catch (error) {
    console.error("Error in getStates:", error);
    return [];
  }
};

// 🔹 GET cities by state slug
export const getCitiesByStateSlug = async (
  slug: string
): Promise<City[]> => {
  const apiUrl = `${process.env.NEXT_PUBLIC_API_V1}locations/states/${slug}`;

  try {
    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch cities for state: ${response.status}`);
    }

    const result: GetCitiesByStateSlugApiResponse = await response.json();

    if (!result.status || !result.data.states?.cities) {
      return [];
    }

    return result.data.states.cities;
  } catch (error) {
    console.error("Error in getCitiesByStateSlug:", error);
    return [];
  }
};
