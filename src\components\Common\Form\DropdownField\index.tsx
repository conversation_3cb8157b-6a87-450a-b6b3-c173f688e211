import React, { useEffect, useRef, useState } from "react";
import styles from "./DropdownField.module.scss";
import { DropdownItem } from "@/types/jobpostingform";
import Image from "next/image";
import { FormikProps, getIn } from "formik";

interface DropdownProps<T extends Record<string, unknown>> {
  className?: string;
  label?: string;
  fieldName: string;
  defaultLabel?: string;
  hide?: boolean;
  dropdownArray: DropdownItem[];
  formik: FormikProps<T>;
  disabled?: boolean;
}

const DropdownField = <T extends Record<string, unknown>> ({
  className = "",
  label = "",
  fieldName = "",
  defaultLabel = "",
  hide = false,
  dropdownArray,
  formik,
  disabled = false
}: DropdownProps<T>) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const selectedValue = getIn(formik.values, fieldName);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);

    // cleanup
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const clickHandler = (valueId: string | number | boolean) => {
    formik.setFieldValue(fieldName, valueId);
    setIsDropdownOpen(false);
  }

  return (
    <div className={`${styles.columnField} ${styles[className]}`}>
      {label && <label htmlFor={fieldName}>{label} {!hide && <span>*</span>}</label>}
      <div className={styles.dropdown} ref={dropdownRef}>
        <button
          type="button"
          className={styles.dropdownToggle}
          onClick={() => setIsDropdownOpen((prev) => !prev)}
          disabled={disabled}
          name={fieldName}
        >
          {dropdownArray?.find((item) => item.value == selectedValue)?.label || defaultLabel || "Select"}
            <span className={`${styles.iconWrapper} ${isDropdownOpen ? styles.rotate : ""}`}>
              <Image src="/images/icons/icon-down-arrow.svg" alt="arrow" width={16} height={16} />
            </span>
        </button>

        {isDropdownOpen && (
          <div className={styles.dropdownMenu}>
            {dropdownArray?.map((list) => (
              <button
                type="button"
                key={list?.label}
                className={styles.dropdownItem}
                onClick={() => clickHandler(list.value)}
              >
                {list?.label}
              </button>
            ))}
          </div>
        )}
      </div>
      {getIn(formik.touched, fieldName) && typeof getIn(formik.errors, fieldName) === "string" && (
        <div className="error_msg">
          {getIn(formik.errors, fieldName)}
        </div>
      )}
    </div>
  );
};

export default DropdownField;
