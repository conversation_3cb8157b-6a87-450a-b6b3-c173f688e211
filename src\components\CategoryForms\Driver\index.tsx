'use client'
import AdditionalDocument from "./AdditionalDocument";
import Consent from "./Consents";
import DrivingExperience from "./DrivingExperience";
import EmploymentHistory from "./EmploymentHistory";
import MedicalCertification from "./MedicalCertifications";
import { useDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import css from "./driver.module.scss";
import StepIndicator from "@/components/Common/StepIndicator";

const DriverCategoryForm = () => {
  const { currentStep, completedSteps, isLoading, } = useDriverCategory();

  const categorySteps = [
    { id: 1, title: "Driving Experience", component: DrivingExperience },
    { id: 2, title: "Employment History", component: EmploymentHistory },
    { id: 3, title: "Medical Certification", component: MedicalCertification },
    { id: 4, title: "Additional Documents", component: AdditionalDocument },
    { id: 5, title: "Consent", component: Consent },
  ];

  if (isLoading) {
    return (
      <div style={{ padding: "2rem", textAlign: "center" }}>
        <div>Loading...</div>
      </div>
    );
  }

  const currentStepData = categorySteps.find(step => step.id === currentStep);
  const CurrentComponent = currentStepData?.component;

  return (
    <div className={css.driverCategoryForm}>
      <div className={css.container}>
    

        <StepIndicator
          categorySteps={categorySteps}
          currentStep={currentStep}
          completedSteps={completedSteps}
        />

        <h6 className={css.required}>
          Required fields are marked with <sup>*</sup>
        </h6>

        <div className={css.stepContent}>
          <h2>{currentStepData?.title}</h2>
          {CurrentComponent && <CurrentComponent />}
        </div>
      </div>
    </div>
  );
};

export default DriverCategoryForm;
