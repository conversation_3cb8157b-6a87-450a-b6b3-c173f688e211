import BrowseFiles from '@/components/Browse/BrowseFiles'
import css from '../employerRegistration.module.scss'
import { useFormik } from 'formik';
import * as Yup from "yup";
import { useRouter } from 'next/navigation';
import { toast } from "react-toastify";
import { submitCompanyDetails } from '@/services/employerFormService';
import Link from 'next/link';
import { FileItem, FormInitialValues } from '../types';

type VerificationProcessProps = {
    formInitialValues: FormInitialValues;
    dotFiles: FileItem[];
    mcFiles: FileItem[];
    enDocFiles: FileItem[];
    addDocFiles: FileItem[];
    currentStep: number;
    fmcsaVerified: boolean | null;
    setDotFiles: (files: FileItem[]) => void;
    setMcFiles: (files: FileItem[]) => void;
};

const VerificationProcess: React.FC<VerificationProcessProps> = ({ formInitialValues, dotFiles, mcFiles, currentStep, fmcsaVerified, setDotFiles, addDocFiles, setMcFiles, enDocFiles }) => {
    const router = useRouter();
    const formikVerification = useFormik({
        enableReinitialize: true,
        initialValues: {
            usDotNumber: formInitialValues?.company?.usDotNumber || null,
            mcNumber: formInitialValues?.company?.mcNumber || null,
        },
        validationSchema: Yup.object({
            usDotNumber: Yup.string().nullable(),
            mcNumber: Yup.string().nullable(),
        }),
        onSubmit: async () => {
            const hasDotFiles = dotFiles.length > 0;
            const hasMcFiles = mcFiles.length > 0;

            const businessRegistrationFiles = hasDotFiles
                ? dotFiles
                : formInitialValues?.fileUploads?.docFiles?.businessRegistration || [];

            const einLetterFiles = hasMcFiles
                ? mcFiles
                : formInitialValues?.fileUploads?.docFiles?.einLetter || [];

            if (
                businessRegistrationFiles.length === 0 ||
                einLetterFiles.length === 0
            ) {
                toast.error("Please upload both required documents before submitting.");
                return;
            }

            const step = 4
            const payload = {
                ...(step >= currentStep && { currentStep: step }),
                currentStage: 2,
                company: {
                    // usDotNumber: values.usDotNumber,
                    // mcNumber: values.mcNumber,
                    docFiles: {
                        businessRegistration: dotFiles,
                        einLetter: mcFiles,
                    },
                },
            };
            try {
                const success = await submitCompanyDetails(payload);
                if (success) {
                    if (hasDotFiles || hasMcFiles) {
                        toast.success("Verification details submitted");
                    }
                    setTimeout(() => {
                        router.push("/");
                    }, 2000)
                } else {
                    toast.error("Failed to submit verification details");
                }
            } catch (error) {
                console.error(error);
                toast.error("Something went wrong!");
            }
        },
    });
    return (
        <form
            className={css.commonForm}
            onSubmit={formikVerification.handleSubmit}
        >
            {formInitialValues?.company?.usDotNumber && (
                <div className={css.formRow}>
                    <div className={css.col03}>
                        <div className={css.labelDiv}>
                            <label>
                                US DOT Number <span>(If applicable)</span>
                                <sup>*</sup>
                            </label>
                        </div>
                        <input
                            type="text"
                            name="usDotNumber"
                            placeholder="US DOT Number"
                            value={formInitialValues?.company?.usDotNumber || ""}
                            disabled
                        />
                        <span className={css.FMCSA}>Check&nbsp;<Link
                                href="https://safer.fmcsa.dot.gov/"
                            >
                                 FMCSA SAFER 
                            </Link>&nbsp;website
                        </span>
                    </div>

                    <div className={css.col03}>
                        <div className={css.labelDiv}>
                            <label>
                                MC Number (Motor Carrier) <span>(If applicable)</span>
                            </label>
                        </div>
                        <input
                            type="text"
                            name="mcNumber"
                            placeholder="MC Number"
                            value={formInitialValues?.company?.mcNumber || ""}
                            disabled
                        />
                    </div>
                </div>
            )}
            {formInitialValues?.company?.usDotNumber &&
                (fmcsaVerified ? (
                    <div>
                        Verification completed successfully. Document upload not
                        required.
                    </div>
                ) : (
                    
                    <div style={{color:'#F91313', fontSize:16, fontWeight:700}}>
                        DOT Number not found or mismatched. Please proceed with
                        document upload.
                    </div>
                ))}

            {formInitialValues?.company?.usDotNumber && (
                <div className={css.verificationOption}>
                    <span>OR</span>
                </div>
            )}

            <div className={css.uploadDocuments}>
                {formInitialValues?.company?.usDotNumber ? (
                    <h5>Upload Documents.</h5>
                ) : (
                    <div className={css.dot}>
                        <img src="/images/icons/DOT.svg" alt="DOT.svg" />
                        <h5>No DOT Number provided or applicable.</h5>
                    </div>
                )}
                <h6>
                    Please upload the following documents for manual
                    verification.
                </h6>
                <div className={css.flexBox}>
                    <div className={css.col02}>
                        <div className={`${css.labelDiv} ${css.mb8}`}>
                            <label>
                                Articles of Incorporation / LLC Formation / Business Registration Document
                                <span className={css.tooltipIcon}>
                                    <img src="/images/icons/icon-info.svg" alt="" />
                                    <span className={css.tooltip}>
                                       Document filed with your state showing business formation/registration.
                                    </span>
                                </span>
                            </label>
                        </div>
                        <BrowseFiles 
                            label="Business Registration Document" 
                            onUploadComplete={setDotFiles} 
                            initialFiles={addDocFiles} 
                            maxFiles={3}
                        />
                    </div>
                    <div className={css.col02}>
                        <div className={`${css.labelDiv} ${css.mb8}`}>
                            <label>
                                EIN Confirmation Letter (IRS SS-4, CP 575, or 147C)
                                <span className={css.tooltipIcon}>
                                    <img src="/images/icons/icon-info.svg" alt="" />
                                    <span className={css.tooltip}>
                                       Official letter from the IRS confirming your Employer Identification Number.
                                    </span>
                                </span>
                            </label>
                        </div>
                        <BrowseFiles 
                            label="EIN Confirmation Letter" 
                            onUploadComplete={setMcFiles} 
                            initialFiles={enDocFiles}
                            maxFiles={3}
                        />
                    </div>
                </div>
            </div>

            <div className={`${css.formRow} ${css.submitRow}`}>
                <button type="submit" className={css.submitBtn}>
                    Submit for Verification & Access Drivers
                </button>
            </div>
        </form>
    )
}
export default VerificationProcess
