'use client';
import React, { useEffect } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { submitDriverDetails } from '@/services/driverFormService';  
import { SpecialPermit } from "../types";
import { toast } from 'react-toastify';
import css from '../driverRegistration.module.scss';
import 'react-datepicker/dist/react-datepicker.css';

interface SpecialtyPermitField {
  driverSpecialLicenseId?: number;
  name: string;
  number: string;
}

interface FormValues {
  hasSpecialtyPermit: 'yes' | 'no';
  driverSpecialLicense: SpecialtyPermitField[];
}

interface Props{
  onFormSubmit:()=>void
  initialData:SpecialPermit | null
  currentStep:number
  currentStage:number
}

const validationSchema = Yup.object().shape({
  hasSpecialtyPermit: Yup.string().required('Required'),
  driverSpecialLicense: Yup.array().when('hasSpecialtyPermit', {
    is: 'yes',
    then: (schema) =>
      schema
        .min(1, 'At least one permit is required')
        .max(5, 'You can add up to 5 permits only')
        .of(
          Yup.object().shape({
            name: Yup.string().required('Permit name is required'),
            number: Yup.string().required('Permit number is required'),
          })
        ),
    otherwise: (schema) => schema.notRequired(),
  }),
});

function isPermitErrorArray(
  value: unknown
): value is Array<{ name?: string; number?: string }> {
  return Array.isArray(value);
}
const SpecialtyPermit = ({onFormSubmit ,initialData,currentStep,currentStage}:Props) => {
  const formik = useFormik<FormValues>({
    initialValues: {
      hasSpecialtyPermit: 'no',
      driverSpecialLicense: [{ name: '', number: '' }],
    },
      enableReinitialize: true,
    validationSchema,
    onSubmit: async (values) => {
    const  step=4
      const payload = {
        ...(currentStage <= 2 && { currentStage: 2 }),
        ...(currentStage === 2 && step >= currentStep && { currentStep: step }),
        driver: {
          isSpecialLicenses: values.hasSpecialtyPermit === 'yes',
          driverSpecialLicense:
            values.hasSpecialtyPermit === 'yes'
              ? values.driverSpecialLicense
                  .filter((p) => p.name.trim() || p.number.trim())
                  .map((p) => ({
                    ...(p.driverSpecialLicenseId && {
                      driverSpecialLicenseId: p.driverSpecialLicenseId,
                    }),
                    nameOfSpecialLicenses: p.name,
                    specialLicensesNumber: p.number,
                  }))
              : [],
        },
      };

      try {
        await submitDriverDetails(payload);
        toast.success('Driver specialty permit submitted successfully');
        onFormSubmit();
      } catch (err: unknown) {
        toast.error(err instanceof Error ? "Failed to submit specialty permit details" : 'Something went wrong ');
      }
    },
  });

  const {
    values,
    errors,
    touched,
    handleChange,
    handleSubmit,
    setFieldValue,
  } = formik;

  
useEffect(() => {
  if (initialData) {
    formik.setValues({
      hasSpecialtyPermit: initialData.isSpecialLicenses ? "yes" : "no",
      driverSpecialLicense: initialData.driverSpecialLicense?.map(
        (item) => ({
          name: item.nameOfSpecialLicensesOrCertifications || '',
          number: item.specialLicensesOrCertificationsNumber || '',
          driverSpecialLicenseId: item.driverSpecialLicenseId,
        })
      ) || [],
    });
  }
  
}, [initialData]);

  return (
    <form onSubmit={handleSubmit} className={css.commonForm}>
      <div className={`${css.formRow} ${css.dBlaco}`}>
        <div className={css.labelDiv}>
          <label>
              Do you hold any additional permits or licenses required for specific driving roles like Chauffeur, Taxi, Livery, etc. (beyond your standard DL/CDL)?
          </label>
        </div>
        <ul className={`${css.checkboxList} ${css.radioList}`}>
          <li className={css.radioGroup}>
            <label className={css.radioLabel}>
                <input
                  type="radio"
                  name="hasSpecialtyPermit"
                  value="yes"
                  checked={values.hasSpecialtyPermit === 'yes'}
                  onChange={() => {
                    setFieldValue('hasSpecialtyPermit', 'yes');
                    if (values.driverSpecialLicense.length === 0) {
                      setFieldValue('driverSpecialLicense', [{ name: '', number: '' }]);
                    }
                  }}
                />
                <span className={css.checkmark}></span>
                <p>Yes</p>
            </label>
          </li>
          <li className={css.radioGroup}>
            <label className={css.radioLabel}>
                <input
                  type="radio"
                  name="hasSpecialtyPermit"
                  value="no"
                  checked={values.hasSpecialtyPermit === 'no'}
                  onChange={() => {
                    setFieldValue('hasSpecialtyPermit', 'no');
                    setFieldValue('driverSpecialLicense', []);
                  }}
                />
                <span className={css.checkmark}></span>
                <p>No</p>
            </label>
          </li>
        </ul>
      </div>
      
      {values.hasSpecialtyPermit === 'yes' &&
        values.driverSpecialLicense.map((permit, index) => (
          <div key={index} className={`${css.formRow} ${css.flexEnd}`}>
            <div className={css.col03}>
              <div className={css.labelDiv}>
                <label>
                  Name of Specialty Permit/License <span>(Optional)</span>
                  <span className={css.tooltipIcon}>
                    <img src="/images/icons/icon-info.svg" alt="" />
                    <span className={css.tooltip}>
                      Select the general geographic areas your company serves.
                    </span>
                  </span>
                </label>
              </div>
              <input
                type="text"
                name={`driverSpecialLicense[${index}].name`}
                value={permit.name}
                onChange={handleChange}
                placeholder="Enter name"
              />
              {touched.driverSpecialLicense?.[index]?.name &&
                isPermitErrorArray(errors.driverSpecialLicense) &&
                errors.driverSpecialLicense[index]?.name && (
                  <div className={css.error}>
                    {errors.driverSpecialLicense[index]?.name}
                  </div>
                )}
            </div>
            <div className={css.col03}>
              <div className={css.labelDiv}>
                <label>
                  Specialty Permit/License Number <span>(Optional)</span>
                </label>
              </div>
              <input
                type="text"
                name={`driverSpecialLicense[${index}].number`}
                value={permit.number}
                onChange={handleChange}
                placeholder="Enter number"
              />
              {touched.driverSpecialLicense?.[index]?.number &&
                isPermitErrorArray(errors.driverSpecialLicense) &&
                errors.driverSpecialLicense[index]?.number && (
                  <div className={css.error}>
                    {errors.driverSpecialLicense[index]?.number}
                  </div>
                )}
            </div>
            <div className={css.col03}>
              {index === 0 && values.driverSpecialLicense.length < 5 ? (
                <button
                  type="button"
                  className={css.additionalBtn}
                  onClick={() =>
                    
                    setFieldValue('driverSpecialLicense', [
                       ...values.driverSpecialLicense,
                      { name: '', number: '' },
                     
                    ])
                  }
                >
                  Add Additional Specialty Permit
                </button>
              ) : index > 0 ? (
                <button
                  type="button"
                  className={css.additionalCloseBtn}
                  onClick={() => {
                    const updated = [...values.driverSpecialLicense];
                    updated.splice(index, 1);
                    setFieldValue('driverSpecialLicense', updated);
                  }}
                >
                  <img src="/images/icons/icon-close.svg"/>
                  Remove
                </button>
              ) : null}
            </div>
          </div>
        ))}

      
        {/* Submit Button */}
        <div className={`${css.formRow} ${css.submitRow}`}>
            <button type="submit" className={css.submitBtn}>
              Save and Continue
          </button>
        </div>
    </form>
  );
};

export default SpecialtyPermit;
