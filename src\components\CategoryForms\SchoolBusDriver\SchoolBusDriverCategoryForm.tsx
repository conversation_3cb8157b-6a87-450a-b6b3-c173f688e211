"use client";
import React from "react";
import { useSchoolBusDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import DrivingExperience from "./DrivingExperience";
import WorkHistory from "./WorkHistory";
import Medical from "./Medical";
import Documents from "./Documents";
import ConsentsReview from "./ConsentsReview";
import css from "../Driver/driver.module.scss";
import StepIndicator from "@/components/Common/StepIndicator";

const SchoolBusDriverCategoryForm: React.FC = () => {
  const { currentStep } = useSchoolBusDriverCategory();

  const categorySteps = [
    { id: 1, title: "Experience" },
    { id: 2, title: "History" },
    { id: 3, title: "Medical" },
    { id: 4, title: "Docs" },
    { id: 5, title: "Consents & Review" },
  ];

  const completedSteps: number[] = [];
  for (let i = 1; i < currentStep; i++) {
    completedSteps.push(i);
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <DrivingExperience />;
      case 2:
        return <WorkHistory />;
      case 3:
        return <Medical />;
      case 4:
        return <Documents />;
      case 5:
        return <ConsentsReview />;
      default:
        return <DrivingExperience />;
    }
  };

  const currentStepData = categorySteps.find(step => step.id === currentStep);

  return (
    <div className={css.driverCategoryForm}>
      <div className={css.container}>
        <StepIndicator
          categorySteps={categorySteps}
          currentStep={currentStep}
          completedSteps={completedSteps}
        />

        <h6 className={css.required}>
          Required fields are marked with <span>*</span>
        </h6>

        <div className={css.stepContent}>
          <h2>{currentStepData?.title}</h2>
          {renderStepContent()}
        </div>
      </div>
    </div>
  );
};

export default SchoolBusDriverCategoryForm;
