@import '../../styles/global.scss';

.registrationSection {
    padding: 56px 0px 40px;
    width: 100%;

    @include for-size(big-tablet-down) {
        padding: 32px 16px;
    }

    .loginWrapper {
        background-color: $white;
        border: 1px solid #E5E5E5;
        border-radius: 16px;
        width: 100%;
        max-width: 804px;
        margin: 0 auto;
        padding: 40px 0px 52px;

        @include for-size(big-tablet-down) {
            border: none;
            border-radius: 0px;
            padding: 0px;
        }

        h1 {
            font-weight: 500;
            font-size: 24px;
            line-height: 32px;
            color: $dark;
            text-align: center;
            max-width: 600px;
            margin: 0px auto;
        }

        p {
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            text-align: center;
            margin: 12px auto 0px;
            max-width: 600px;

            a {
              color: $black;
              font-weight: 600;
            }

            @include for-size(big-tablet-down) {
                margin-top: 16px;
            }
        }
    }

    .registrationForm {
        display: flex;
        flex-wrap: wrap;
        column-gap: 24px;
        row-gap: 28px;
        max-width: 600px;
        margin: 32px auto 0px;

        @include for-size(big-tablet-down) {
            row-gap: 20px;
        }

        .formGroup {
            position: relative;
            width: 100%;

            .error {
                color: #F91313;
                font-size: 12px;
                line-height: 18px;
                font-weight: 400;
                position: absolute;
                left: 0px;
                top: calc(100% + 4px);

                @include for-size(tablet-phone) {
                    top: 100%;
                }
            }

            &:has( .error) {
              input {
                border-color: #F91313;
              }
            }
        }

        label {
            color: $black;
            font-weight: 700;
            font-size: 14px;
            line-height: 22px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 4px;
            width: 100%;

            span {
                font-weight: 400;
            }

            sup {
                color: $red;
                line-height: 0px;
            }
        }

        input[type='text'],
        input[type='email'] {
            background-color: #FFFFFF;
            border: 1px solid #707070;
            border-radius: 4px;
            width: 100%;
            height: 44px;
            color: #515B6F;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            text-align: left;
            padding: 10px 8px;
            outline: none !important;

            &:disabled {
                background-color: #F7F7F7;
                font-style: italic;
            }

            &::placeholder {
                color: #9D9D9D;
                font-weight: 400;
                opacity: 1;
            }
        }

        .error {
          color: #F91313;
          font-size: 12px;
          line-height: 18px;
          position: absolute;
          left: 0px;
          top: calc(100% + 4px);
          width: 100%;
        }

        .submitBtn {
            background-color: $secondary;
            border: none;
            border-radius: 8px;
            color: $black;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 18px;
            line-height: 24px;
            padding: 4px;
            width: 100%;
            height: 60px;

            @include for-size(big-tablet-down) {
                border-radius: 4px;
                height: 50px;
            }
        }
    }
}