"use client";
import React, { useRef, useState, useEffect } from "react";
import { toast } from "react-toastify";
import css from "../../../components/RegistrationForm/Employer02/employerRegistration.module.scss";

export interface FileItem {
  filename?: string;
  filepath?: string;
  multimediaId?: number;
  side?: string;
}

interface UploadFile {
  file: File;
  progress: number;
  uploading: boolean;
  error: string;
  cdnUrl: string;
  path?: string;
  multimediaId?: number;
  isPrefetched?: boolean;
}

interface DocumentUploaderProps {
  label: string;
  side?: "front" | "back";
  maxFiles?: number;
  initialFiles?: FileItem[];
  onUploadComplete: (files: FileItem[]) => void;
}

const VALID_TYPES = ["image/jpeg", "image/png", "application/pdf"];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB

const BrowseFiles: React.FC<DocumentUploaderProps> = ({
  label,
  side,
  maxFiles = 1,
  initialFiles = [],
  onUploadComplete,
}) => {
  const [files, setFiles] = useState<UploadFile[]>([]);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [initialized, setInitialized] = useState(false);

  const isValidFile = (file: File): string => {
    if (!VALID_TYPES.includes(file.type))
      return "Only JPG, PNG, and PDF files are allowed";
    if (file.size > MAX_FILE_SIZE) return "File size should not exceed 5MB";
    return "";
  };

  const updateFile = (index: number, updated: Partial<UploadFile>) => {
    setFiles((prev) =>
      prev.map((f, i) => (i === index ? { ...f, ...updated } : f))
    );
  };

  const uploadFile = async (uploadFile: UploadFile, index: number) => {
    if (uploadFile.isPrefetched) return;

    try {
      updateFile(index, { uploading: true, progress: 0, error: "" });

      const res = await fetch(
        `${process.env.NEXT_PUBLIC_API_V1}files/generate-presigned-url`,
        {
          method: "GET",
          headers: { "Content-Type": uploadFile.file.type },
        }
      );

      if (!res.ok) throw new Error("Failed to get presigned URL");
      const { data } = await res.json();
      const { uploadUrl, cdnUrl, path } = data;

      const xhr = new XMLHttpRequest();
      xhr.upload.onprogress = (e) => {
        if (e.lengthComputable) {
          updateFile(index, {
            progress: Math.round((e.loaded / e.total) * 100),
          });
        }
      };

      xhr.onload = () => {
        if (xhr.status === 200) {
          updateFile(index, { uploading: false, cdnUrl, path });
        } else {
          updateFile(index, { uploading: false, error: "Upload failed" });
        }
      };

      xhr.onerror = () => {
        updateFile(index, { uploading: false, error: "Upload failed" });
      };

      xhr.open("PUT", uploadUrl, true);
      xhr.setRequestHeader("Content-Type", uploadFile.file.type);
      xhr.send(uploadFile.file);
    } catch (err) {
      updateFile(index, { uploading: false, error: "Upload failed" });
      console.error(
        `File upload failed for index ${index}, file: ${uploadFile.file.name}`,
        err
      );
    }
  };

  const addFiles = (fileList: FileList | null) => {
    if (!fileList) return;

    const incoming = Array.from(fileList);
    const availableSlots = maxFiles - files.length;

    if (availableSlots <= 0) {
      toast.error(
        `You can upload a maximum of ${maxFiles} file${
          maxFiles > 1 ? "s" : ""
        }.`
      );
      return;
    }

    const validFiles: File[] = [];
    const invalidFiles: File[] = [];

    incoming.slice(0, availableSlots).forEach((file) => {
      const validationError = isValidFile(file);
      if (validationError) {
        invalidFiles.push(file);
        toast.error(`${validationError}`);
      } else {
        validFiles.push(file);
      }
    });

    // Only add valid files to state
    if (validFiles.length > 0) {
      const newUploads: UploadFile[] = validFiles.map((file) => ({
        file,
        progress: 0,
        uploading: false,
        error: "",
        cdnUrl: "",
      }));

      setFiles((prev) => {
        const updated = [...prev, ...newUploads];
        newUploads.forEach((file, idx) => uploadFile(file, prev.length + idx));
        return updated;
      });
    }
  };

  const removeFile = (index: number) => {
    setFiles((prev) => {
      const newFiles = prev.filter((_, i) => i !== index);
      return newFiles;
    });
  };

  useEffect(() => {
    if (!initialized && initialFiles.length > 0) {
      const prefetched: UploadFile[] = initialFiles.map((f) => ({
        file: new File([""], f.filename || "prefetched"),
        progress: 100,
        uploading: false,
        error: "",
        cdnUrl: `${process.env.NEXT_PUBLIC_IMAGE_BASE}${f.filepath}`,
        path: f.filepath,
        multimediaId: f.multimediaId,
        isPrefetched: true,
      }));
      setFiles(prefetched);
      setInitialized(true);
    }
  }, [initialFiles, initialized]);

  useEffect(() => {
    const allUploaded = files.every(
      (f) => (f.cdnUrl || f.isPrefetched) && !f.uploading && !f.error
    );

    if (files.length === 0 || allUploaded) {
      const finalFiles: FileItem[] = files.map((f) => {
        const fileItem: FileItem = {
          filename: f.file.name,
          filepath: f.path,
          ...(f.multimediaId ? { multimediaId: f.multimediaId } : {}),
          ...(side ? { side } : {}),
        };
        return fileItem;
      });

      setTimeout(() => {
        onUploadComplete(finalFiles);
      }, 0);
    }
  }, [files, onUploadComplete, side]);

  return (
    <div style={{ marginBottom: "2rem" }}>
      <p>{label}</p>

      <div
        className={css.BrowseUpload}
        onDrop={(e) => {
          e.preventDefault();
          addFiles(e.dataTransfer.files);
        }}
        onDragOver={(e) => e.preventDefault()}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={VALID_TYPES.join(",")}
          // onChange={(e) => addFiles(e.target.files)}
          onChange={(e) => {
            addFiles(e.target.files);
            e.target.value = "";
          }}
          multiple={maxFiles > 1}
          style={{ display: "none" }}
        />

        <div className={css.dragDrop}>
          <img src="/images/icons/icon-drag-drop.svg" alt="drag icon" />
          <span>Drag & drop your file here</span>
        </div>

        <div className={css.or}>or</div>
        <div className={css.browseFile}>
          <button type="button">Browse File</button>
        </div>
      </div>

      <p className={css.supportsType}>
        Supported file types: <strong>JPG, PNG, PDF</strong> | Max file size:{" "}
        <strong>5MB</strong>
      </p>

      <ul className={css.browseFilesList}>
        {files.map((fileData, index) => (
          <li key={index}>
            <img
              src="/images/icons/icon-step-complete.svg"
              alt="icon-step-complete.svg"
            />
            <p>{fileData.file.name}</p>{" "}
            {fileData.uploading && (
              <div className={css.progressber}>
                <progress
                  value={fileData.progress}
                  max="100"
                  style={{ width: "100%" }}
                />
                <p>{fileData.progress}%</p>
              </div>
            )}
            <button
              type="button"
              onClick={() => removeFile(index)}
              title="Remove"
              className={css.remove}
            >
              <img src="/images/icons/icon-remove.svg" alt="icon-remove.svg" />
              Remove
            </button>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default BrowseFiles;
