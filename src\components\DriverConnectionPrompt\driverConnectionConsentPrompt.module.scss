.promptWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.promptBox {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 2.5rem;
  max-width: 720px;
  width: 100%;
  font-family: 'Segoe UI', sans-serif;

  .logo {
    width: 100px;
    height: auto;
    object-fit: contain;
    margin: 1rem 0;
  }

  h2 {
    font-size: 1.75rem;
    margin-bottom: 1.25rem;
    color: #333;
  }

  h3,
  h4 {
    font-size: 1.2rem;
    margin-top: 1.5rem;
    color: #444;
  }
}

.highlight {
  font-size: 1rem;
  margin-bottom: 1rem;
  color: #333;
}

.bulletPoints {
  list-style-type: disc;
  padding-left: 1.5rem;
  margin: 1rem 0;
  color: #555;

  li {
    margin-bottom: 0.75rem;
    line-height: 1.5;

    strong {
      color: #222;
    }

    .subPoints {
      list-style-type: circle;
      padding-left: 1.25rem;
      margin-top: 0.5rem;

      li {
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
        color: #666;
      }
    }
  }
}

.agreementText {
  font-size: 0.95rem;
  color: #555;
  margin-top: 2rem;

  a {
    color: #0070f3;
    text-decoration: underline;

    &:hover {
      text-decoration: none;
    }
  }
}

.buttonGroup {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
}

.acceptBtn {
  background-color: #0070f3;
  color: #fff;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;

  &:hover {
    background-color: #005bb5;
  }
}

.cancelBtn {
  background-color: transparent;
  color: #666;
  padding: 0.75rem 1.5rem;
  border: 1px solid #ccc;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;

  &:hover {
    background-color: #f0f0f0;
  }
}
