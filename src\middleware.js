import { NextResponse } from "next/server";
 
let locales = ['en', 'es']
 
// Get the preferred locale, similar to the above or using a library
function getLocale(request) {
  console.log('request', request)
  return 'en'
}
 
export function middleware(request) {
  const { pathname } = request.nextUrl

  const authToken = request.cookies.get("authToken")?.value;
  const pathnameHasLogin = locales.some((locale) => pathname.startsWith(`/${locale}/login`))
  if (pathnameHasLogin && authToken) {
    request.nextUrl.pathname = `/${getLocale(request)}/`; 
    return NextResponse.redirect(request.nextUrl);
  }

  const pathnameHasProfile = locales.some((locale) => pathname.startsWith(`/${locale}/profile`))
  if (pathnameHasProfile && !authToken) {
    request.nextUrl.pathname = `/${getLocale(request)}/`; 
    return NextResponse.redirect(request.nextUrl);
  }

  const pathnameHasCategory = locales.some((locale) => pathname.startsWith(`/${locale}/category`))
  if (pathnameHasCategory && !authToken) {
    request.nextUrl.pathname = `/${getLocale(request)}/`; 
    return NextResponse.redirect(request.nextUrl);
  }

  const pathnameHasLocale = locales.some(
    (locale) => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  )
  if (pathnameHasLocale) return
  // Redirect if there is no locale
  const locale = getLocale(request)
  request.nextUrl.pathname = `/${locale}${pathname}`
  return NextResponse.redirect(request.nextUrl)
}
 
export const config = {
  matcher: [
    // Skip all internal paths (_next)
    '/((?!_next|images|favicon.ico|fonts|api).*)',
    // Optional: only run on root (/) URL
    // '/'
  ],
}