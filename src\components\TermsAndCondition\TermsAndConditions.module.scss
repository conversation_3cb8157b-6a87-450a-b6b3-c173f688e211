.termsContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 32px;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.08);

  color: #333;
  line-height: 1.6;

  p {
    margin-bottom: 24px;
  }

  ul {
    padding-left: 24px;
    margin-bottom: 24px;

    li {
      margin-bottom: 8px;
    }
  }
}

.heading {
  font-size: 32px;
  font-weight: bolder;
  margin-bottom: 24px;
  color: #222;
  text-align: center;
}

.subHeading {
  font-size: 20px;
  font-weight: bold;
  margin-top: 32px;
  margin-bottom: 8px;
  color: #1a1a1a;
}

.link {
  color: #0070f3;
  text-decoration: underline;
  font-weight: 500;

  &:hover {
    color: #0056b3;
  }
}

.agreementBox {
  margin-top: 32px;
  padding: 16px;
  background-color: #f7f9fc;
  border: 1px solid #d9e2ec;
  border-radius: 8px;

  label {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 16px;
    color: #333;

    input[type="checkbox"] {
      margin-top: 5px;
      transform: scale(1.1);
    }

    span {
      line-height: 1.5;
    }
  }
}
.buttonWrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}
.button {
  all: unset;
  text-align: center;
  width: 660px;
  height: 30px;
  gap: 10px;
  border-radius: 4px;
  padding: 12px;
  background: #fbd758;
  margin-top: 24px;
  font-weight: 500;
  font-size: 18px;
  line-height: 120%;
  letter-spacing: 0%;
}