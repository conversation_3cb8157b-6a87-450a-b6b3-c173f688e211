import React from 'react';
import css from './radioGroup.module.scss'

interface RadioGroupProps {
  name: string;
  options: string[];
  selectedValue: string;
  onChange: (value: string) => void;
  direction?: 'row' | 'column';
}

const RadioGroup: React.FC<RadioGroupProps> = ({
  name,
  options,
  selectedValue,
  onChange,
  // direction = 'row',
}) => {
  return (
    <ul className={css.radioList}>
      {options.map((option, index) => (
        <li className={css.radioGroup} key={index}>
          <label key={index} className={css.radioLabel}>
            <input
              type="radio"
              name={name}
              value={option}
              checked={selectedValue === option}
              onChange={() => onChange(option)}
            />
            <span className={css.checkmark}></span>
            <p>{option}</p>
          </label>
        </li>
      ))}
    </ul>
  );
};

export default RadioGroup;
