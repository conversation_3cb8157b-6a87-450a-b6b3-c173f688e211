import { useFormik } from 'formik';
import css from '../employerRegistration.module.scss'
import * as Yup from "yup";
import { toast } from "react-toastify";
import { getStates, City, State,
} from '@/services/locationService';
import { useEffect, useState } from 'react';
import { FormValues, submitCompanyDetails } from '@/services/employerFormService';
import BrowseImages from '@/components/Browse/BrowseImages';
import { StepThreeValues } from '../types';

const step3ValidationSchema = Yup.object().shape({
    companyTypes: Yup.array().min(1, "Please select at least one company type"),
    yearsInBusiness: Yup.string().required("Please select years in business"),
    estimatedDrivers: Yup.string().required("Please select estimated drivers"),
    primaryAreas: Yup.array()
        .min(1, "Select at least one operating area")
        .required("Required"),
    otherCompanyType: Yup.string().when("companyTypes", {
        is: (types: string[]) => types.includes("other"),
        then: (schema) => schema.required("Please enter other company type"),
        otherwise: (schema) => schema.notRequired(),
    }),
});

type ProfileEssentialsProps = {
    formInitialValues: StepThreeValues;
    currentStep: number;
    companyTypes: FormValues[];
    areas: FormValues[];
    yearInBussiness: FormValues[];
    driverOptions: FormValues[];
    setIsThirdAccordionComplete: (val: boolean) => void;
    setIsThirdAccordionOpen: (val: boolean) => void;
    setIsFourthAccordionOpen: (val: boolean) => void;
    selectedStateSlug:string,
    setSelectedStateSlug:(val: string)=>void;
    selectedCitySlug:string,
    setSelectedCitySlug:(val:string)=>void;
    cities:City[]
    setCities: (val:City[])=>void
};


const ProfileEssentials: React.FC<ProfileEssentialsProps> = ({ formInitialValues, currentStep, companyTypes, areas, yearInBussiness, driverOptions, setIsThirdAccordionComplete, setIsThirdAccordionOpen, setIsFourthAccordionOpen, selectedStateSlug, setSelectedStateSlug,  selectedCitySlug, setSelectedCitySlug, cities, setCities }) => {
    const [states, setStates] = useState<State[]>([]);
    const [imagePath, setImagePath] = useState<string | null>();

    useEffect(() => {
        const loadstate = async () => {
            const stateList = await getStates();
            setStates(stateList);
        };
        loadstate();
    }, []);

    const handleStateChange = async (slug: string) => {
        setSelectedStateSlug(slug);
        setSelectedCitySlug("");
    };

    const formikStepthree = useFormik<StepThreeValues>({
        initialValues: formInitialValues,
        enableReinitialize: true,
        validationSchema: step3ValidationSchema,
        onSubmit: async (values) => {
            const formattedCompanyTypes = values.companyTypes.map((type) =>
                type === "other" ? values.otherCompanyType?.trim() : type
            ).filter(Boolean)

            const isId = (val: string) => /^\d+$/.test(val)
            const formattedPrimaryOperatingAreas = values.primaryAreas
                .filter((val) => isId(val))
                .concat(
                    values.primaryAreas.includes("local")
                        ? [selectedStateSlug, selectedCitySlug].filter(Boolean)
                        : []
                );
            const step = 3
            const payload = {
                currentStage: 2,
                ...(step >= currentStep && { currentStep: step }),
                company: {
                    companyLogo: imagePath || values.companyLogo || null,
                    companyType: formattedCompanyTypes,
                    yearsInBusiness: values.yearsInBusiness,
                    numberOfDrivers: values.estimatedDrivers,
                    primaryOperatingAreas: formattedPrimaryOperatingAreas,
                },
            };
            const success = await submitCompanyDetails(payload);
            if (success) {
                setImagePath(null)
                toast.success("Profile information Submitted");
                setTimeout(()=>{
                setIsThirdAccordionComplete(true);
                setIsThirdAccordionOpen(false);
                setIsFourthAccordionOpen(true);
                },2000)
            } else {
                toast.error("Failed to submit profile information");
            }
        },
    });

    useEffect(() => {
        if (!formikStepthree.values.primaryAreas.includes("local")) {
            setSelectedStateSlug('');
            setSelectedCitySlug('');
            setCities([]);
        }
    }, [formikStepthree.values.primaryAreas,
        setSelectedStateSlug,
        setSelectedCitySlug,
        setCities]);

    return (
        <form
            className={css.commonForm}
            onSubmit={formikStepthree.handleSubmit}
        >
            <BrowseImages
                setImagePath={setImagePath}
                label={
                    <>
                        Company Logo <span>(Optional, but Recommended)</span>
                    </>
                }
                toolTip='Upload your company logo (JPG, PNG format, recommended size: 300x300px, Max 2MB'
            />
            <div className={`${css.formRow} ${css.dBlaco}`}>
                <div className={css.labelDiv}>
                    <label>
                        Company Type <sup>*</sup>
                    </label>
                </div>

                <ul className={css.checkboxList}>
                    {companyTypes.map((item) => (
                        <li key={item.formValueId}>
                            <div className={css.checkBox}>
                                <input
                                    type="checkbox"
                                    name="companyTypes"
                                    value={item.formValueId}
                                    onChange={formikStepthree.handleChange}
                                    checked={formikStepthree.values.companyTypes.includes(
                                        item.formValueId.toString()
                                    )}
                                />
                                <span className={css.checkmark}></span>
                                <p>{item.label?.en}</p>
                            </div>
                        </li>
                    ))}

                    <li>
                        <div className={css.checkBox}>
                            <input
                                type="checkbox"
                                name="companyTypes"
                                value="other"
                                onChange={formikStepthree.handleChange}
                                checked={formikStepthree.values.companyTypes.includes(
                                    "other"
                                )}
                            />
                            <span className={css.checkmark}></span>
                            <p>Other</p>
                        </div>
                    </li>
                    {formikStepthree.values.companyTypes.includes("other") && (
                        <li>
                            <input
                                type="text"
                                name="otherCompanyType" 
                                placeholder="Enter here"
                                value={formikStepthree.values.otherCompanyType}
                                onChange={formikStepthree.handleChange}
                            />
                            {formikStepthree.touched.otherCompanyType &&
                                formikStepthree.errors.otherCompanyType && (
                                    <p>{formikStepthree.errors.otherCompanyType}</p>
                                )}
                        </li>
                    )}
                </ul>
                {formikStepthree.touched.companyTypes &&
                    formikStepthree.errors.companyTypes && (
                        <p className={css.error}>
                            {formikStepthree.errors.companyTypes}
                        </p>
                    )}
            </div>

            {/* Years in Business */}
            <div className={`${css.formRow} ${css.dBlaco}`}>
                <div className={css.labelDiv}>
                    <label>
                        Years in Business <sup>*</sup>
                    </label>
                </div>
                <ul className={`${css.checkboxList} ${css.radioList}`}>
                    {yearInBussiness.map((item) => (
                        <li key={item.formValueId} className={css.radioGroup}>
                            <label className={css.radioLabel}>
                                <input
                                    type="radio"
                                    name="yearsInBusiness"
                                    value={item.formValueId.toString()}
                                    onChange={formikStepthree.handleChange}
                                    checked={
                                        formikStepthree.values.yearsInBusiness ===
                                        item.formValueId.toString()
                                    }
                                />
                                <span className={css.checkmark}></span>
                                <span className={css.labelText}>
                                    {item.label?.en}
                                </span>
                            </label>
                        </li>
                    ))}
                </ul>
                {formikStepthree.touched.yearsInBusiness &&
                    formikStepthree.errors.yearsInBusiness && (
                        <p className={css.error}>
                            {formikStepthree.errors.yearsInBusiness}
                        </p>
                    )}
            </div>

            {/* Estimated Drivers */}
            <div className={`${css.formRow} ${css.dBlaco}`}>
                <div className={css.labelDiv}>
                    <label>
                        Estimated Number of Drivers/Operators Employed{" "}
                        <sup>*</sup>
                    </label>
                </div>
                <ul className={`${css.checkboxList} ${css.radioList}`}>
                    {driverOptions.map((item) => (
                        <li key={item.formValueId} className={css.radioGroup}>
                            <label className={css.radioLabel}>
                                <input
                                    type="radio"
                                    name="estimatedDrivers"
                                    value={item.formValueId}
                                    onChange={formikStepthree.handleChange}
                                    checked={
                                        formikStepthree.values.estimatedDrivers ===
                                        item.formValueId.toString()
                                    }
                                />
                                <span className={css.checkmark}></span>
                                <span className={css.labelText}>
                                    {item.label?.en}
                                </span>
                            </label>
                        </li>
                    ))}
                </ul>
                {formikStepthree.touched.estimatedDrivers &&
                    formikStepthree.errors.estimatedDrivers && (
                        <p className={css.error}>
                            {formikStepthree.errors.estimatedDrivers}
                        </p>
                    )}
            </div>

            {/* Primary Operating Areas */}
            <div className={`${css.formRow} ${css.dBlaco}`}>
                <div className={css.labelDiv}>
                    <label>
                        Primary Operating Area(s) <sup>*</sup>
                        <span className={css.tooltipIcon}>
                            <img src="/images/icons/icon-info.svg" alt="" />
                            <span className={css.tooltip}>
                                Select the general geographic areas your company serves.
                            </span>
                        </span>
                    </label>
                </div>
                <span className={css.selectRegion}>
                    Select the main regions you operate in
                </span>
                <ul className={css.checkboxList}>
                    {areas.map((item) => (
                        <li key={item.formValueId}>
                            <div className={css.checkBox}>
                                <input
                                    type="checkbox"
                                    name="primaryAreas"
                                    value={item.formValueId}
                                    onChange={formikStepthree.handleChange}
                                    checked={formikStepthree.values.primaryAreas.includes(
                                        item.formValueId.toString()
                                    )}
                                />
                                <span className={css.checkmark}></span>
                                <p>{item.label?.en}</p>
                            </div>
                        </li>
                    ))}

                    <li>
                        <div className={css.checkBox}>
                            <input
                                type="checkbox"
                                name="primaryAreas"
                                value="local"
                                onChange={formikStepthree.handleChange}
                                checked={formikStepthree.values.primaryAreas.includes(
                                    "local"
                                )}
                            />
                            <span className={css.checkmark}> </span>
                            <p>Local</p>
                        </div>
                    </li>


                    {formikStepthree.values.primaryAreas.includes("local") && (
                        <li className={css.flexList}>
                            <div className={css.colIn02}>
                                <div className={css.labelDiv}>
                                    <label>State</label>
                                </div>
                                <div className={css.dropdown}>
                                    <button 
                                        type="button" 
                                        className={css.dropdownToggle}
                                        onClick={() => {
                                            const stateDropdown = document.getElementById('stateDropdown');
                                            if (stateDropdown) {
                                                stateDropdown.style.display = stateDropdown.style.display === 'block' ? 'none' : 'block';
                                            }
                                        }}
                                    >
                                        {states.find(state => state.slug === selectedStateSlug)?.name?.en || 'Select a State'}
                                    </button>
                                    <div 
                                        id="stateDropdown"
                                        className={css.dropdownMenu} 
                                        style={{ display: 'none' }}
                                    >
                                        {states.map((state) => (
                                            <button 
                                                type="button" 
                                                key={state.stateId}
                                                className={css.dropdownItem}
                                                onClick={() => {
                                                    handleStateChange(state.slug);
                                                    const stateDropdown = document.getElementById('stateDropdown');
                                                    if (stateDropdown) {
                                                        stateDropdown.style.display = 'none';
                                                    }
                                                }}
                                            >
                                                {state.name?.en}
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            </div>
                            <div className={css.colIn02}>
                                <div className={css.labelDiv}>
                                    <label>City</label>
                                </div>
                                <div className={css.dropdown}>
                                    <button 
                                        type="button" 
                                        className={css.dropdownToggle}
                                        onClick={() => {
                                            const cityDropdown = document.getElementById('cityDropdown');
                                            if (cityDropdown) {
                                                cityDropdown.style.display = cityDropdown.style.display === 'block' ? 'none' : 'block';
                                            }
                                        }}
                                        disabled={!selectedStateSlug}
                                    >
                                        {cities.find(city => city.slug === selectedCitySlug)?.name?.en || 'Select a city'}
                                    </button>
                                    <div 
                                        id="cityDropdown"
                                        className={css.dropdownMenu} 
                                        style={{ display: 'none' }}
                                    >
                                        {cities.map((city) => (
                                            <button 
                                                type="button" 
                                                key={city.cityId}
                                                className={css.dropdownItem}
                                                onClick={() => {
                                                    setSelectedCitySlug(city.slug);
                                                    const cityDropdown = document.getElementById('cityDropdown');
                                                    if (cityDropdown) {
                                                        cityDropdown.style.display = 'none';
                                                    }
                                                }}
                                            >
                                                {city.name?.en}
                                            </button>
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </li>
                    )}
                </ul>
                {formikStepthree.touched.primaryAreas &&
                    formikStepthree.errors.primaryAreas && (
                        <p className={css.error}>
                            {formikStepthree.errors.primaryAreas}
                        </p>
                    )}
            </div>

            {/* Submit Button */}
            <div className={`${css.formRow} ${css.submitRow}`}>
                <button type="submit" className={css.submitBtn}>
                    Save and Continue
                </button>
            </div>
        </form>
    )
}
export default ProfileEssentials
