// "use client";

// import { useParams } from "next/navigation";
// import css from "./basicFormStepContainer.module.scss";
// import FormField from "../FormField";
// import { FormStep } from "@/types/form";
// import { FormikProps } from "formik";

// interface BasicFormStepContainerProps {
//   stepData: FormStep;
//   formik: FormikProps<Record<string, string | number | boolean>>;
// }

// const BasicFormStepContainer: React.FC<BasicFormStepContainerProps> = ({ stepData = {}, formik }) => {
//   const params = useParams();
//   const { lang } = params as { lang: "en" | "es" };

//   const { stepName, formFields } = stepData;

//   return (
//     <div className={`${css.basicForm} ${css.active}`}>
//       <div className={css.formHeading}>
//         <h3>{lang === "en" ? stepName?.en : stepName?.es}</h3>
//         <img src="/images/icons/icon-down-arrow.svg" alt="icon-down-arrow.svg" className={css.icon}/>
//       </div>
//       {formFields?.map((field, index) => (
//         <div className={css.formBody}>
//           <FormField key={index} data={field} formik={formik} />
//         </div>
//       ))}
//     </div>
//   );
// };

// export default BasicFormStepContainer;
