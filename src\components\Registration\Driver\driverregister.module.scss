
// @import '../../../styles/global.scss';

// .loginHeader {
//     text-align: center;
//     display: flex;
//     flex-direction: column;
//     width: 699px;
//     height: 74px;
//     margin-top: 80px;
//     left: 371px;
//     gap: 16px;

//     .mainHeading {
//         font-size: 22px;
//         font-family: Epilogue;
//         font-weight: 500;
//         font-size: 22px;
//         line-height: 140%;
//         letter-spacing: 0%;
//         vertical-align: middle;
//         color: #2a2a2a;
        
//     }

//     .subHeading {
//         font-family: Epilogue;
//         font-weight: 400;
//         font-size: 19px;
//         line-height: 140%;
//         letter-spacing: 0%;
//         text-align: center;
//         vertical-align: middle;
//         color: #707070;

//         .registerLink {
//             font-weight: bold;
//             color: #000000;
//             text-decoration: none;

//             &:hover {
//                 text-decoration: underline;
//             }
//         }
//     }
// }

// .employerInputContainer {
//     display: flex;
//     flex-direction: row;
//     justify-content: space-between;
//     width: 660px;
//     margin-top: 16px;
//     gap: 32px;
//     height: 70px;

//     .inputGroup {
//         display: flex;
//         flex-direction: column;
//         flex: 1;

//         label {
//             font-weight: 500;
//             margin-bottom: 6px;
//         }

//         input {
//             width: 314px;
//             height: 44px;
//             gap: 8px;
//             border-radius: 4px;
//             border-width: 1px;
//             padding: 10px 8px;
//             border-color: #707070;

//             &::placeholder {
//                 font-family: Epilogue;
//                 font-weight: 400;
//                 font-size: 16px;
//                 line-height: 24px;
//                 letter-spacing: 0%;
//                 vertical-align: middle;
//                 color: #707070;
//                 // optional
//             }
//         }

//         .passwordWrapper {
//             position: relative;

//             input {
//                 width: 100%;
//                 padding-right: 2.5rem; // space for the eye icon
//             }
//         }

//         .eyeIcon {
//             position: absolute;
//             top: 50%;
//             right: 0.75rem;
//             transform: translateY(-50%);
//             cursor: pointer;
//             color: #888;
//         }
//     }
// }

// .checkboxContainer {
//     display: flex;
//     margin-top: 16px;
//     width: 518px;
//     height: 24px;
// }

// .checkboxLabel {
//     display: flex;
//     gap: 8px;
//     white-space: nowrap;
//     align-items: center;
//     font-weight: 400;
//     font-size: 14px;
//     line-height: 160%;
//     letter-spacing: 0%;
//     color: #000000;
//     cursor: pointer;

//     .checkbox {
//         width: 18px;
//         height: 18px;

//         accent-color: black;
//         cursor: pointer;
//     }
// }

// .buttonWrapper {
//     display: flex;
//     flex-direction: column;
//     width: 660px;
//     height: 88px;
//     top: 466px;
//     left: 374px;
//     gap: 16px;
//     margin-top: 44px;
//     margin-bottom: 208px;

//     .submitBtn {
//         all: unset;
//         text-align: center;
//         width: 660;
//         height: 50;
//         gap: 10px;
//         border-radius: 4px;
//         padding: 12px;
//         background: #FBD758;

//         font-weight: 500;
//         font-size: 18px;
//         line-height: 120%;
//         letter-spacing: 0%;


//     }
// }

// .forgotLink {
//     font-weight: 400px;
//     font-size: 16px;
//     line-height: 140%;
//     letter-spacing: 0%;
//     text-align: right;
// }

// .button {
//     background: #FBD758;
//     width: 660;
//     height: 50;
//     top: 776px;
//     left: 392px;
//     gap: 16px;

// }

// .error {
//     color: red;
//     font-size: 12px;
//     margin-top: 2px;
// }

@import '../../../styles/global.scss';

.registrationSection {
    padding: 56px 0px 40px;
    width: 100%;

    @include for-size(big-tablet-down) {
        padding: 32px 16px;
    }

    .loginWrapper {
        background-color: $white;
        border: 1px solid #E5E5E5;
        border-radius: 16px;
        width: 100%;
        max-width: 804px;
        margin: 0 auto;
        padding: 40px 0px 52px;

        @include for-size(big-tablet-down) {
            border: none;
            border-radius: 0px;
            padding: 0px;
        }

        h1 {
            font-weight: 500;
            font-size: 24px;
            line-height: 32px;
            color: $dark;
            text-align: center;
        }

        p {
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            text-align: center;
            margin: 12px auto 0px;
            max-width: 580px;

            @include for-size(big-tablet-down) {
                margin-top: 16px;
            }
        }
    }

    .requiredFields {
        color: $black;
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        display: block;
        margin: 20px auto 0px;
        max-width: 600px;

        sup {
            color: #F91313;
            line-height: 0px;
        }
    }

    .registrationForm {
        display: flex;
        flex-wrap: wrap;
        column-gap: 24px;
        row-gap: 28px;
        max-width: 600px;
        margin: 16px auto 0px;

        @include for-size(big-tablet-down) {
            row-gap: 20px;
        }

        .formGroup {
            position: relative;

            &.col01 {
                width: 100%;

                &:has( .error) {
                    input {
                    border-color: #F91313;
                    }
                }
            }

            &.col02 {
                width: calc((100% - 24px) / 2);

                &:has( .error) {
                    input {
                    border-color: #F91313;
                    }
                }

                @include for-size(big-tablet-down) {
                  width: 100%;
                }
            }

            p {
                color: #707070;

                a {
                    color: $black;
                    font-weight: 500;
                }
            }

            .error {
                color: #F91313;
                font-size: 12px;
                line-height: 18px;
                font-weight: 400;
                position: absolute;
                left: 0px;
                top: calc(100% + 4px);

                @include for-size(tablet-phone) {
                    top: 100%;
                }
            }
        }

        label {
            color: $black;
            font-weight: 700;
            font-size: 14px;
            line-height: 22px;
            display: inline-flex;
            align-items: center;
            margin-bottom: 4px;
            width: 100%;

            span {
                font-weight: 400;
            }

            sup {
                color: $red;
                line-height: 0px;
            }

            .tooltipIcon {
                width: 22px;
                height: 22px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                position: relative;
                margin-left: 2px;
                cursor: pointer;

                &:hover {
                    .tooltip {
                        display: block;
                    }
                }
            }

            .tooltip {
                background-color: #1E1E1E;
                border-radius: 4px;
                color: $white;
                font-size: 14px;
                line-height: 24px;
                text-align: center;
                max-width: 330px;
                padding: 8px;
                width: 84vw;
                position: absolute;
                left: 50%;
                bottom: calc(100% + 10px);
                transform: translateX(-50%);
                display: none;

                &:after {
                    border-left: 10px solid transparent;
                    border-right: 10px solid transparent;
                    border-top: 10px solid #1E1E1E;
                    content: "";
                    position: absolute;
                    left: 50%;
                    bottom: -9px;
                    transform: translateX(-50%);
                }
            }
        }

        input[type='text'],
        input[type='email'],
        input[type='password'],
        input[type='tel'],
        input[type='number'],
        select,
        .dropdownToggle {
            background-color: #FFFFFF;
            border: 1px solid #707070;
            border-radius: 4px;
            width: 100%;
            height: 44px;
            color: #515B6F;
            font-weight: 400;
            font-size: 14px;
            line-height: 24px;
            text-align: left;
            padding: 10px 8px;
            outline: none !important;

            &:disabled {
                background-color: #F7F7F7;
                font-style: italic;
            }

            &::placeholder {
                color: #9D9D9D;
                font-weight: 400;
                opacity: 1;
            }
        }

        input[type='password'] {
            padding-right: 44px;
        }

        select {
            appearance: none;
            background-image: url(/images/icons/icon-down-arrow.svg);
            background-repeat: no-repeat;
            background-position: right 12px center;
        }

        .showPassword {
            border: none;
            background-color: transparent;
            cursor: pointer;
            width: 22px;
            height: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            right: 8px;
            top: 39px;
        }

        .dropdown {
            position: relative;

            .dropdownToggle {
                display: inline-flex;
                align-items: center;
                justify-content: flex-start;
                background-image: url(/images/icons/icon-down-arrow.svg);
                background-repeat: no-repeat;
                background-position: right 12px center;
            }

            .dropdownMenu {
                background-color: #FFFFFF;
                border-radius: 4px;
                -webkit-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
                -moz-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
                -ms-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
                -o-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
                box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
                max-height: 180px;
                overflow-x: hidden;
                overflow-y: auto;
                position: absolute;
                width: 100%;
                left: 0px;
                top: calc(100% + 8px);
                z-index: 1;
            }

            &::-webkit-scrollbar {
                width: 6px;
            }

            &::-webkit-scrollbar-track {
                background: #F5F5F5;
            }

            &::-webkit-scrollbar-thumb {
                background: #929292;
                border-radius: 24px;
            }
        }

        .dropdownItem {
            background-color: #FFFFFF;
            border: none;
            display: inline-flex;
            align-items: center;
            justify-content: flex-start;
            font-weight: 500;
            font-size: 15px;
            line-height: 22px;
            height: 44px;
            padding: 4px 12px;
            width: 100%;
        }

        .checkBox {
            position: relative;
            cursor: pointer;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            margin-bottom: 16px;

            &:last-child {
                margin-bottom: 0px;
            }

            input {
                position: absolute;
                opacity: 0;
                cursor: pointer;
                height: 24px;
                width: 24px;
                z-index: 3;

                &:checked {
                    ~.checkmark {
                        background-color: #555555;
                        border-color: #555555;

                        &:after {
                            display: block;
                        }
                    }
                }
            }

            .checkmark {
                height: 18px;
                width: 18px;
                background-color: #FFFFFF;
                border-radius: 4px;
                border: 2px solid #555555;
                position: absolute;
                left: 0px;
                top: 2px;

                &:after {
                    content: "";
                    position: absolute;
                    display: none;
                    left: 5px;
                    top: 0px;
                    width: 5px;
                    height: 10px;
                    border: solid $white;
                    border-width: 0 1px 1px 0;
                    -webkit-transform: rotate(45deg);
                    -ms-transform: rotate(45deg);
                    transform: rotate(45deg);
                }
            }

            p {
                font-weight: 400;
                font-size: 14px;
                line-height: 24px;
                padding-left: 24px;
                margin: 0px;
                text-align: left;

                a {
                    color: #0075F2;
                }
            }
        }

        .submitBtn {
            background-color: $secondary;
            border: none;
            border-radius: 8px;
            color: $black;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 18px;
            line-height: 24px;
            padding: 4px;
            width: 100%;
            height: 60px;

            @include for-size(big-tablet-down) {
                border-radius: 4px;
                height: 50px;
            }
        }
    }
}