import { cookies } from "next/headers";
import { redirect } from "next/navigation";

export async function checkAuthAndRole(
  lang: string,
  requiredIsCompany: boolean,
  redirectPath?: string
) {
  const defaultRedirect = `/${lang}`;
  const redirectTo = redirectPath || defaultRedirect;
  const cookieStore = await cookies();
  const token = cookieStore.get("authToken");

  if (!token?.value) {
    redirect(redirectTo);
  }
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_V1}auth/me`, {
      method: "GET",
      headers: {
        Authorization: `${token.value}`,
      },
      cache: "no-store",
    });

    if (!response.ok) {
      redirect(redirectTo);
    }
    const data = await response.json();
    if (!data || !data.status) {
      redirect(redirectTo);
    }
    const user = data?.data?.user;
    if (!user || user.isCompany !== requiredIsCompany) {
      redirect(redirectTo);
    }
    if (!requiredIsCompany && !user.isPhoneNumberVerified) {
      redirect(`/${lang}/register/driver`);
    }
    return user;
  } catch (error) {
    console.error("Error fetching user data:", error);
    redirect(redirectTo);
  }
}
