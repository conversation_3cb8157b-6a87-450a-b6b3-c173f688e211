// "use client";
// import React, { useEffect, useState } from "react";
// import { useFormik } from "formik";
// import { useRouter } from "next/navigation";
// import { verifyOtp, resendOtp } from "@/services/verificationService";
// import { submitDriverDetails } from "@/services/driverFormService";
// import { toast } from "react-toastify";
// import * as Yup from "yup";
// import css from './verificationScreen.module.scss';
// import Link from "next/link";

// interface Props {
//   userId: string;
//   phone: string;
// }

// const VerificationScreen: React.FC<Props> = ({ userId, phone }) => {
//   const [isEditingPhone, setIsEditingPhone] = useState(false);
//   const [isOtpDisabled, setIsOtpDisabled] = useState(false);
//   const [resendTimer, setResendTimer] = useState(0);

//   const router = useRouter();

//   // Timer logic
//   useEffect(() => {
//     if (resendTimer > 0) {
//       const timer = setTimeout(() => {
//         setResendTimer((prev) => prev - 1);
//       }, 1000);
//       return () => clearTimeout(timer);
//     }
//   }, [resendTimer]);

//   // Phone number change
//   const handlePhoneChangeConfirm = async () => {
//     await formik.validateForm();

//     if (formik.errors.phone) {
//       formik.setTouched({ phone: true });
//       toast.error("Please enter a valid phone number.");
//       return;
//     }

//     const payload = {
//       driver: {
//         phoneNumber: formik.values.phone,
//       },
//       currentStage: 2,
//       currentStep: 1,
//     };

//     const response = await submitDriverDetails(payload);

//     if (response) {
//       toast.success("Phone number updated. OTP sent to the new number.");
//       setIsEditingPhone(false);
//       setIsOtpDisabled(false);
//       setResendTimer(60);
//     } else {
//       toast.error("Failed to update phone number.");
//     }
//   };

//   const validationSchema = Yup.object({
//     otp: Yup.string().required("Please enter otp here"),
//     phone: Yup.string()
//       .matches(/^[0-9]{10,15}$/, "Invalid phone number")
//       .required("Phone is required"),
//   });

//   // Formik setup
//   const formik = useFormik({
//     initialValues: {
//       userId: userId || "",
//       phone: phone || "",
//       otp: "",
//     },
//     validationSchema: validationSchema,
//     enableReinitialize: true,

//     onSubmit: async (values) => {
//       if (!values.otp || values.otp.trim().length !== 6) {
//         toast.error("Please enter a valid 6-digit OTP.");
//         return;
//       }

//       const payload = {
//         userId: Number(values.userId),
//         otp: Number(values.otp.trim()),
//       };

//       try {
//         const res = await verifyOtp(payload);

//         if (res?.status) {
//           toast.success("Phone verified successfully!");
//           router.push("/profile/driver");
//         } else {
//           toast.error(res?.message || "Invalid or expired OTP");
//         }
//       } catch (e) {
//         console.error("Verification failed:", e);
//         toast.error("Something went wrong while verifying");
//       }
//     },
//   });

//   if (!userId || !phone) return null;

//   return (
//     <section className={css.registrationSection}>
//       <div className={css.loginWrapper}>
//         <h1>Verify Your Phone Number</h1>
//         <p>Enter the 6-digit verification code sent to {formik.values.phone}</p>
//         <form onSubmit={formik.handleSubmit} className={css.registrationForm}>
//           <div className={css.formGroup}>
//             <div className={css.col6}>
//                 <div className={css.labelDiv}>
//                   <label>Verification Code</label>
//                 </div>
//                 <input
//                   type="text"
//                   name="otp"
//                   maxLength={6}
//                   inputMode="numeric"
//                   pattern="[0-9]*"
//                   value={formik.values.otp}
//                   onChange={formik.handleChange}
//                   disabled={isOtpDisabled}
//                 />
//                    {formik.touched.otp && formik.errors.otp && (
//             <div style={{ fontSize: "12px", color: "red" }}>
//               {formik.errors.otp}
//             </div>
//           )}
//             </div>
//             <div className={css.col6}>
//               <div className={css.labelDiv}>
//                 <label>Phone Number</label>
//                 {!isEditingPhone ? (
//                 <button
//                 type="button"
//                 onClick={() => {
//                 setIsEditingPhone(true);
//                 setIsOtpDisabled(true);
//                 }}
//                 >
//                   <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
//                 </button>
//                 ) : (
//                 <button type="button" onClick={handlePhoneChangeConfirm}>
//                   <img src="/images/icons/icon-send.svg" alt="send" /> Change
//                 </button>
//                 )}
//               </div>
//               <input
//                 type="text"
//                 name="phone"
//                 value={formik.values.phone}
//                 disabled={!isEditingPhone}
//                 onChange={formik.handleChange}
//               />
//             </div>
//           </div>

//           {/* Submit Button */}
//           <div className={`${css.formGroup} ${css.btnGroup}`}>
//             <button type="submit" className={css.submitBtn}>Verify Account</button>
//             <p>
//               {`Did not receive a code?`}{" "}
//               <Link
//                 href="#"
//                 className={css.registerLink}
//                 style={{
//                   cursor: resendTimer > 0 ? 'default' : 'pointer',
//                   opacity: resendTimer > 0 ? 0.6 : 1,
//                   textDecoration: 'none',
//                   pointerEvents: resendTimer > 0 ? 'none' : 'auto'
//                 }}
//                 onClick={(e) => {
//                   e.preventDefault();
//                   if (resendTimer === 0) {
//                     resendOtp(Number(userId)).then((res) => {
//                       if (res?.status) {
//                         toast.success("OTP sent successfully.");
//                         setResendTimer(60);
//                       } else {
//                         toast.error(res?.message || "Failed to send OTP");
//                       }
//                     });
//                   }
//                 }}
//               >
//                 {resendTimer > 0
//                   ? `Resend in ${resendTimer}s`
//                   : "Resend"}
//               </Link>
//             </p>
//           </div>

//         </form>
//       </div>
//     </section>
//   );
// };

// export default VerificationScreen;

"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { verifyOtp, resendOtp } from "@/services/verificationService";
import { submitDriverDetails } from "@/services/driverFormService";
import { toast } from "react-toastify";
import * as Yup from "yup";
import css from "./verificationScreen.module.scss";
import Link from "next/link";

interface Props {
  userId: string;
  phone: string;
}

const VerificationScreen: React.FC<Props> = ({ userId, phone }) => {
  const [isEditingPhone, setIsEditingPhone] = useState(false);
  const [isOtpDisabled, setIsOtpDisabled] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);

  const router = useRouter();

  useEffect(() => {
    if (resendTimer > 0) {
      const timer = setTimeout(() => {
        setResendTimer((prev) => prev - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendTimer]);

  const validationSchema = Yup.object({
    otp: Yup.string().required("Please enter otp here"),
    phone: Yup.string()
      .matches(/^[0-9]{10,15}$/, "Invalid phone number")
      .required("Phone is required"),
  });

  const formik = useFormik({
    initialValues: {
      userId: userId || "",
      phone: phone || "",
      otp: "",
    },
    validationSchema: validationSchema,
    enableReinitialize: true,

    onSubmit: async (values) => {
      if (!values.otp || values.otp.trim().length !== 6) {
        toast.error("Please enter a valid 6-digit OTP.");
        return;
      }

      const payload = {
        userId: Number(values.userId),
        otp: Number(values.otp.trim()),
      };

      try {
        const res = await verifyOtp(payload);

        if (res?.status) {
          toast.success("Phone verified successfully!");
          router.push("/profile/driver");
        } else {
          toast.error(res?.message || "Invalid or expired OTP");
        }
      } catch (e) {
        console.error("Verification failed:", e);
        toast.error("Something went wrong while verifying");
      }
    },
  });

  if (!userId || !phone) return null;

  return (
    <section className={css.registrationSection}>
      <div className={css.loginWrapper}>
        <h1>Verify Your Phone Number</h1>
        <p>Enter the 6-digit verification code sent to {formik.values.phone}</p>
        <form onSubmit={formik.handleSubmit} className={css.registrationForm}>
          <div className={css.formGroup}>
            <div className={css.col6}>
              <div className={css.labelDiv}>
                <label>Verification Code</label>
              </div>
              <input
                type="text"
                name="otp"
                maxLength={6}
                inputMode="numeric"
                pattern="[0-9]*"
                value={formik.values.otp}
                onChange={formik.handleChange}
                disabled={isOtpDisabled}
              />
              {formik.touched.otp && formik.errors.otp && (
                <div style={{ fontSize: "12px", color: "red" }}>
                  {formik.errors.otp}
                </div>
              )}
            </div>
            <div className={css.col6}>
              <div className={css.labelDiv}>
                <label>Phone Number</label>
                <button
                  type="button"
                  onClick={() => {
                    setIsEditingPhone((prev) => !prev);
                    setIsOtpDisabled((prev) => !prev);
                  }}
                >
                  <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
                </button>
              </div>
              <input
                type="text"
                name="phone"
                value={formik.values.phone}
                disabled={!isEditingPhone}
                onChange={formik.handleChange}
              />
            </div>
          </div>

          <div className={`${css.formGroup} ${css.btnGroup}`}>
            <button type="submit" className={css.submitBtn}>
              Verify Account
            </button>
            <p>
              {`Did not receive a code?`}{" "}
              <Link
                href="#"
                className={css.registerLink}
                style={{
                  cursor: resendTimer > 0 ? "default" : "pointer",
                  opacity: resendTimer > 0 ? 0.6 : 1,
                  textDecoration: "none",
                  pointerEvents: resendTimer > 0 ? "none" : "auto",
                }}
                onClick={async (e) => {
                  e.preventDefault();
                  if (resendTimer > 0) return;

                  await formik.validateForm();
                  if (formik.errors.phone) {
                    formik.setTouched({ phone: true });
                    toast.error("Please enter a valid phone number.");
                    return;
                  }

                  const payload = {
                    driver: {
                      phoneNumber: formik.values.phone,
                    },
                    currentStage: 2,
                    currentStep: 1,
                  };

                  const response = await submitDriverDetails(payload);

                  if (!response) {
                    toast.error("Failed to update phone number.");
                    return;
                  }

                  const otpRes = await resendOtp(Number(userId));
                  if (otpRes?.status) {
                    toast.success("OTP sent successfully.");
                    setIsOtpDisabled(false);
                    setIsEditingPhone(false);
                    setResendTimer(60);
                  } else {
                    toast.error(otpRes?.message || "Failed to send OTP");
                  }
                }}
              >
                {resendTimer > 0 ? `Resend in ${resendTimer}s` : "Resend"}
              </Link>
            </p>
          </div>
        </form>
      </div>
    </section>
  );
};

export default VerificationScreen;
