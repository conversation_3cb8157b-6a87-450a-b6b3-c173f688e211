import React from "react";
import styles from "./CheckboxField.module.scss";
import { DropdownItem } from "@/types/jobpostingform";
import { FormikProps } from "formik";
import RadioField from "../RadioField";

interface CheckboxProps<T extends Record<string, unknown>> {
  className?: string;
  label?: string;
  desc?: string;
  fieldName: string;
  hide?: boolean;
  checkboxArray: DropdownItem[];
  formik: FormikProps<T>;
  radioBtn?: boolean;
  radioLabel?: string;
  radioClassName?: string;
  radioFieldName?: string;
  radioArray?: DropdownItem[];
}

const CheckboxField = <T extends Record<string, unknown>>({
  className = "",
  label = "",
  desc = "",
  fieldName = "",
  hide = false,
  checkboxArray = [],
  formik,
  radioBtn = false,
  radioLabel,
  radioClassName = "",
  radioFieldName = "",
  radioArray = []
}: CheckboxProps<T>) => {

  const selectedValue = formik?.values[fieldName] as (string | number | boolean)[];

  const changeHandler = (value: string | number | boolean) => {
    const newArray = Array.isArray(selectedValue) ? [...selectedValue] : [];
    const index = newArray.indexOf(value);

    if(index === -1) {
      newArray.push(value);
    } else {
      newArray.splice(index, 1);
    }

    formik.setFieldValue(fieldName, newArray);
  }

  return (
    <div className={`${styles.checkboxWrapper} ${styles[className]}`}>
      {label &&
        <label htmlFor={fieldName} className={styles.labelCheckbox}>
          {label} {desc && <span className={styles.desc}>{desc}</span>}{" "}
          {!hide && <span className={styles.important}>*</span>}
        </label>
      }
      <ul className={styles.checkboxList}>
        {checkboxArray.map((list) => (
          <li key={list?.label}>
            <label className={`${styles.checkboxContainer} ${radioBtn ? styles.labelRadioWidth : ""}`}>
              <input
                type="checkbox"
                name={fieldName}
                onChange={() => changeHandler(list?.value)}
                checked={selectedValue?.includes(list?.value)}
              />
              <span className={styles.customCheckbox}></span>
              <span className={styles.checkboxLabel}>{list?.label}</span>
              {radioBtn && list.value === "radioBtn" && (
                <span className={styles.otherLabel}>
                  <RadioField
                    className={radioClassName}
                    label={radioLabel}
                    fieldName={radioFieldName}
                    formik={formik}
                    radioArray={radioArray}
                  />
                </span>
              )}
            </label>
          </li>
        ))}
      </ul>
      {formik?.touched[fieldName] && typeof formik?.errors[fieldName] === "string" && (
        <div className="error_msg fielderror">
          {formik?.errors[fieldName]}
        </div>
      )}
    </div>
  );
};

export default CheckboxField;
